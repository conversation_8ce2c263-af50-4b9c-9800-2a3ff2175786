package com.joinus.mqtt.kafka;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.joinus.mqtt.param.SendMessageParam;
import com.joinus.mqtt.service.IMqttService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.listener.ConsumerAwareListenerErrorHandler;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.UUID;

@Component
@Slf4j
@AllArgsConstructor
public class KafkaConsumer {
    @Autowired
    private IMqttService mqttService;
    @KafkaListener(topics = {"public.basic-mqtt.upper-computer.send-messages"}, containerFactory =
            "kafkaManualAckListenerContainerFactory")
    public void consumeExportTask(ConsumerRecord<?, ?> record, Acknowledgment ack) {
        log.info("kafka简单消费 Topic{} 分区{} key{} offeset{} message{}", record.topic(), record.partition(), record.key(),
                record.offset(), record.value());

        if (ObjectUtil.isEmpty(record.value())) {
            log.warn("参数不能为空 {}", JSONUtil.toJsonStr(record));
            ack.acknowledge();
        } else {
            try {
                SendMessageParam sendMessageParam = JSONUtil.toBean((String) record.value(), SendMessageParam.class);
                mqttService.SendMessage(sendMessageParam);
                ack.acknowledge();
            } catch (Exception e) {
                log.error("发送mqtt失败:"+record.value());
                e.printStackTrace();
            }
        }

    }
    @Bean
    public ConsumerAwareListenerErrorHandler consumerAwareErrorHandler() {
        return (message, exception, consumer) -> {
            log.error("kafka消费异常:{}", message.getPayload());
            return null;
        };
    }
}
