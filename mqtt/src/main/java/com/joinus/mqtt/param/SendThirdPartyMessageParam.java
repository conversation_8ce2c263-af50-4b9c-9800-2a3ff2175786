package com.joinus.mqtt.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.List;

@Schema(name = "mqtt发送消息参数")
@NoArgsConstructor
@AllArgsConstructor
@Data
public class SendThirdPartyMessageParam {

    @Schema(title = "mqttTopic")
    @NotNull(message = "mqttTopic不能为空")
    private String mqttTopic;

    @Schema(title = "message")
    @NotNull(message = "message不能为空")
    private String message;



}
