package com.joinus.mqtt.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import javax.validation.constraints.NotNull;
import java.util.List;

@Schema(name = "mqtt发送消息参数")
@NoArgsConstructor
@AllArgsConstructor
@Data
public class SendMessageParam {

    @Schema(title = "mqttTopics")
    @NotNull(message = "mqttTopics不能为空")
    private List<String> mqttTopics;

    @Schema(title = "message")
    @NotNull(message = "message不能为空")
    private String message;


}
