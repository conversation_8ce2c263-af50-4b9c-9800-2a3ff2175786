package com.joinus.mqtt.service.impl;

import com.joinus.mqtt.mqtt.producer.MqttProducer;
import com.joinus.mqtt.param.SendMessageParam;
import com.joinus.mqtt.param.SendThirdPartyMessageParam;
import com.joinus.mqtt.service.IMqttService;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.paho.client.mqttv3.MqttClient;
import org.eclipse.paho.client.mqttv3.MqttConnectOptions;
import org.eclipse.paho.client.mqttv3.MqttException;
import org.eclipse.paho.client.mqttv3.MqttMessage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class MqttServiceImpl implements IMqttService {
    @Autowired
    private MqttProducer mqttProducer;
    @Value("${mqtt_host:************}")
    private String  mqttHost;

    @Value("${mqtt_user-name:ijx_mqtt}")
    private String  mqttUserName;
    @Value("${mqtt_user-password:Ijx967111}")
    private String  mqttUserPassword;

    @Value("${mqtt_port:1883}")
    private int  mqttPort;
    @Override
    public void SendMessage(SendMessageParam sendMessageParam) {
        sendMessageParam.getMqttTopics().forEach(mqttTopic -> mqttProducer.sendMessage(mqttTopic, sendMessageParam.getMessage()));
    }

    @Override
    public void sendThirdPartyMessage(SendThirdPartyMessageParam sendMessageParam) {
        int qos = 1;
        String broker = String.format("tcp://%s:%s", mqttHost, mqttPort);// 修改为你的 MQTT 代理地址
        String clientId = MqttClient.generateClientId();
        try {
            MqttClient sampleClient = new MqttClient(broker, clientId);
            MqttConnectOptions connOpts = new MqttConnectOptions();
            connOpts.setCleanSession(true);
            connOpts.setUserName(mqttUserName);
            connOpts.setPassword(mqttUserPassword.toCharArray());
            sampleClient.connect(connOpts);
            MqttMessage message = new MqttMessage(sendMessageParam.getMessage().getBytes());
            message.setQos(qos);
            sampleClient.publish(sendMessageParam.getMqttTopic(), message);
            sampleClient.disconnect();
            log.info("发送消息成功:{}", sendMessageParam);
        } catch (MqttException e) {
            log.warn("发送消息出错:{}", sendMessageParam, e);
        }
    }
}
