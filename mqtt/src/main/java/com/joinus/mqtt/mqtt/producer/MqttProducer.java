package com.joinus.mqtt.mqtt.producer;

import cn.hutool.core.util.StrUtil;
import com.alibaba.mqtt.server.ServerProducer;
import com.alibaba.mqtt.server.common.SendResult;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.concurrent.TimeoutException;

@Component()
public class MqttProducer {
    @Autowired
    private  ServerProducer serverProducer;
    private static final Log logger = LogFactory.getLog(MqttProducer.class);
    /**
     * sendMessage
     *
     * @param topic   队列名
     * @param message 消息体
     * @return void
     * <AUTHOR>
     * @date 2023-12-12 14:01:20
     */
    public void sendMessage(String topic, String message) {
        try {
            serverProducer.start();
            SendResult sendResult = serverProducer.sendMessage(topic, message.getBytes(StandardCharsets.UTF_8));
            logger.info(StrUtil.format("send mqtt message. topic {} msg {} msgId {}", topic, message,sendResult.getMsgId()));
        } catch (IOException | TimeoutException e) {
            logger.error(StrUtil.format("send mqtt failed! topic {} msg {}", topic, message));
            e.printStackTrace();
        }
    }
}
