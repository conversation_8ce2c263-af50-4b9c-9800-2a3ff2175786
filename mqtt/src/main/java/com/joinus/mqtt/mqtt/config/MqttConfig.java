package com.joinus.mqtt.mqtt.config;

import com.alibaba.mqtt.server.ServerConsumer;
import com.alibaba.mqtt.server.ServerProducer;
import com.alibaba.mqtt.server.config.ConsumerConfig;
import com.alibaba.mqtt.server.config.ProducerConfig;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import com.alibaba.mqtt.server.config.ChannelConfig;

@Configuration
public class MqttConfig {
    @Value("${mqtt.domain:post-cn-2r42fzjnu01-server-internet.mqtt.aliyuncs.com}")
    private String domain;

    @Value("${mqtt.port:5672}")
    private int port;

    @Value("${mqtt.instanceId:post-cn-2r42fzjnu01}")
    private String instanceId;

    @Value("${mqtt.accessKey:LTAI5tGE62vcDTeCbYUMw4LT}")
    private String accessKey;

    @Value("${mqtt.secretKey:******************************}")
    private String secretKey;
    @Bean
    public ChannelConfig channelConfig() {
        ChannelConfig channelConfig = new ChannelConfig();
        channelConfig.setDomain(domain);
        channelConfig.setPort(port);
        channelConfig.setInstanceId(instanceId);
        channelConfig.setAccessKey(accessKey);
        channelConfig.setSecretKey(secretKey);
        return channelConfig;
    }

    @Bean
    public ProducerConfig producerConfig() {
        return new ProducerConfig();
    }

    @Bean
    public ConsumerConfig consumerConfig() {
        return new ConsumerConfig();
    }

    @Bean
    public ServerProducer serverProducer() {
        return new ServerProducer(channelConfig(), producerConfig());
    }
    @Bean
    public ServerConsumer serverConsumer() {
        return new ServerConsumer (channelConfig(), consumerConfig());
    }
}
