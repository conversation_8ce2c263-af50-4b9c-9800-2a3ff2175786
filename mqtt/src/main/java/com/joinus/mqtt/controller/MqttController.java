package com.joinus.mqtt.controller;

import cn.hutool.json.JSONUtil;
import com.joinus.common.model.response.ApiResult;
import com.joinus.common.model.response.OperationResponse;
import com.joinus.mqtt.mqtt.consumer.MqttConsumer;
import com.joinus.mqtt.param.SendMessageParam;
import com.joinus.mqtt.param.SendThirdPartyMessageParam;
import com.joinus.mqtt.service.IMqttService;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR> mwh
 * @create 2023-12-12 14:04:05
 */

@RestController
@Validated
@RequestMapping("/api/snake-mqtt")
public class MqttController {
    private static final Logger log = LoggerFactory.getLogger(MqttController.class);
    @Autowired
    private IMqttService mqttService;
    @GetMapping(value = "/send-message")
    @ResponseBody
    public ApiResult<OperationResponse> sendMessage(@RequestBody  SendMessageParam sendMessageParam) {
        mqttService.SendMessage(sendMessageParam);
        return ApiResult.success();
    }

    @PostMapping(value = "/third-party/send-message")
    @ResponseBody
    public ApiResult<OperationResponse> sendThirdPartyMessage(@RequestBody SendThirdPartyMessageParam sendThirdPartyMessageParam) {
        mqttService.sendThirdPartyMessage(sendThirdPartyMessageParam);
        return ApiResult.success();
    }

}
