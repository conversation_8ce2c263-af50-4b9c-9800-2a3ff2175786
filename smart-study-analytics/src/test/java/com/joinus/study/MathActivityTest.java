package com.joinus.study;

import com.alibaba.fastjson2.JSONObject;
import com.joinus.common.model.response.ApiResult;
import com.joinus.study.model.entity.MathActivity;
import com.joinus.study.model.enums.MathMemberLevelEnum;
import com.joinus.study.model.param.IpayPayBackParam;
import com.joinus.study.model.param.IpayPayRequest;
import com.joinus.study.model.param.MathActivityJoinusParam;
import com.joinus.study.model.vo.MathActivityStudentStudyRecord;
import com.joinus.study.service.MathActivityService;
import com.joinus.study.service.MathActivityStudentService;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/6/16 15:04
 **/
@SpringBootTest(classes = StudyApplication.class)
public class MathActivityTest {

    @Resource
    private MathActivityService mathActivityService;
    @Resource
    private MathActivityStudentService mathActivityStudentService;

    @Test
    public ApiResult<MathActivity> activityDetail() {
        List<MathActivity> list = mathActivityService.list();
        return ApiResult.success(list.get(0));
    }

    @Test
    public void joinActivity() {
        String str ="{\n" +
                "  \"activityId\": 1,\n" +
                "  \"grade\": 7,\n" +
                "  \"publisher\": \"BEI_SHI_DA\",\n" +
                "  \"studentId\": 3876551\n" +
                "}";
        MathActivityJoinusParam mathActivityJoinusParam = JSONObject.parseObject(str, MathActivityJoinusParam.class);
        Long activityStudentId = mathActivityStudentService.studentJoinActivity(mathActivityJoinusParam);
        ApiResult<Long> result = ApiResult.success(activityStudentId);
        System.out.println(JSONObject.toJSONString(result));
    }

    @Test
    public void activityPayMethod() {
        Long schoolId =  1L;
        List<Map<String, String>> methodList= new ArrayList<>();
        Map<String, String> payMethodMap = new HashMap<>();
        payMethodMap.put("payMethod", "304");
        payMethodMap.put("name", "微信APP");
        methodList.add(payMethodMap);
        Map<String, String> payMethodMap2 = new HashMap<>();
        payMethodMap2.put("payMethod", "300");
        payMethodMap2.put("name", "支付宝");
        methodList.add(payMethodMap2);
        ApiResult<List<Map<String, String>>> result = ApiResult.success(methodList);
        System.out.println(JSONObject.toJSONString(result));
    }

    @Test
    public void pay() {
        IpayPayRequest param = new IpayPayRequest();
        String payStr = mathActivityStudentService.activityPay(param);
        ApiResult<String> result = ApiResult.success(payStr);
        System.out.println(JSONObject.toJSONString(result));
    }
    @Test
    public void payBack() {
        /*MathActivityJoinusParam joinusParam = new MathActivityJoinusParam();
        joinusParam.setStudentId(3854963L);
        joinusParam.setActivityId(4L);
        joinusParam.setGrade(8);
        joinusParam.setPublisher("BEI_SHI_DA");
        Long aLong = mathActivityStudentService.studentJoinActivity(joinusParam);
        System.err.println("**************************" + aLong);*/
//        mathActivityStudentService.updateMembershipLevel(3854963l, MathMemberLevelEnum.GIFT);
    }

    @Test
    public void getActivityStudentStudyRecord() {
        MathActivityStudentStudyRecord studentStudyRecord = mathActivityStudentService.getActivityStudentStudyRecord(1L);
        System.err.println("***********************************");
        System.out.println(JSONObject.toJSONString(studentStudyRecord));

    }
}
