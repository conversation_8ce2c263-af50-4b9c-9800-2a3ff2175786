package com.joinus.study.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.joinus.study.mapper.MathActivityStudentMapper;
import com.joinus.study.model.entity.MathActivity;
import com.joinus.study.model.entity.MathActivityStudent;
import com.joinus.study.model.enums.MathMemberLevelEnum;
import com.joinus.study.service.MathActivityService;
import com.joinus.study.service.MembershipService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class MathActivityStudentServiceImplTest {

    private static final Logger log = LoggerFactory.getLogger(MathActivityStudentServiceImplTest.class);

    @Mock
    private MathActivityService mathActivityService;

    @Mock
    private MembershipService membershipService;

    @Mock
    private MathActivityStudentMapper baseMapper;

    @InjectMocks
    private MathActivityStudentServiceImpl mathActivityStudentService;

    private MathActivity testActivity;
    private MathActivityStudent paidMemberStudent;
    private MathActivityStudent giftMemberStudent;
    private MathActivityStudent nonMemberStudent;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        testActivity = new MathActivity();
        testActivity.setId(1L);
        testActivity.setName("暑期数学训练营");

        paidMemberStudent = new MathActivityStudent();
        paidMemberStudent.setId(101L);
        paidMemberStudent.setStudentId(1001L);
        paidMemberStudent.setMembershipLevel(MathMemberLevelEnum.PAID);

        giftMemberStudent = new MathActivityStudent();
        giftMemberStudent.setId(102L);
        giftMemberStudent.setStudentId(1002L);
        giftMemberStudent.setMembershipLevel(MathMemberLevelEnum.GIFT);

        nonMemberStudent = new MathActivityStudent();
        nonMemberStudent.setId(103L);
        nonMemberStudent.setStudentId(1003L);
        nonMemberStudent.setMembershipLevel(MathMemberLevelEnum.NONE);
    }

    @Test
    void autoCancleMathMemberRightsTask_NoStudents_ShouldDoNothing() {
        // 准备
        when(mathActivityService.getMathSummerHolidayActivity()).thenReturn(testActivity);
        when(baseMapper.selectList(any())).thenReturn(CollUtil.newArrayList());

        // 执行
        mathActivityStudentService.autoCancleMathMemberRightsTask();

        // 验证
        verify(membershipService, never()).queryStudentMathMembership(anyLong(), null);
        log.info("测试通过: 当没有学生时，不执行任何操作");
    }

    @Test
    void autoCancleMathMemberRightsTask_WithActiveMembers_ShouldNotCancel() {
        // 准备
        List<MathActivityStudent> studentList = Arrays.asList(paidMemberStudent, giftMemberStudent);
        when(mathActivityService.getMathSummerHolidayActivity()).thenReturn(testActivity);
        when(baseMapper.selectList(any())).thenReturn(studentList);
        when(membershipService.queryStudentMathMembership(1001L, null)).thenReturn(true);
        when(membershipService.queryStudentMathMembership(1002L, null)).thenReturn(true);

        // 执行
        mathActivityStudentService.autoCancleMathMemberRightsTask();

        // 验证
        verify(membershipService, times(2)).queryStudentMathMembership(anyLong(), null);
        log.info("测试通过: 当会员状态有效时，不取消会员权益");
    }

    @Test
    void autoCancleMathMemberRightsTask_WithExpiredMembers_ShouldCancel() {
        // 准备
        List<MathActivityStudent> studentList = Arrays.asList(paidMemberStudent, giftMemberStudent);
        when(mathActivityService.getMathSummerHolidayActivity()).thenReturn(testActivity);
        when(baseMapper.selectList(any())).thenReturn(studentList);
        when(membershipService.queryStudentMathMembership(1001L, null)).thenReturn(false);
        when(membershipService.queryStudentMathMembership(1002L, null)).thenReturn(false);

        // 执行
        mathActivityStudentService.autoCancleMathMemberRightsTask();

        // 验证
        verify(membershipService, times(2)).queryStudentMathMembership(anyLong(), null);
        // 注意：由于updateStudentSummerPlanStatus被注释掉了，我们只验证日志输出
        log.info("测试通过: 当会员状态过期时，记录取消会员权益的日志");
    }

    @Test
    void autoCancleMathMemberRightsTask_WithException_ShouldLogError() {
        // 准备
        List<MathActivityStudent> studentList = Arrays.asList(paidMemberStudent);
        when(mathActivityService.getMathSummerHolidayActivity()).thenReturn(testActivity);
        when(baseMapper.selectList(any())).thenReturn(studentList);
        when(membershipService.queryStudentMathMembership(1001L, null))
                .thenThrow(new RuntimeException("会员服务不可用"));

        // 执行
        mathActivityStudentService.autoCancleMathMemberRightsTask();

        // 验证
        verify(membershipService, times(1)).queryStudentMathMembership(1001L, null);
        log.info("测试通过: 当查询会员状态异常时，记录错误日志");
    }
}
