package com.joinus.study.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.joinus.study.StudyApplication;
import com.joinus.study.mapper.MathActivityStudentMapper;
import com.joinus.study.model.entity.MathActivity;
import com.joinus.study.model.entity.MathActivityStudent;
import com.joinus.study.model.enums.MathMemberLevelEnum;
import com.joinus.study.service.MathActivityService;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest(classes = StudyApplication.class)
@ActiveProfiles("test")
@Transactional // 测试后回滚数据
class MathActivityStudentServiceIntegrationTest {

    @Autowired
    private MathActivityStudentServiceImpl mathActivityStudentService;
    
    @Autowired
    private MathActivityService mathActivityService;
    
    @Autowired
    private MathActivityStudentMapper mathActivityStudentMapper;
    
    private MathActivity testActivity;
    private MathActivityStudent paidMemberStudent;
    private MathActivityStudent giftMemberStudent;
    private MathActivityStudent nonMemberStudent;

    private static final org.slf4j.Logger log = org.slf4j.LoggerFactory.getLogger(MathActivityStudentServiceIntegrationTest.class);

    @BeforeEach
    void setUp() {
        // 创建测试数据
        testActivity = new MathActivity();
        testActivity.setName("暑期数学活动");
        mathActivityService.save(testActivity);
        
        // 创建付费会员学生
        paidMemberStudent = new MathActivityStudent();
        paidMemberStudent.setActivityId(testActivity.getId());
        paidMemberStudent.setStudentId(1001L);
        paidMemberStudent.setMembershipLevel(MathMemberLevelEnum.PAID);
        mathActivityStudentMapper.insert(paidMemberStudent);
        
        // 创建赠送会员学生
        giftMemberStudent = new MathActivityStudent();
        giftMemberStudent.setActivityId(testActivity.getId());
        giftMemberStudent.setStudentId(1002L);
        giftMemberStudent.setMembershipLevel(MathMemberLevelEnum.GIFT);
        mathActivityStudentMapper.insert(giftMemberStudent);
        
        // 创建非会员学生
        nonMemberStudent = new MathActivityStudent();
        nonMemberStudent.setActivityId(testActivity.getId());
        nonMemberStudent.setStudentId(1003L);
        nonMemberStudent.setMembershipLevel(null);
        mathActivityStudentMapper.insert(nonMemberStudent);
        
        log.info("测试数据准备完成");
    }
    
    @AfterEach
    void tearDown() {
        // 清理数据（由@Transactional处理）
    }
    
    @Test
    void autoCancleMathMemberRightsTask_ShouldCancelExpiredMembers() {
        // 执行测试
        mathActivityStudentService.autoCancleMathMemberRightsTask();
        
        // 验证日志输出（这里假设方法会记录日志）
        // 可以通过MockMvc或其他方式捕获日志进行断言
        
        // 验证数据库状态
        List<MathActivityStudent> updatedStudents = mathActivityStudentMapper.selectList(
            new QueryWrapper<MathActivityStudent>()
                .eq("activity_id", testActivity.getId())
                .in("student_id", paidMemberStudent.getStudentId(), giftMemberStudent.getStudentId())
        );
        
        // 这里应该根据实际业务逻辑添加断言
        // 例如，检查会员状态是否被更新
        assertNotNull(updatedStudents);
        assertEquals(2, updatedStudents.size());
        
        // 可以添加更多具体的断言，例如检查日志是否记录了正确的信息
        // 或者检查会员状态是否被正确更新
    }
    
    @Test
    void autoCancleMathMemberRightsTask_WithNoMembers_ShouldDoNothing() {
        // 删除所有会员学生
        mathActivityStudentMapper.delete(new QueryWrapper<MathActivityStudent>()
            .eq("activity_id", testActivity.getId())
            .in("membership_level", MathMemberLevelEnum.PAID, MathMemberLevelEnum.GIFT)
        );
        
        // 执行测试
        mathActivityStudentService.autoCancleMathMemberRightsTask();
        
        // 验证没有异常抛出
        assertTrue(true);
    }
}
