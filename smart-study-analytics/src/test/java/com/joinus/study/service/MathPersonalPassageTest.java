package com.joinus.study.service;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joinus.study.StudyApplication;
import com.joinus.study.model.dto.ExamChapterQuestionDetailDto;
import com.joinus.study.model.dto.KnowledgePointQuestionTypesDto;
import com.joinus.study.model.dto.PersonalExamDto;
import com.joinus.study.model.enums.PublisherEnum;
import com.joinus.study.model.param.CreateQuestionByKnowledgeParamV2;
import com.joinus.study.model.param.IpayPayRequest;
import com.joinus.study.model.param.StudentAndTimePageParam;
import com.joinus.study.model.vo.*;
import com.joinus.study.service.impl.AiRequestService;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.UUID;


/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/3/28 18:27
 **/
@SpringBootTest(classes = StudyApplication.class)
public class MathPersonalPassageTest {
    @Resource
    private ExamAnalysisReportService examAnalysisReportService;
    @Resource
    private SpecializedTrainingService specializedTrainingService;
    @Resource
    private AiRequestService aiRequestService;
    @Resource
    private PayService payService;
    @Resource
    private PersonalExamService personalExamService;
    @Test
    public void getExamAnalysisReportPdfInfo() {
       // ExamAnalysisReportVo report = examAnalysisReportService.getExamAnalysisReport(433l);

        ExamAnalysisReportPdfVo2 shiDa = examAnalysisReportService.getExamAnalysisReportPdfInfoV2(718l);
        System.out.println(JSONObject.toJSONString(shiDa));
    }

    @Test
    public void getExamAnalysisReport() {
        String json= "{\"payMethod\":\"303\",\"studentId\":3881851,\"parentId\":2975950,\"money\":10,\"title\":\"冠希哥购买2件柠檬水\",\"schoolId\":29660,\"extraParam\":\"1094#2975950#3881851#303#1000\",\"type\":\"basePackage\",\"platformType\":\"android\",\"ip\":\"*************\",\"openid\":\"o8Mzg5EvDgHu0m0Pnu98Gi7hDR8w\",\"appid\":\"wxd8a79383a44703ae\",\"uniqueBusinessId\":\"1094\",\"notifyUrl\":\"https://wx-videophone.uat.ijiaxiao.net/api/videophone/mixue/order/callback\"}";
        IpayPayRequest ipayPayRequest = JSONObject.parseObject(json,IpayPayRequest.class);
        String invokePay = payService.invokePay(ipayPayRequest);
        System.out.println("********************");
        System.out.println(invokePay);

    }
    @Test
    public void createQuestionByKnowledgeAndQuestionTypeV3() {
        String json= "{\"knowledgePointIds\":[\"f8fa7f68-96de-4f03-8fd2-f92f6df494a9\",\"f5800eaf-42d6-4efc-90e5-55e9f281d758\",\"f7fd9467-dbb6-4ad3-a504-a91916c3d0dd\"],\"questionTypeIds\":[],\"questionCount\":1,\"enablePastExamPapers\":false}";
        CreateQuestionByKnowledgeParamV2 paramV2 = JSONObject.parseObject(json, CreateQuestionByKnowledgeParamV2.class);
        ExamChapterQuestionDetailDto questionTypeV3 = specializedTrainingService.createQuestionByKnowledgeAndQuestionTypeV3(paramV2);
        System.err.println("************************************");
        System.out.println(JSONObject.toJSONString(questionTypeV3));
    }
    @Test
    public void getMathQuestionDetailById() {
        SpecializedTrainingQuestionVo detailById = specializedTrainingService.
                getMathQuestionDetailById(UUID.fromString("5e18dee4-5477-4ea6-a470-d2acfc6c0880"), PublisherEnum.REN_JIAO);
        System.err.println("************************************");
        System.out.println(JSONObject.toJSONString(detailById));
    }

    @Test
    public void getKnowledgePointQuestionTypes() {
        StudentAndTimePageParam param = new StudentAndTimePageParam();
        param.setStudentId(3890996l);
        param.setGrade(8);
        param.setRegion("河南省.郑州市.金水区");
        param.setSchoolId(29660l);
        param.setPublisher(PublisherEnum.BEI_SHI_DA);
        Page<PersonalExamDto> examList = personalExamService.getRegionExamList(param);
        System.err.println("************************************");
        System.out.println(JSONObject.toJSONString(examList));
    }

}
