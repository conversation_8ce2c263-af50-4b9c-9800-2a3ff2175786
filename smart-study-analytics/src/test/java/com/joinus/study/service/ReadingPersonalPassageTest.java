//package com.joinus.study.service;
//
//import com.alibaba.fastjson2.JSONArray;
//import com.alibaba.fastjson2.JSONObject;
//import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
//import com.joinus.common.exception.BaseException;
//import com.joinus.study.StudyApplication;
//import com.joinus.study.mapper.*;
//import com.joinus.study.model.entity.ReadingPassages;
//import com.joinus.study.model.entity.ReadingPersonalPassages;
//import com.joinus.study.model.entity.ReadingPersonalPassagesQuestions;
//import com.joinus.study.model.entity.ReadingPersonalPrint;
//import com.joinus.study.model.enums.GenreEnum;
//import com.joinus.study.model.enums.OssEnum;
//import com.joinus.study.model.enums.PassageStatusEnum;
//import com.joinus.study.model.param.*;
//import com.joinus.study.model.vo.ReadingPersonalPassageQuestionVo;
//import com.joinus.study.model.vo.ReadingPersonalPassagesVo;
//import com.joinus.study.service.impl.AiRequestService;
//import com.joinus.study.utils.AliOssUtils;
//import org.junit.jupiter.api.Test;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.boot.test.context.SpringBootTest;
//
//import javax.annotation.Resource;
//import java.util.*;
//import java.util.stream.Collectors;
//
//
///**
// * @Description TODO
// * <AUTHOR>
// * @Date 2025/3/28 18:27
// **/
//@SpringBootTest(classes = StudyApplication.class)
//public class ReadingPersonalPassageTest {
//    @Resource
//    private ReadingPersonalPassagesQuestionMapper readingPersonalPassagesQuestionMapper;
//    @Resource
//    private ReadingUnitsMapper readingUnitsMapper;
//    @Resource
//    private ReadingPassagesMapper readingPassagesMapper;
//    @Resource
//    private ReadingPersonalPrintMapper readingPersonalPrintMapper;
//    @Resource
//    private ReadingPersonalPassagesMapper readingPersonalPassagesMapper;
//    @Resource
//    private ReadingAiAnalysisService readingAiAnalysisService;
//    @Resource
//    private AliOssUtils aliOssUtils;
//    @Resource
//    private EmailService emailService;
//    @Resource
//    private AiRequestService aiRequestService;
//    @Value("${ali.oss.endpoint}")
//    private String endpoint;
//    @Resource
//    private ReadingPersonalAnalysisReportService readingPersonalAnalysisReportService;
//    @Resource
//    private ReadingPersonalPassagesService readingPersonalPassagesService;
//    @Test
//    public void testAddition() {
//        ReadingStudentPassagesQueryParam param = new ReadingStudentPassagesQueryParam();
//        param.setPassageId(UUID.fromString("5bc13a8e-4ef1-4798-9ee9-bda04c7b7895"));
//        //param.setUnitId(UUID.fromString("995c4b7d-ffdc-4741-b241-52976faa2679"));
//        param.setStudentId(3818628l);
//        try {
//            ReadingPersonalPassagesVo passage = readingPersonalPassagesService.getAndSavePersonalPassage(param);
//            System.out.println("**********************");
//            System.out.println(  JSONObject.toJSONString(passage));
//        } catch (Exception e ){
//            e.printStackTrace();
//        }
//    }
//    @Test
//    public void pausePersonalPassagesQuestions(){
//        JSONObject object = new JSONObject();
//        object.put("passageId",UUID.fromString("5bc13a8e-4ef1-4798-9ee9-bda04c7b7895"));
//        object.put("id",6l);
//        object.put("studentId",3818727l);
//
//        List<JSONObject> questions = new ArrayList<>();
//        JSONObject obj1 = new JSONObject();
//        obj1.put("questionId",UUID.fromString("5e006df7-648d-48dc-9c35-693b886e6986"));
//        obj1.put("userAnswer","测试");
//        questions.add(obj1);
//        JSONObject obj2 = new JSONObject();
//        obj2.put("questionId",UUID.fromString("56f19c4f-60b8-4f25-afd2-524e43cab723"));
//        obj2.put("userAnswer","测试");
//        questions.add(obj2);
//
//        JSONObject obj3 = new JSONObject();
//        obj3.put("questionId",UUID.fromString("4bf5d340-711c-486b-99a3-d1fddbd980b1"));
//        obj3.put("userAnswer","测试");
//        questions.add(obj3);
//        object.put("questions",questions);
//        ReadingPersonalPassagesQuestionsParam param = JSONObject.parseObject(object.toJSONString(), ReadingPersonalPassagesQuestionsParam.class);
//        readingPersonalPassagesService.pausePersonalPassagesQuestions(param);
//    }
//
//    @Test
//    public void studentPersonalList(){
//        /*ReadingPersonalPassagesQueryParam param = new ReadingPersonalPassagesQueryParam();
//        param.setStudentId(3818727l);
//        Page<StudentReadingPersonalPassagesVo> passagesVoPage = readingPersonalPassagesService.studentPersonalList(param);
//        System.out.println(JSONObject.toJSONString(passagesVoPage));*/
//        ReadingPersonalPassagesVo passagesVo = readingPersonalPassagesService.getpersonalPassagesById(39l);
//        System.out.println("**********************");
//        System.out.println(JSONObject.toJSONString(passagesVo));
//    }
//    @Test
//    public void generatePassageHtml(){
//        try {
//            UUID passageId =null;
//            List<UUID> questionIds = null;
//            ReadingStudentPassagesQueryParam param = new ReadingStudentPassagesQueryParam();
//            /*param.setPassageId(UUID.fromString("9b3d7ba8-078e-4952-b2d9-716bc92ed9d5"));
//            param.setId(378l);*/
//            param.setStudentId(3834784l);
//            param.setUnitId(UUID.fromString("995c4b7d-ffdc-4741-b241-52976faa2679"));
//            if(param.getId() != null){
//                passageId = param.getPassageId();
//                questionIds = readingPersonalPassagesService.getQuestionIdsByPersonalPassagesId(param.getId());
//            } else {
//                ReadingPersonalPassagesVo passagesVo = readingPersonalPassagesService.getNewPersonalPassage(param);
//                passageId = passagesVo.getPassageId();
//                questionIds = passagesVo.getQuestions().stream().map(ReadingPersonalPassageQuestionVo::getQuestionId).collect(Collectors.toList());
//            }
//            System.out.println(questionIds);
//            //String html = readingPersonalPassagesService.generatePassageHtml(passageId, questionIds, 0);
//            String passagePdf = readingPersonalPassagesService.generatePassagePdf(param.getId(), passageId, questionIds, 3);
//            System.out.println(passagePdf);
//        } catch (Exception e){
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void generatePassagePdf(){
//        try {
//           /* String html = readingPersonalPassagesService.generatePassagePdf(39l, 0);
//            System.out.println(html);*/
//        } catch (Exception e){
//            e.printStackTrace();
//        }
//    }
//    @Test
//    public void recognizeQrCode(){
//        ReadingPersonalPrint print = readingPersonalPrintMapper.selectById(13l);
//        if(print != null){
//            ReadingPassages passages = readingPassagesMapper.selectById(print.getPassageId());
//            if (passages == null) {
//                throw new BaseException("无效的文章id");
//            }
//            ReadingPersonalPassagesVo passagesVo = new ReadingPersonalPassagesVo();
//            passagesVo.setPassageId(passages.getId());
//            passagesVo.setTitle(passages.getTitle());
//            passagesVo.setContent(passages.getContent());
//            passagesVo.setGenre(passages.getGenre());
//            passagesVo.setGenreName(GenreEnum.getByName(passagesVo.getGenre()));
//            List<UUID> questionIds = JSONArray.parseArray(print.getQuestionIds(), UUID.class);
//            List<ReadingPersonalPassageQuestionVo> passagesQuestions =
//                    readingPersonalPassagesQuestionMapper.selectPersonalPassageQuestionVoList(print.getPassageId(), questionIds);
//            passagesVo.setQuestions(passagesQuestions);
//            System.out.println(JSONObject.toJSONString(passagesVo));
//        }
//    }
//
//    @Test
//    public void testAiPostRequest(){
//        String url = "https://oss-edu-knowledge-hub.qingyulan.net/uat/question/2025/04/10/1744277301647.JPEG?x-oss-date=20250410T092828Z&x-oss-expires=3600&x-oss-signature-version=OSS4-HMAC-SHA256&x-oss-credential=LTAI5tH52JJv1MG3sH8eGWE2%2F20250410%2Fcn-beijing%2Foss%2Faliyun_v4_request&x-oss-signature=857d5fb965f6fa7c53334e89eb0fbca22432872ebf809494c66124b5a095d0dc";
//        String substring = url.substring(0, url.indexOf("?"));
//        System.out.println(substring);
//        System.out.println(endpoint);
//        String replaced = substring.replace(endpoint, "");
//        System.out.println("replaced : " + replaced);
//        try {
//            String key= "https://oss-edu-knowledge-hub.qingyulan.net/uat/question/2025/04/10/1744277301647.JPEG?x-oss-date=20250411T003745Z&x-oss-expires=3599&x-oss-signature-version=OSS4-HMAC-SHA256&x-oss-credential=LTAI5tH52JJv1MG3sH8eGWE2%2F20250411%2Fcn-beijing%2Foss%2Faliyun_v4_request&x-oss-signature=ee690b2b6fd057eaabdf68a189040b179625b72b86c7bb03a67b2927e767a1eb";
//            String s = aliOssUtils.generatePresignedUrl(key);
//            System.out.println(s);
//        } catch (BaseException e) {
//            e.printStackTrace();
//        }
//
//    }
//    @Test
//    public void sendSimpleMessage(){
//        MailParam mail = new MailParam();
//        mail.setFrom("<EMAIL>");
//        mail.setTo("<EMAIL>");
//        mail.setSubject("测试pdf邮件2");
//        mail.setContent("测试pdf邮件2");
//        emailService.sendOssPdf(mail,"uat/reading-training/pdf/2025/4/8/dd91f98a-e144-4118-b254-3d792b8eb5fe.pdf","dd91f98a-e144-4118-b254-3d792b8eb5fe.pdf");
//    }
//
//    @Test
//    public void sendPdfMessage(){
//        String str = "377\n" +
//                "378\n" +
//                "418\n" +
//                "417\n" +
//                "387\n" +
//                "406\n" +
//                "407\n" +
//                "413\n" +
//                "415\n" +
//                "380";
//
//        String[] split = str.split("\n");
//        for (String id : split) {
//            /*List<ReadingPersonalPassageQuestionVo> passagesQuestions
//                    = readingPersonalPassagesQuestionMapper.selectPersonalPassageQuestionVoByPassageId(Long.parseLong(id));
//            ReadingAIAbilityParam aiparam = new ReadingAIAbilityParam();
//            aiparam.setId(id);
//            List<ReadingAIAbilityItemParam> items = new ArrayList<>();
//            for (ReadingPersonalPassageQuestionVo question : passagesQuestions) {
//                ReadingAIAbilityItemParam param = new ReadingAIAbilityItemParam();
//                param.setId(question.getQuestionId().toString());
//                param.setAnswer(question.getUserAnswer());
//                items.add(param);
//            }
//            aiparam.setItems(items);
//            System.out.println("**********************");
//            System.out.println(JSONObject.toJSONString(aiparam));
//            aiRequestService.correctingAIRequest(aiparam);*/
//            readingPersonalAnalysisReportService.generateTrainingReport(Long.parseLong(id)); // 生成报告
//        }
//    }
//
//    @Test
//    public void submitPersonalPassagesQuestions(){
//        String str ="599";
//        String[] split = str.split("\n");
//        for (String id : split) {
//            try {
//                readingPersonalAnalysisReportService.generateTrainingReport(Long.parseLong(id));
//            } catch (Exception e){
//                e.printStackTrace();
//            }
//        }// 生成报告
//    }
//
//    @Test
//    public void getPassagesQuestionsListByStudentId(){
//        ReadingPersonalPassages passages =new ReadingPersonalPassages();
//        passages.setId(599l);
//        try {
//            LambdaQueryWrapper<ReadingPersonalPassagesQuestions> queryWrapper = new LambdaQueryWrapper<>();
//            queryWrapper.eq(ReadingPersonalPassagesQuestions::getPersonalPassageId, passages.getId());
//            List<ReadingPersonalPassagesQuestions> questions = readingPersonalPassagesQuestionMapper.selectList(queryWrapper);
//            boolean isCorrected = questions.stream().allMatch(question -> question.getResult() != null);
//            if (isCorrected) {
//                List<UUID> questionIds = questions.stream().map(ReadingPersonalPassagesQuestions::getQuestionId).collect(Collectors.toList());
//                List<ReadingQuestionknowledgePintVo> pointIdsByQuestionIds = readingPersonalPassagesQuestionMapper.getPointIdsByQuestionIds(questionIds);
//                String knowledgePoints = String.join(",", pointIdsByQuestionIds.stream().map(ReadingQuestionknowledgePintVo::getName).distinct().collect(Collectors.toList()));
//                System.out.println("************* "+ knowledgePoints) ;
//                passages.setKnowledgePoints(knowledgePoints); // 获取题目知识点保存薄弱点
//                passages.setUpdatedAt(new Date());
//                passages.setStatus(PassageStatusEnum.CORRECTING.getCode());
//                readingPersonalPassagesMapper.updateById(passages);
//            }
//        } catch (BaseException e) {
//            e.printStackTrace();
//        }
//    }
//    //测试错题标注实现
//    @Test
//    public void annotationWrongPersonalPassagesQuestions(){
//        ReadingPersonalPassagesQuestionsParam param = new ReadingPersonalPassagesQuestionsParam();
//        param.setId(26l);
//        param.setPassageId(UUID.fromString("8e309647-6ec1-4039-9431-74e5e3c09050"));
//        QuestionsParam param1 = new QuestionsParam();
//        param1.setQuestionId(UUID.fromString("276b49c9-d9b9-4afa-b4f0-051d960b2566"));
//        param1.setResultCode(0);
//        param.setQuestions(Arrays.asList(param1));
//        readingPersonalPassagesService.annotationWrongPersonalPassagesQuestions(param);
//
//    }
//    @Test
//    public void aliOssUtilsPdf(){
//        /*String str ="uat/reading-training/pdf/2025/4/11/dcee28ff-c099-46a7-98d2-e0137e5db066.pdf";
//        String preview = aliOssUtils.generatePdfPreview(OssEnum.ALIYUN_EDU_KNOWLEDGE_HUB.getBucket(),str);
//        System.out.println(preview);
//        String presignedUrl = aliOssUtils.generatePresignedUrl(str);
//        System.out.println(presignedUrl);*/
//        String str ="ttp:/img.967111.com/olume photo/16758461/imagefaceCache 47640b8e-2994-4ec1-8285-be80e2aabc7d.PEG";
//        System.out.println("******************* " + str.length());
//    }
//
//}
