
package com.joinus.study;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joinus.study.model.dto.ExamGroupQuestionTypeInfoDto;
import com.joinus.study.model.dto.PersonalExamDto;
import com.joinus.study.model.param.StudentAndTimePageParam;
import com.joinus.study.service.PersonalExamService;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.UUID;


/**
 * u8003u60c5u5206u6790u62a5u544au6d4bu8bd5u7c7b
 */
@SpringBootTest(classes = StudyApplication.class)
public class ExamAnalysisReportTest {
    @Resource
    private PersonalExamService personalExamService;
    @Test
    public void testGetRegionExamDetail() {
        ExamGroupQuestionTypeInfoDto examDetail = personalExamService.getRegionExamDetail(UUID.fromString("a7e1f36e-0146-403a-9eef-965e3022b9cf"), null);
        System.err.println("*******************************************");
        System.out.println(JSONObject.toJSONString(examDetail));
    }
    @Test
    public void testGetRegionExamList() {
        StudentAndTimePageParam param = new StudentAndTimePageParam();
        param.setRegion("河南省.郑州市.金水区");
        param.setSchoolId(29660l);
        param.setSchoolExamType(1);
        Page<PersonalExamDto> examList = personalExamService.getRegionExamList(param);
        System.err.println("*******************************************");
        System.out.println(JSONObject.toJSONString(examList));
    }

}

