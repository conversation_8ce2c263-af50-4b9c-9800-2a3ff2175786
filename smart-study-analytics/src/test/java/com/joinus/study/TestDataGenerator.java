/*
package com.joinus.study;

import com.joinus.study.model.enums.PersonalExamQuestionResultEnum;
import com.joinus.study.model.enums.QuestionTypeEnum;
import com.joinus.study.model.param.CreateExamAnalysisReportParam;
import com.joinus.study.model.param.CreateExamAnalysisReportParam.QuestionData;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;

*/
/**
 * 测试数据生成器
 *//*

public class TestDataGenerator {

    */
/**
     * 生成测试数据
     *//*

    public static void main(String[] args) {
        CreateExamAnalysisReportParam param = generateTestData(20);
        System.out.println("测试数据生成完成，共 " + param.getExamDataList().size() + " 条数据");
        
        // 打印生成的数据
        System.out.println("\n生成的测试数据如下：");
        System.out.println("CreateExamAnalysisReportParam param = CreateExamAnalysisReportParam.builder()\n" +
                "        .examDataList(new ArrayList<>())\n" +
                "        .build();");
        System.out.println("List<QuestionData> examDataList = param.getExamDataList();");
        
        for (QuestionData data : param.getExamDataList()) {
            System.out.println("examDataList.add(QuestionData.builder()\n" +
                    "        .questionId(" + data.getQuestionId() + "L)\n" +
                    "        .questionType(QuestionTypeEnum." + data.getQuestionType() + ")\n" +
                    "        .result(PersonalExamQuestionResultEnum." + data.getResult() + ")\n" +
                    "        .build());");
        }
    }

    */
/**
     * 生成指定数量的测试数据
     *
     * @param count 数据条数
     * @return 测试数据参数对象
     *//*

    public static CreateExamAnalysisReportParam generateTestData(int count) {
        List<QuestionData> examDataList = new ArrayList<>();
        Random random = new Random();

        // 确保每种题型都有一定数量
        int choiceCount = count / 3 + (random.nextInt(3) - 1); // 选择题数量
        int fillBlankCount = count / 3 + (random.nextInt(3) - 1); // 填空题数量
        int freeResponseCount = count - choiceCount - fillBlankCount; // 解答题数量

        // 生成选择题数据
        for (int i = 0; i < choiceCount; i++) {
            examDataList.add(QuestionData.builder()
                    .questionId((long) (i + 1))
                    .questionType(QuestionTypeEnum.MULTIPLE_CHOICE)
                    .result(random.nextDouble() < 0.6 ? PersonalExamQuestionResultEnum.correct : PersonalExamQuestionResultEnum.mistake)
                    .build());
        }

        // 生成填空题数据
        for (int i = 0; i < fillBlankCount; i++) {
            examDataList.add(QuestionData.builder()
                    .questionId((long) (choiceCount + i + 1))
                    .questionType(QuestionTypeEnum.FILL_BLANK)
                    .result(random.nextDouble() < 0.5 ? PersonalExamQuestionResultEnum.correct : PersonalExamQuestionResultEnum.mistake)
                    .build());
        }

        // 生成解答题数据
        for (int i = 0; i < freeResponseCount; i++) {
            examDataList.add(QuestionData.builder()
                    .questionId((long) (choiceCount + fillBlankCount + i + 1))
                    .questionType(QuestionTypeEnum.FREE_RESPONSE)
                    .result(random.nextDouble() < 0.4 ? PersonalExamQuestionResultEnum.correct : PersonalExamQuestionResultEnum.mistake)
                    .build());
        }

        return CreateExamAnalysisReportParam.builder()
                .examDataList(examDataList)
                .build();
    }
}
*/
