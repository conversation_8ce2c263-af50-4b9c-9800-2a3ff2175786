# 测试环境配置
spring:
  # 数据源配置
  datasource:
    dynamic:
      # 默认数据源
      primary: postgresql
      # 严格匹配数据源，未匹配到指定数据源时抛出异常
      strict: true
      datasource:
        postgresql:
          # 数据库连接配置
          driver-class-name: org.postgresql.Driver
          # 使用测试数据库，建议与生产环境隔离
          url: ***********************************************************
          username: smart_study_analytics
          password: qF9hI4bN3y
  
  # 日志级别配置
  logging:
    level:
      root: INFO
      com.joinus.study: DEBUG
      org.springframework.jdbc: DEBUG
      org.springframework.transaction: DEBUG

# MyBatis-Plus 配置
mybatis-plus:
  # 控制台打印 SQL
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  # 全局配置
  global-config:
    db-config:
      # 逻辑删除字段
      logic-delete-field: deletedAt
      # 逻辑删除值
      logic-delete-value: now()
      # 逻辑未删除值
      logic-not-delete-value: null

# 测试相关配置
test:
  # 测试数据库初始化脚本
  sql:
    init:
      mode: always
      schema-locations: classpath:db/schema-test.sql
      data-locations: classpath:db/data-test.sql

# 测试环境禁用缓存
spring.cache.type: none

# 测试环境禁用 Swagger 文档
springdoc:
  api-docs:
    enabled: false
  swagger-ui:
    enabled: false

# 测试环境禁用 Actuator 端点（按需开启）
management:
  endpoints:
    web:
      exposure:
        include: health,info
  endpoint:
    health:
      show-details: when-authorized
