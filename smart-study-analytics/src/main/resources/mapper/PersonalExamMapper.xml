<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.study.mapper.PersonalExamMapper">

    <resultMap id="BaseResultMap" type="com.joinus.study.model.entity.PersonalExam">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="student_id" property="studentId" jdbcType="BIGINT"/>
        <result column="exam_id" property="examId" jdbcType="OTHER" typeHandler="com.joinus.study.config.PostgresUUIDTypeHandler"/>
        <result column="created_at" property="createdAt" jdbcType="TIMESTAMP"/>
        <result column="updated_at" property="updatedAt" jdbcType="TIMESTAMP"/>
        <result column="deleted_at" property="deletedAt" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!--    根据试卷id获取试卷信息   -->
    <select id="getExamQuestionInfo" resultType="com.joinus.study.model.dto.ExamQuestionInfoDto">
        select me.name "examName",mq.id as "questionId" ,mq.question_type,meq.sort_no from math_exams me,
                                     math_exam_questions meq,
                                     math_questions mq
        where me.id = meq.exam_id
          and mq.id = meq.question_id
          and me.id = #{examId}::uuid
    </select>
    <select id="selectByExamIdAndStudentId" resultType="long">
        select id
        from personal_exam
        where exam_id = #{examId}::uuid
          and student_id = #{studentId}
    </select>

    <!-- 根据ID查询PersonalExam实体 -->
    <select id="selectPersonalExamById" resultMap="BaseResultMap">
        select id, student_id, exam_id, created_at, updated_at, deleted_at
        from personal_exam
        where id = #{id}
    </select>


    <select id="listWeakKnowledgePoints" resultType="com.joinus.study.model.entity.QuestionKnowledgePoint">
        select
            qkp.* from personal_exam t
               inner join personal_exam_question peq on t.id = peq.personal_exam_id
               inner join question_knowledge_point qkp on peq.question_id = qkp.question_id
        where t.id = #{personalExamId}
            and peq.result = 'mistake'
    </select>

    <select id="selectExistExamPhoto" resultType="String">
        select oss_url from math_exam_files mex,files f
        where mex.file_id = f.id and mex.exam_id = #{examId}
            and mex.type = 'HANDWRITING_REMOVED_PAPER'
        order by sort_no
    </select>
    <select id="selectExistExamPhotoByOriginalPaper" resultType="String">
        select oss_url from math_exam_files mex,files f
        where mex.file_id = f.id and mex.exam_id = #{examId}
            and mex.type = 'ORIGINAL_PAPER'
        order by sort_no
    </select>
    <select id="getQuestionInfoById" resultType="com.joinus.study.model.dto.QuestionInfoByIdDto">
        SELECT
               mq.id AS "questionId",
               mq.question_type,
               mq.content AS "questionContent",
               mqa.answer,
               mqa.content "analyzeContent"
        FROM
            math_questions mq
                LEFT JOIN math_question_answers qar ON qar.question_id = mq.id
                LEFT JOIN math_answers mqa ON mqa.id = qar.answer_id AND mqa.content IS NOT NULL
        WHERE
            mq.id = #{questionId}
        ORDER BY
            mqa.created_at DESC
        LIMIT 1
    </select>
    <select id="questionAnalyzeResult" resultType="com.joinus.study.model.dto.QuestionAnalyzeResultDto">
        SELECT
            mqa.created_at,
            mqa.id AS "answerId",
            mq.id AS "questionId",
            mq.question_type AS "questionType",
            mq.content AS "questionContent",
            mqa.content AS "analyzeContent",
            mqa.answer,
            STRING_AGG(DISTINCT mkp.knowledge_point_name, ',') AS "knowledgePointName"
        FROM
            math_questions mq
                LEFT JOIN math_question_answers qar ON qar.question_id = mq.id
                LEFT JOIN math_answers mqa ON mqa.id = qar.answer_id
                LEFT JOIN math_knowledge_point_questions qkp ON qkp.question_id = mq.id
                LEFT JOIN view_math_knowledge_points mkp ON mkp.knowledge_point_id = qkp.knowledge_point_id
                                                                <if test="publisherName != null">
                                                                and mkp.publisher=#{publisherName}
                                                                </if>
        WHERE
            mq.id = #{questionId}
        GROUP BY
            mqa.created_at,
            mqa.id,
            mq.id,
            mq.question_type,
            mq.content,
            mqa.content,
            mqa.answer
        ORDER BY
            mqa.id,
            mqa.created_at DESC
    </select>
    <select id="getQuestionInfoListByExamId" resultType="com.joinus.study.model.dto.QuestionInfoByIdDto">
        SELECT DISTINCT ON (mq.id)
            mex.sort_no,
            mq.id AS "questionId",
            mq.question_type,
            mq.content AS "questionContent",
            mqa.answer,
            mqa.content AS "analyzeContent"
        FROM   math_exam_questions mex
                inner join  math_questions mq on mq.id=mex.question_id
                LEFT JOIN math_question_answers qar ON qar.question_id = mq.id
                LEFT JOIN math_answers mqa ON mqa.id = qar.answer_id AND mqa.content IS NOT NULL
        WHERE
            mex.exam_id = #{examId}
        ORDER BY mq.id,
            mqa.created_at DESC
    </select>

    <select id="selectLatestNoAnalyzedPersonalExam" resultType="com.joinus.study.model.entity.PersonalExam">
        select pe.*
        from personal_exam pe
        where student_id = #{studentId}
          and exam_id = #{examId}
          and not exists (
            select 1 from exam_analyze_result ear where ear.personal_exam_id = pe.id
        )
        order by created_at desc
            limit 1
    </select>

</mapper>
