<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.study.mapper.MathExamsMapper">
    <select id="selectExamListByRegion" resultType="com.joinus.study.model.dto.PersonalExamDto">
        SELECT me.id as "examId",
        me.name AS "examName",
        mtgs."value" as "primaryName",
        STRING_AGG(mtg_sub.value,',') AS "examTagValue"
        FROM  math_exams me
        left join math_school_exam ms on ms.exam_id = me.id and ms.school_id = #{param.schoolId}
        left join math_exam_tags mtgs on mtgs.exam_id = me.id and mtgs.type = 'ALIAS' AND (mtgs.properties -> 'isPrimary')::boolean = true  and mtgs.deleted_at is null
        left join math_exam_tags mtg_sub on mtg_sub.exam_id = me.id and mtg_sub.type = 'ELITE_SCHOOL_EXAM_PAGERS' and mtg_sub.deleted_at is null
        WHERE me.deleted_at is null AND me.source = '常规考试卷'
          and me.state = 'DONE'
        <if test="param.grades != null and !param.grades.isEmpty()">
            AND me.grade IN
            <foreach collection="param.grades" item="grade" open="(" close=")" separator=",">
                #{grade}
            </foreach>
          </if>
        <![CDATA[ AND me.region_path <@ subpath(#{param.region}::ltree, 0, '1') ]]>
        <if test="param.schoolExamType != null and param.schoolExamType == 1">
            AND mtg_sub.exam_id is not null
            <![CDATA[ AND me.region_path <@ subpath(#{param.region}::ltree, 0, 2) ]]>
        </if>
        <if test="param.schoolExamType != null and param.schoolExamType == 2 ">
            AND mtg_sub.exam_id is not null
        </if>
        <if test="param.regions != null and !param.regions.isEmpty()">
            AND (
            <foreach collection="param.regions" item="region" separator=" OR ">
                <![CDATA[ me.region_path <@ subpath(#{region}::ltree, 0, 2) ]]>
            </foreach>
            )
        </if>
        GROUP BY me.id,me.name,mtgs."value",ms.school_id,me.region_path,me.grade
        ORDER BY
        CASE WHEN ms.school_id IS NOT NULL THEN 0 ELSE 1 END,
        <![CDATA[ CASE
            WHEN me.region_path = #{param.region}::ltree THEN 0
            WHEN me.region_path != #{param.region}::ltree AND me.region_path <@ subpath(#{param.region}::ltree, 0, 2) THEN 1
            ELSE 2 END ]]>,
        me.region_path DESC,me.grade,me.id
        LIMIT #{pageSize} OFFSET #{offset}
    </select>
    <select id="selectExamListByRegion_count" resultType="long">
        SELECT COUNT(1)
        FROM math_exams me
        WHERE me.deleted_at is null AND me.source = '常规考试卷'
        and me.state = 'DONE'
        <if test="param.grades != null and !param.grades.isEmpty()">
            AND me.grade IN
            <foreach collection="param.grades" item="grade" open="(" close=")" separator=",">
                #{grade}
            </foreach>
        </if>
        <![CDATA[ AND me.region_path <@ subpath(#{param.region}::ltree, 0, 1) ]]>
        <if test="param.schoolExamType != null and param.schoolExamType == 1">
            AND EXISTS (
            SELECT 1 FROM math_exam_tags
            WHERE exam_id = me.id
            AND type = 'ELITE_SCHOOL_EXAM_PAGERS'
            AND deleted_at IS NULL
            )
            <![CDATA[ AND me.region_path <@ subpath(#{param.region}::ltree, 0, 2) ]]>
        </if>
        <if test="param.schoolExamType != null and param.schoolExamType == 2">
            AND EXISTS (
            SELECT 1 FROM math_exam_tags
            WHERE exam_id = me.id
            AND type = 'ELITE_SCHOOL_EXAM_PAGERS'
            AND deleted_at IS NULL
            )
        </if>
        <if test="param.regions != null and !param.regions.isEmpty()">
            AND (
            <foreach collection="param.regions" item="region" separator=" OR ">
                <![CDATA[ me.region_path <@ subpath(#{region}::ltree, 0, 2) ]]>
            </foreach>
            )
        </if>
    </select>
    <select id="selectExamPhotos" resultType="java.lang.String">
        SELECT
            f.oss_url AS "ossUrl"
        FROM public.math_exams me
                 INNER JOIN public.math_exam_files mef on mef.exam_id = me.id
                 INNER JOIN public.files f on f.id = mef.file_id
        WHERE me.id =#{examId}
          and mef.type = CAST(#{examFileType.value} AS TEXT)
        GROUP BY mef.sort_no,f.oss_url
        ORDER BY mef.sort_no ASC
    </select>
    <select id="selectExamById" resultType="com.joinus.study.model.entity.MathExamsEntity">
        SELECT
        me.id,
        me.name,
        me.exam_section_descriptions
        from math_exams me
        left join math_exam_tags mtgs on mtgs.exam_id = me.id and mtgs.type = 'ALIAS'
                                             AND (mtgs.properties -> 'isPrimary')::boolean = true  and mtgs.deleted_at is null
        WHERE me.id = #{examId} and me.deleted_at is null
    </select>

    <select id="getExamName" resultType="java.lang.String">
        select case when met.value is null then me.name else met.value end as "name"
        from math_exams me
                 left join math_exam_tags met on me.id = met.exam_id and met.deleted_at is null
                                                     and met.type = 'ALIAS' and (met.properties->>'isPrimary')::boolean = true
        where me.id = #{examId}
    </select>


    <select id="listQuestionCountByIds" resultType="com.joinus.study.model.po.MathExamQuestinCountPo">
        select me.id as examId,
               count(distinct mq.id) as questionCount
        from math_exams me
                 left join math_exam_questions meq on me.id = meq.exam_id
                 left join math_questions mq on mq.id = meq.question_id and mq.deleted_at is null
        where me.id in
        <foreach item="examId" collection="examIds" separator="," open="(" close=")" index="">
            #{examId}
        </foreach>
        group by me.id
    </select>
</mapper>
