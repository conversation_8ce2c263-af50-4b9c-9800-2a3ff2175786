package com.joinus.study.service;

import com.joinus.study.model.bo.JumpUrlBo;
import com.joinus.study.model.enums.ExamSourceType;

import java.util.UUID;

public interface MathAnonService {

    JumpUrlBo queryJumpUrlForMath(String qrStr, Long studentId);

    JumpUrlBo queryJumpUrlForChiniese(String qrStr, String keyboard);

    String getJumpUrlByExamSource(ExamSourceType examSource, UUID examId);
}
