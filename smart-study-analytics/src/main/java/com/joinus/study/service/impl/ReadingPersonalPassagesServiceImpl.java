package com.joinus.study.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.http.Method;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joinus.common.BaseServiceImpl;
import com.joinus.common.exception.BaseException;
import com.joinus.common.model.response.CommonResponse;
import com.joinus.study.interceptor.util.DataUtils;
import com.joinus.study.mapper.*;
import com.joinus.study.model.CurrentUser;
import com.joinus.study.model.dto.ReadingActivityLastPracticeDto;
import com.joinus.study.model.dto.ReadingActivityWithStudentDTO;
import com.joinus.study.model.entity.*;
import com.joinus.study.model.enums.*;
import com.joinus.study.model.param.*;
import com.joinus.study.model.vo.*;
import com.joinus.study.service.*;
import com.joinus.study.util.ReadingQuestionUtil;
import com.joinus.study.util.CurrentUserHolder;
import com.joinus.study.utils.AliOssUtils;
import com.joinus.study.utils.DataUtil;
import com.joinus.study.utils.HtmlToPdfUtil;
import com.joinus.study.utils.LocalHttpUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2025/3/28 16:00
 **/
@Service
@Slf4j
public class ReadingPersonalPassagesServiceImpl extends BaseServiceImpl<ReadingPersonalPassagesMapper, ReadingPersonalPassages> implements ReadingPersonalPassagesService {

    @Resource
    private ReadingPersonalPassagesQuestionMapper readingPersonalPassagesQuestionMapper;
    @Resource
    private ReadingUnitsMapper readingUnitsMapper;
    @Resource
    private ReadingPassagesMapper readingPassagesMapper;
    @Resource
    private ReadingPassageQuestionsMapper readingPassageQuestionsMapper;
    @Resource
    private ReadingPersonalPrintMapper readingPersonalPrintMapper;
    @Resource
    private ReadingPersonalPlanMapper readingPlanMapper;
    @Resource
    private AliOssUtils aliOssUtils;
    @Resource
    private EmailService emailService;
    @Resource
    @Lazy
    private ReadingPersonalAnalysisReportService readingPersonalAnalysisReportService;
    @Resource
    @Lazy
    private ReadingPersonalPassagesService readingPersonalPassagesService;

    @Resource
    public AiRequestService requestService;
    @Resource
    private ReadingActivityMapper activityMapper;
    @Resource
    private ReadingPassageQuestionSetsService passageQuestionSetsService;
    @Resource
    private ReadingPersonalPassagesQuestionMapper personalPassagesQuestionMapper;
    @Resource
    private ReadingAiAnalysisService aiAnalysisService;
    @Resource
    private ReadingActivitySetsMapper activitySetsMapper;
    @Value("${reading-hub-host-url:https://edu-knowledge-hub.uat.qingyulan.net/api/edu-knowledge-hub}")
    private String readingHubHostUrl;
    @Value("${ijx.ink.q.ch:https://ijx.ink/q/ch/}")
    private String inkQCh;  // 二位码生成添加前缀值
    @Value("${ijx.ink.q.ch.as:https://ijx.ink/q/ch/as/}")
    private String inkQChas;
    @Value("${pdf.request.host.name:https://pdf-generator-server.uat.static.ijx.ink}")
    private String pdfRequestHostName;

    @Value("${member.benefit.print.times.setting:30}")
    private Integer memberBenefitPrintTimesSetting;

    @Value("${member.ship.units:4}")
    private Integer memberShipUnits;
    @Autowired
    private ReadingMembershipService membershipService;

    @Override
    public ReadingPersonalPassagesVo getAndSavePersonalPassage(ReadingStudentPassagesQueryParam param) throws BaseException {
        //通过ai接口获取文章
        ReadingPersonalPassagesVo passage = this.getNewPersonalPassage(param);
        if (passage == null) {
            throw new BaseException("暂无文章");
        }
        if (CollectionUtils.isEmpty(passage.getQuestions())) {
            throw new BaseException("暂无题目");
        }
        //保存练习记录
        ReadingPersonalPassages passages = new ReadingPersonalPassages();
        passages.setStudentId(param.getStudentId());
        passages.setPassageId(passage.getPassageId());
        passages.setPlanId(passage.getPlanId());
        passages.setStatus(PassageStatusEnum.PENDING.getCode());
        passages.setCreatedAt(new Date());
        List<UUID> questionIds = passage.getQuestions().stream().map(ReadingPersonalPassageQuestionVo::getQuestionId).collect(Collectors.toList());
        if(!questionIds.isEmpty()){
            List<ReadingQuestionknowledgePintVo> pointIdsByQuestionIds = this.getPointIdsByQuestionIds(questionIds);
            String knowledgePoints = String.join(",", pointIdsByQuestionIds.stream().map(ReadingQuestionknowledgePintVo::getName).distinct().collect(Collectors.toList()));
            passages.setKnowledgePoints(knowledgePoints); // 获取题目知识点保存薄弱点
        }
        baseMapper.insert(passages);
        passage.setId(passages.getId());
        int questionNo = 1;
        for (ReadingPersonalPassageQuestionVo question : passage.getQuestions()){
            ReadingPersonalPassagesQuestions questions = new ReadingPersonalPassagesQuestions();
            questions.setPersonalPassageId(passages.getId());
            questions.setQuestionId(question.getQuestionId());
            questions.setQuestionType(question.getQuestionType());
            questions.setCreatedAt(new Date());
            questions.setQuestionNo(questionNo++);
            questions.setIsBlocked(0);
            questions.setIsFeedback(0);
            readingPersonalPassagesQuestionMapper.insert(questions);
            question.setId(questions.getId());
        }
        return passage;
    }

    @Override
    public ReadingPersonalPassagesVo getNewPersonalPassage(ReadingStudentPassagesQueryParam param) throws BaseException {
        ReadingUnits readingUnits = readingUnitsMapper.selectById(param.getUnitId());
        if (readingUnits.getOrderNo() != null && readingUnits.getOrderNo() > memberShipUnits){
            membershipService.checkStudentMembership(param.getStudentId(), param.getParentId(), 5);
        }
        List<UUID> weakKnowledgePointIds = this.getStudentWeakKnowledgePointIds(param.getStudentId());
        List<UUID> doQuestionIds = this.getStudentDoQuestionIds(param.getStudentId());
        ReadingPersonalPlan plan = readingPlanMapper.getPlanInfoByStudentId(param.getStudentId());
        if(plan != null){
            if (StringUtils.isNotBlank(plan.getWeakKnowledgePointIds())){
                weakKnowledgePointIds.addAll(Arrays.asList(plan.getWeakKnowledgePointIds().split(",")).stream().map(UUID::fromString).collect(Collectors.toList()));
            }
        }
        // 获取练习文章
        JSONObject urlParam = new JSONObject();
        urlParam.put("unitId", param.getUnitId());
        urlParam.put("knowledgePoints", weakKnowledgePointIds);
        urlParam.put("questionIds", doQuestionIds);
        urlParam.put("passageId", param.getPassageId());
        String response = LocalHttpUtil.post(readingHubHostUrl + "/reading/unit/question",
                urlParam);
        JSONObject bean = JSONObject.parseObject(response, JSONObject.class);
        if (StringUtils.isBlank(bean.getString("data"))) {
            throw new BaseException("暂无文章");
        }
        ReadingPassagesVo passage = bean.getObject("data", ReadingPassagesVo.class);
        ReadingPersonalPassagesVo readingPersonalPassagesVo = new ReadingPersonalPassagesVo();
        readingPersonalPassagesVo.setPassageId(passage.getId());
        readingPersonalPassagesVo.setTitle(passage.getTitle());
        readingPersonalPassagesVo.setContent(passage.getContent());
        readingPersonalPassagesVo.setGenreName(GenreEnum.getByName(passage.getGenre()));
        readingPersonalPassagesVo.setUnitId(param.getUnitId());
        if(param.getUnitId() == null){
            readingPersonalPassagesVo.setUnitId(this.getReadingPassagesByid(passage.getId()).getUnitId());
        }
        List<ReadingPersonalPassageQuestionVo> questions = new ArrayList<>();
        passage.getQuestions().forEach(question -> {
            ReadingPersonalPassageQuestionVo questionVo = new ReadingPersonalPassageQuestionVo();
            questionVo.setQuestionId(question.getId());
            questionVo.setQuestionContent(question.getContent());
            questionVo.setQuestionType(question.getQuestionType());
            questionVo.setOptions(question.getOptions());
            questions.add(questionVo);
        });
        questions.sort(Comparator.comparing(ReadingPersonalPassageQuestionVo::getQuestionId));
        readingPersonalPassagesVo.setQuestions(questions);
        if(plan != null){
            readingPersonalPassagesVo.setPlanId(plan.getId());
        }
        return readingPersonalPassagesVo;
    }

    //获取学生薄弱点
    @Override
    public List<UUID> getStudentWeakKnowledgePointIds(Long studentId)  throws BaseException {
        ReadingPersonalKnowledgePointDataParam param = ReadingPersonalKnowledgePointDataParam.builder()
                .studentId(studentId)
                .result(0)
                .startDate(LocalDate.now().minusDays(7))
                .endDate(LocalDate.now().plusDays(1))
                .build();
        List<ReadingKnowledgePointVo> pointVos = readingPersonalAnalysisReportService.queryPersonalPassageKnowledgePointList(param);

        if (!CollectionUtils.isEmpty(pointVos)) {
            return pointVos.stream().map(ReadingKnowledgePointVo::getId).collect(Collectors.toList());
        }
        return new ArrayList<>();
    }

    //获取学生已练习过题目
    public List<UUID> getStudentDoQuestionIds(Long studentId) {
        return readingPersonalPassagesQuestionMapper.getStudentDoQuestionIds(studentId,null);
    }

    // 生成待打印ReadingPersonalPassagesVo
    public List<ReadingQuestionknowledgePintVo> getPointIdsByQuestionIds(List<UUID> questionIds) {
        return readingPersonalPassagesQuestionMapper.getPointIdsByQuestionIds(questionIds);
    }

    @Override
    public ReadingPassages getReadingPassagesByid(UUID uuid){
        return readingPassagesMapper.selectById(uuid);
    }

    /**
     * 方法描述 生成待打印ReadingPersonalPassagesVo
     * @param: 打印范围 0 全部 1 文章题目 2 答案 3 解析
     * @return: java.lang.String
     * @author: DELL
     * @date: 2025/4/10 15:16
     */
    @Override
    public String generatePassageHtml(UUID passageId, List<UUID> questionIds,Integer printRange) throws BaseException {
        try {
            ReadingPassages passages = this.getReadingPassagesByid(passageId);
            // 获取html模板
            String htmlResourcePath = "/static/reading-comprehension.html";
            InputStream htmlStream = HtmlToPdfUtil.class.getResourceAsStream(htmlResourcePath);
            if (htmlStream == null) {
                throw new BaseException("获取html模板错误" + htmlResourcePath);
            }
            String html = new String(htmlStream.readAllBytes(), "UTF-8");
            html = html.replace("PASSAGES_TITLE", passages.getTitle());
            if (printRange < 2) { // 文章题目 0 1
                String content = "";
                if(passages.getContent().contains("\n\n")){
                    String[] split = passages.getContent().split("\n\n");
                    for(String str : split){
                        String spanStr = "<p>" + str + "</p>";
                        content = content + spanStr;
                    }
                }else{
                    content = "<p>" + passages.getContent() + "</p>";
                }
                html = html.replace("PASSAGES_CONTENT", content);
            } else {
                html = html.replace("PASSAGES_CONTENT", "");
            }
            // 获取题目及答案解析
            List<ReadingPersonalPassageQuestionVo> passagesQuestionsF =
                    readingPersonalPassagesQuestionMapper.selectPersonalPassageQuestionVoList(passageId, questionIds);
            // passagesQuestionsF 根据 questionIds 排序
            List<ReadingPersonalPassageQuestionVo> passagesQuestions = new ArrayList<>();
            questionIds.forEach(questionId -> {
                for (ReadingPersonalPassageQuestionVo question : passagesQuestionsF) {
                    if (questionId.equals(question.getQuestionId())) {
                        passagesQuestions.add(question);
                        break;
                    }
                }
            });
            ReadingQuestionUtil.convertQuestionFields(passagesQuestions);
            StringBuffer questionHtml = new StringBuffer(""); // 题目
            StringBuffer answerHtml = new StringBuffer(""); // 答案
            int i = 1; // 题号
            for (ReadingPersonalPassageQuestionVo question : passagesQuestions) {
//                this.doSetOptions(question); // 格式化设置选项
                Map<String, Object> questionMap = DataUtil.processQuestionContent(question.getQuestionContent());
                question.setQuestionContent(questionMap.get("questionContent").toString());
                question.setOptions((cn.hutool.json.JSONArray)questionMap.get("options"));
                if(printRange < 2){ // 设置题目
                    questionHtml.append(String.format("<div class=\"question\">%d : %s</div>", i, question.getQuestionContent()));
                    if (CollectionUtils.isEmpty(question.getOptions())) {
                        questionHtml.append("<div class=\"question-answer\"></div>");
                    } else {
                        if(question.getQuestionType().equals(ReadingQuestionTypeEnum.CHOICE.getQuestionTypeValue())
                                || question.getQuestionType().equals(ReadingQuestionTypeEnum.CHOICE.name())){
                            for(int o=0;o<question.getOptions().size();o++){
                                cn.hutool.json.JSONObject object = question.getOptions().getJSONObject(o);
                                questionHtml.append("<li class=\"option-item\">"+String.valueOf(object.get("key")) + ":" + String.valueOf(object.get("value")) +"</li>");
                            }
                        }
                    }
                }
                if (printRange !=1 ) {
                    String answerStr = "<div class=\"answer\">%d： %s </div>\n";
                    String analysisStr = "<div class=\"answer\"> %d 解析： %s </div>";
                    if (printRange == 0 || printRange == 2) {
                        if(question.getAnswer().indexOf("✓")>-1){
                            question.setAnswer(question.getAnswer().replace("✓", "√"));
                        }
                        answerHtml.append(String.format(answerStr, i, question.getAnswer()));
                    }
                    if (printRange == 0 || printRange == 3) {
                        answerHtml.append(String.format(analysisStr, i, question.getAnswerContent()));
                    }
                }
                i++;
            }
            html = html.replace("QUESTION_CONTENT", questionHtml);
            if (StringUtils.isNotBlank(answerHtml.toString())) {
                html = html.replace("ANSWER_CONTENT", answerHtml);
            } else {
                html = html.replace("ANSWER_CONTENT", "");
            }
//            return html.replaceAll("cloze:", "______");// html 内容
            return html.replaceAll("<cloze:", "______");// html 内容
        } catch (Exception e) {
            e.printStackTrace();
            log.error("获取html内容失败", e.getMessage());
            throw new BaseException("获取html内容失败" + e.getMessage());
        }
    }

    @Override
    public Map<String, String> generatePassagePdf(ReadingPersonalPrintParam param) throws BaseException {
        UUID passageId = param.getPassageId();
        List<UUID> questionIds = param.getQuestionIds();
        Long studentId = param.getStudentId();

        Map<String, String> resultMap = new HashMap<>();
        Integer remainingPrintTimes = getRemainingPrintTimes(studentId);
        if (remainingPrintTimes <= 0) {
            CurrentUser currentUser = CurrentUserHolder.getCurrentUser();
            membershipService.checkStudentMembership(studentId, currentUser.getUserId(), 5);
        }
        try {
            ReadingPersonalPrint personalPrint = ReadingPersonalPrint.builder()
                    .passageId(passageId)
                    .questionIds(JSONObject.toJSONString(questionIds))
                    .createdAt(new Date())
                    .entryType(param.getEntryType())
                    .setsId(param.getSetsId())
                    .planId(param.getPlanId())
                    .studentId(studentId)
                    .build();
            readingPersonalPrintMapper.insert(personalPrint);
            ReadingPassages readingPassages = readingPersonalPassagesService.getReadingPassagesByid(passageId);
            if(StringUtils.isNotBlank(personalPrint.getObjectKey())){
                resultMap.put("title",readingPassages.getTitle()+ DateFormatUtils.format(new Date(),"yyyyMMdd")+".pdf");
                resultMap.put("pdfUrl",aliOssUtils.generatePresignedUrl(personalPrint.getObjectKey()));
                resultMap.put("personalPrintId", String.valueOf(personalPrint.getId()));
                return resultMap;
            }
            ReadingPassages passages = this.getReadingPassagesByid(passageId);
            JSONObject requestJObject = new JSONObject();
            requestJObject.put("id", personalPrint.getId());
            requestJObject.put("type", param.getPrintRange());
            requestJObject.put("name", passages.getTitle());
            requestJObject.put("content", passages.getContent());
            List<JSONObject> questionsJObjectList = new ArrayList<>();
            List<ReadingPersonalPassageQuestionVo> passagesQuestionsF =
                    readingPersonalPassagesQuestionMapper.selectPersonalPassageQuestionVoList(passageId, questionIds);
            // passagesQuestionsF 根据 questionIds 排序
            List<ReadingPersonalPassageQuestionVo> passagesQuestions = new ArrayList<>();
            questionIds.forEach(questionId -> {
                for (ReadingPersonalPassageQuestionVo question : passagesQuestionsF) {
                    if (questionId.equals(question.getQuestionId())) {
                        passagesQuestions.add(question);
                        break;
                    }
                }
            });
            ReadingQuestionUtil.convertQuestionFields(passagesQuestions);
            for (ReadingPersonalPassageQuestionVo question : passagesQuestions) {
                JSONObject questionJObject = new JSONObject();
                questionJObject.put("id", question.getId());
                questionJObject.put("questionId", question.getQuestionId());
                questionJObject.put("questionContent", JSONObject.parseObject(question.getQuestionContent()));
                questionJObject.put("questionType", question.getQuestionType());
                questionJObject.put("userAnswer", question.getUserAnswer());
                questionJObject.put("result", question.getResult());
                questionJObject.put("answer", JSONObject.parseObject(question.getAnswer()));
                questionJObject.put("answerContent", question.getAnswerContent());
                questionJObject.put("passageId", question.getPassageId());
                questionJObject.put("options", question.getOptions());
                questionJObject.put("optionStr", question.getOptionStr());
                questionJObject.put("knowledgePointId", question.getKnowledgePointId());
                questionJObject.put("isFeedback", question.getIsFeedback());
                questionJObject.put("isErrorCorrection", question.getIsErrorCorrection());
                questionJObject.put("multiOptions", question.getMultiOptions());
                questionJObject.put("multiOptionsStr", question.getMultiOptionsStr());
                questionJObject.put("prettyAnswer", question.getPrettyAnswer());
                questionJObject.put("prettyAnswerStr", question.getPrettyAnswerStr());
                questionsJObjectList.add(questionJObject);
            }
            requestJObject.put("questions", new JSONArray(questionsJObjectList));
            String pdfRequestUrl = pdfRequestHostName + "/pdf/generate/chinese-paper";
            JSONObject resultJObject = pdfPostRequest(pdfRequestUrl, JSONObject.toJSONString(requestJObject));
            if(DataUtils.isNotEmpty(resultJObject) && DataUtils.isNotEmpty(resultJObject.get("data"))){
                String objectKey = String.valueOf(JSONObject.parseObject(JSONObject.toJSONString(resultJObject.get("data"))).get("key"));
                personalPrint.setObjectKey(objectKey);
                readingPersonalPrintMapper.updateById(personalPrint);
                resultMap.put("title",readingPassages.getTitle()+ DateFormatUtils.format(new Date(),"yyyyMMdd")+".pdf");
                resultMap.put("pdfUrl",String.valueOf(JSONObject.parseObject(JSONObject.toJSONString(resultJObject.get("data"))).get("presignedUrl")));
                resultMap.put("personalPrintId", String.valueOf(personalPrint.getId()));
                return resultMap;
            }else {
                return null;
            }
        } catch (Exception e) {
            log.error("create pdf error", e);
            resultMap.put("title","生成失败！");
            resultMap.put("pdfUrl","create pdf error " + e.getMessage());
            return resultMap;
        }
    }

    private JSONObject pdfPostRequest(String requestUrl, String paramsJson) {
        log.info("请求接口:{},请求参数:{}", requestUrl, paramsJson);
        // 创建请求
        HttpRequest request = HttpUtil.createRequest(Method.valueOf("POST"), requestUrl);
        // 如果是POST方法，可以添加body内容
        request.body(paramsJson);
        // 发送请求
        HttpResponse response = request.execute();
        if (null != response && response.isOk()) {
            log.info("返回结果:{}", JSONObject.toJSONString(response.body()));
            return JSONObject.parseObject(response.body());
        }
        return null;
    }

    @Override
    public byte[] generatePassagePdfByte(Long id) throws BaseException, IOException {
        CommonResponse.ERROR.assertNotNull(id, "练习id不能为空！");
        byte[] existingPdf = null;
        ReadingPersonalPrint personalPrint = readingPersonalPrintMapper.selectById(id);
        if(StringUtils.isNotBlank(personalPrint.getObjectKey())){
            //文件字节流
            existingPdf = aliOssUtils.download(personalPrint.getObjectKey());
            if (existingPdf != null && existingPdf.length > 0) {
                log.info("Returning existing PDF from OSS [objectKey:{}]", personalPrint.getObjectKey());
                return existingPdf; // 直接返回已有文件字节流
            }
        }
        return null;
    }

    @Override
    public void pausePersonalPassagesQuestions(ReadingPersonalPassagesQuestionsParam param) {
        ReadingPersonalPassages passage = new ReadingPersonalPassages();
        passage.setId(param.getId());
        passage.setPauseAt(new Date());
        passage.setStatus(PassageStatusEnum.PAUSE.getCode());
        passage.setAnswerMethod(0);
        passage.setTimeSpent(param.getTimeSpent());
        baseMapper.updateById(passage);
        List<QuestionsParam> paramQuestions = param.getQuestions();
        for (QuestionsParam question : paramQuestions) {
            //保存个人试卷做题结果
            ReadingPersonalPassagesQuestions questions = new ReadingPersonalPassagesQuestions();
            questions.setPersonalPassageId(param.getId());
            questions.setQuestionId(question.getQuestionId());
            questions.setUserAnswer(question.getUserAnswer());
            this.savePassagesQuestion(questions);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ReadingQuestionSubmitResultVo submitPersonalPassagesQuestions(ReadingPersonalPassagesQuestionsParam param) {
        // 兼容v1.1.2.1扫码提交页面答题,先保存练习记录，再保存答题结果
        if (ObjectUtil.isEmpty(param.getId())) {
            Long personalPassagesId = this.annotationWrongPersonalPassagesQuestions(param);
            CommonResponse.ERROR.assertNotNull(personalPassagesId, "保存练习记录失败！");
            param.setId(personalPassagesId);
        }

        // 保存答题结果
        List<QuestionsParam> paramQuestions = param.getQuestions();
        List<ReadingAIAbilityItemParam> items = new ArrayList<>();
        ReadingAIAbilityParam paramAI = new ReadingAIAbilityParam();
        paramAI.setId(String.valueOf(param.getId()));
        paramAI.setType(1);
        paramAI.setReadingAIAbilityType(ReadingAIAbilityType.ANSWER_COMPARE.getType());
        List<ReadingAIAbilityItemParam> itemParamList = new ArrayList<>();
        boolean notBank = false; // 是否有作答
        int questionNo = 1;
        for (QuestionsParam question : paramQuestions) {
            ReadingPassageQuestions passageQuestions = readingPassageQuestionsMapper.selectById(question.getQuestionId());
            CommonResponse.ERROR.assertNotNull(passageQuestions, "数据异常！，找不到题目信息");
            // 保存题目 savePassagesQuestion
            ReadingPersonalPassagesQuestions questions = new ReadingPersonalPassagesQuestions();
            questions.setPersonalPassageId(param.getId());
            questions.setQuestionId(question.getQuestionId());
            questions.setUserAnswer(question.getUserAnswer());
            questions.setQuestionNo(questionNo);
            questionNo++;
            ReadingAIAbilityItemParam readingAIAbilityItemParam = new ReadingAIAbilityItemParam();
            readingAIAbilityItemParam.setId(question.getQuestionId().toString());
            readingAIAbilityItemParam.setAnswer(question.getUserAnswer());
            itemParamList.add(readingAIAbilityItemParam);
            //判断题型是否为选择或判断题
            if(passageQuestions.getQuestionType().equals("CHOICE") || passageQuestions.getQuestionType().equals("JUDGMENT")){
                if(StringUtils.isNotBlank(question.getUserAnswer()) ){ // 用户作答
                    items.add(readingAIAbilityItemParam);
                    notBank = true;
                } else {
                    questions.setResult(BigDecimal.ZERO); // 默认错题
                }
            }else{
                //除了选择和判断 其它题型的答案依据前端传值result 如果没有为错
                if(question.getResult() != null && question.getResult().compareTo(BigDecimal.ONE) == 0){
                    questions.setResult(BigDecimal.ONE);
                }else{
                    questions.setResult(BigDecimal.ZERO);
                }
            }
            this.savePassagesQuestion(questions);
        }

        Long reportId = null;

        ReadingPersonalPassages passage = new ReadingPersonalPassages();
        passage.setId(param.getId());
        passage.setUpdatedAt(new Date());
        passage.setEndAt(new Date());
        passage.setStatus(PassageStatusEnum.SUBMIT.getCode());
        passage.setAnswerMethod(0); // 做题方式
        passage.setTimeSpent(param.getTimeSpent());
        if(notBank){
            ReadingAIAbilityParam aiparam = new ReadingAIAbilityParam();
            aiparam.setId(String.valueOf(param.getId()));
            aiparam.setItems(items);
            baseMapper.updateById(passage);
            //ai批改改为同步
            ReadingAIAbilityParam result = requestService.correctingAIRequest(aiparam);  // 去分析
            if(ObjectUtil.isNotEmpty(result)){
                reportId = saveCorrectingResults(result,param.getEntryType());//ReadingAIAbilityParam param
            }
        } else {
            passage.setStatus(PassageStatusEnum.CORRECTING.getCode());
            baseMapper.updateById(passage);
            if(param.getEntryType() != 2){ // 非定向爆破
                reportId = readingPersonalAnalysisReportService.generateTrainingReport(param.getId()); // 生成报告
            }
        }
        // 返回结果
        ReadingQuestionSubmitResultVo resultVo = new ReadingQuestionSubmitResultVo();
        resultVo.setPersonalPassagesId(param.getId());
        resultVo.setReportId(reportId);
        resultVo.setEntryType(param.getEntryType());
        // 定向爆破
        if(param.getEntryType() == 2) {
            // 是否消除薄弱点
            resultVo.setWeakKnowledgePoints(this.getClearWeekKnowledgePointList(param.getId(),param.getStudentId()));
        }
        paramAI.setItems(itemParamList);
        aiAnalysisService.analysesAIRequest(paramAI);
        return resultVo;
    }

    @Override
    public Page<StudentReadingPersonalPassagesVo> studentPersonalList(ReadingPersonalPassagesQueryParam param) throws BaseException {
        //查询个人练习列表
        Page<StudentReadingPersonalPassagesVo> page = new Page<>(param.getCurrent(), param.getSize());
        List<StudentReadingPersonalPassagesVo> passagesVos = baseMapper.studentPersonalList(page, param);
        for (StudentReadingPersonalPassagesVo passagesVo : passagesVos) {
            if (passagesVo.getStatus() == PassageStatusEnum.CORRECTING.getCode().intValue()
                    && DataUtils.isNotEmpty(passagesVo.getAccuracyRate()) && Double.parseDouble(passagesVo.getAccuracyRate()) < 1) {
                passagesVo.setWeakPoints(readingPersonalPassagesQuestionMapper.selectKnowledgePointsByid(passagesVo.getId()));
            }
        }
        page.setRecords(passagesVos);
        return page;
    }

    @Override
    public List<ReadingUnits> getUnits(String textbook, Long grade, Integer semester, Long studentId, Long parentId) {
        CommonResponse.ERROR.assertIsTrue(grade != null && grade > 0, "年级不能为空");
        CommonResponse.ERROR.assertIsTrue(semester != null && semester > 0, "学期不能为空");
        //查询单元
        List<ReadingUnits> list = readingUnitsMapper.selectList(new LambdaQueryWrapper<ReadingUnits>()
                /*.eq(ReadingUnits::getTextbook, textbook)*/
                .eq(ReadingUnits::getGrade, grade)
                .eq(ReadingUnits::getSemester, semester)
                .isNull(ReadingUnits::getDeletedAt)
                .orderByAsc(ReadingUnits::getOrderNo)
        );
        Boolean isMember;
        if (studentId != null && parentId != null) {
            isMember = membershipService.checkStudentIsOpenMember(studentId, parentId,5);
        } else {
            isMember = false;
        }
        list.forEach(unit -> unit.setIsFree(unit.getOrderNo() == null || unit.getOrderNo() <= memberShipUnits || isMember));
        return list;
    }

    public void savePassagesQuestion(ReadingPersonalPassagesQuestions questions) {
        Long questionsId = questions.getId();
        questions.setUpdatedAt(new Date());
        if (questions.getIsBlocked() == null) {
            questions.setIsBlocked(0);
        }
        if (questionsId != null) {
            readingPersonalPassagesQuestionMapper.updateById(questions);
        }
        //查询题目
        Wrapper<ReadingPersonalPassagesQuestions> wrapper = new LambdaQueryWrapper<ReadingPersonalPassagesQuestions>()
                .eq(ReadingPersonalPassagesQuestions::getPersonalPassageId, questions.getPersonalPassageId())
                .eq(ReadingPersonalPassagesQuestions::getQuestionId, questions.getQuestionId());
        ReadingPersonalPassagesQuestions question = readingPersonalPassagesQuestionMapper.selectOne(wrapper);
        if (question == null) {
            questions.setCreatedAt(new Date());
            readingPersonalPassagesQuestionMapper.insert(questions);
        } else {
            questions.setQuestionNo(null);
            questions.setId(question.getId());
            readingPersonalPassagesQuestionMapper.updateById(questions);
        }
    }

    @Override
    public Page<ReadingErrorBookVO> pagesOfErrorBook(ReadingErrorBookPageParam pageParam) {
        Page<ReadingErrorBookVO> page = new Page<>(pageParam.getCurrent(), pageParam.getSize());
        List<ReadingErrorBookVO> list = this.baseMapper.pagesOfErrorBook(page, pageParam);
        page.setRecords(list);
        return page;
    }

    @Override
    public List<ReadingPassageQuestionsAnswersVO> queryPersonalPassagesQuestionList(Long id) {
        return baseMapper.queryPersonalPassagesQuestionList(id);
    }

    @Override
    public List<Long> listStudentsWithTrainingRecords(LocalDate date) {
        return baseMapper.listStudentsWithTrainingRecords(date);
    }

    @Override
    public void deprecatePersonalPassagesQuestions(Long id) {
        ReadingPersonalPassages passages = new ReadingPersonalPassages();
        passages.setId(id);
        passages.setIsDiscard(1);
        passages.setUpdatedAt(new Date());
        baseMapper.updateById(passages);
    }

    @Override
    public Long saveCorrectingResults(ReadingAIAbilityParam param,Integer entryType) {
        Long id = Long.parseLong(param.getId());
        ReadingPersonalPassages passages = new ReadingPersonalPassages();
        passages.setId(id);
        //baseMapper.updateById(passages);
        param.getItems().forEach(item -> {
            ReadingPersonalPassagesQuestions questions = new ReadingPersonalPassagesQuestions();
            questions.setPersonalPassageId(id);
            questions.setQuestionId(UUID.fromString(item.getId()));
            questions.setResult(item.getResult());
            readingPersonalPassagesQuestionMapper.updateResultCode(questions);
        });
        Long reportId = this.setPersonalPassagesCorrected(passages,entryType);
        return reportId;
    }

    @Override
    public Long annotationWrongPersonalPassagesQuestions(ReadingPersonalPassagesQuestionsParam param) throws BaseException {

        if (param.getStudentId() == null || param.getEntryType() == null || param.getPassageId() == null || CollectionUtils.isEmpty(param.getQuestions())) {
            throw new BaseException("参数错误");
        }

        // 校验，如果是暑期训练营，则限制每天（无论线上线下）只能作答目标题数
        if (param.getEntryType() == ReadingEntryTypeEnum.HOLIDAY_INTENSIVE_TRAINING.getCode() ||
                param.getEntryType() == ReadingEntryTypeEnum.HOLIDAY_CONSOLIDATION_EXERCISES.getCode()) {
            if (planIdIsEmpty(param.getPlanId(), param.getEntryType(), param.getStudentId())){
                param.setPlanId(null);
            }
        }

        ReadingPersonalPassages passage = new ReadingPersonalPassages();
        passage.setPassageId(param.getPassageId());
        passage.setStudentId(param.getStudentId());
        passage.setCreatedAt(new Date());
        passage.setEndAt(new Date());
        passage.setEntryType(param.getEntryType());
        passage.setStatus(PassageStatusEnum.SUBMIT.getCode());
        // 计划id，可能为空
        if(ObjectUtil.isNotEmpty(param.getPlanId())){
            passage.setPlanId(param.getPlanId());
        }
        // 套题id，可能为空
        if (ObjectUtil.isNotEmpty(param.getSetsId())){
            passage.setSetsId(param.getSetsId());
        }

        passage.setAnswerMethod(1);
        List<UUID> questionIds = param.getQuestions().stream().map(QuestionsParam::getQuestionId).collect(Collectors.toList());
        if(!questionIds.isEmpty()){
            List<ReadingQuestionknowledgePintVo> pointIdsByQuestionIds = this.getPointIdsByQuestionIds(questionIds);
            String knowledgePoints = String.join(",", pointIdsByQuestionIds.stream().map(ReadingQuestionknowledgePintVo::getName).distinct().collect(Collectors.toList()));
            passage.setKnowledgePoints(knowledgePoints); // 获取题目知识点保存薄弱点
        }
        baseMapper.insert(passage);
        List<QuestionsParam> paramQuestions = param.getQuestions();
        //paramQuestions.sort(Comparator.comparing(QuestionsParam::getQuestionId));
        int questionNo = 1;
        for (QuestionsParam question : paramQuestions) {
            //保存个人试卷做题结果
            ReadingPersonalPassagesQuestions questions = new ReadingPersonalPassagesQuestions();
            questions.setPersonalPassageId(passage.getId());
            questions.setQuestionId(question.getQuestionId());
            questions.setQuestionNo(questionNo);
            questions.setQuestionType(question.getQuestionType());
            questionNo ++;
            if(question.getResultCode() == null){
                questions.setResult(BigDecimal.ZERO);
            } else {
                questions.setResult(question.getResultCode()); // 保存拍照做题结果
            }
            this.savePassagesQuestion(questions);
        }
        //this.setPersonalPassagesCorrected(passage,param.getEntryType());
        return passage.getId();
    }

    /**
     * @description: 校验planId是否为空
     * @author: lifengxu
     * @date: 2025/6/24 14:48
     */
    private Boolean planIdIsEmpty(Long planId, Integer paramEntryType, Long studentId) {
        // planId为空无需修改
        if (planId == null) {
            return false;
        }

        // 查询活动信息
        ReadingActivityWithStudentDTO activity = activityMapper.getActivityWithStudent(planId, studentId);
        if (activity == null) {
            return true;
        } else {
            // 获取学生今日做题类型
            int entryType = this.getActivityEntryType(planId, studentId);
            if (entryType != paramEntryType) {
                return true;
            } else {
                ReadingStudentPassagesQueryParam queryParam = ReadingStudentPassagesQueryParam.builder()
                        .planId(planId)
                        .studentId(studentId)
                        .entryType(paramEntryType)
                        .build();
                if (paramEntryType == ReadingEntryTypeEnum.HOLIDAY_INTENSIVE_TRAINING.getCode()) {
                    List<UUID> currentDayDoSetsIds = personalPassagesQuestionMapper.getActivityStudentDoSetsIds(queryParam, null, LocalDate.now(),2);
                    return ObjectUtil.isNotEmpty(currentDayDoSetsIds) && currentDayDoSetsIds.size() >= activity.getDailyPassageNum();
                } else if (paramEntryType == ReadingEntryTypeEnum.HOLIDAY_CONSOLIDATION_EXERCISES.getCode()) {
                    List<Long> todayPersonalPassageIds = personalPassagesQuestionMapper.getActivityStudentDoPersonalPassageIds(queryParam, LocalDate.now(),2);
                    return ObjectUtil.isNotEmpty(todayPersonalPassageIds) && todayPersonalPassageIds.size() >= activity.getDailyPassageNum();
                }
            }
        }
        return false;
    }

    //设置题目批改完成
    private Long setPersonalPassagesCorrected(ReadingPersonalPassages passages,Integer entryType) {
        try {
            Long reportId = null;
            LambdaQueryWrapper<ReadingPersonalPassagesQuestions> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ReadingPersonalPassagesQuestions::getPersonalPassageId, passages.getId());
            List<ReadingPersonalPassagesQuestions> questions = readingPersonalPassagesQuestionMapper.selectList(queryWrapper);
            boolean isCorrected = questions.stream().allMatch(question -> question.getResult() != null);
            if (!isCorrected && passages.getAnswerMethod() != null && passages.getAnswerMethod() == 1) { // 错题标注
                questions.stream().forEach(question -> {
                    if (question.getResult() == null) {
                        question.setResult(BigDecimal.ONE);
                        question.setUpdatedAt(new Date());
                        savePassagesQuestion(question);
                    }
                });
                isCorrected = true;
            }
            if (isCorrected) {  // 全部题目完成修改
                //筛选错误题目 生成薄弱点
                passages.setUpdatedAt(new Date());
                passages.setStatus(PassageStatusEnum.CORRECTING.getCode());
                baseMapper.updateById(passages);
                if (entryType != null && entryType != 2) { // 定向爆破
                    reportId = readingPersonalAnalysisReportService.generateTrainingReport(passages.getId());
                    if(reportId == null){
                        throw new BaseException("生成报告失败");
                    }
                    return reportId;
                }
            }
            return reportId;
        } catch (BaseException e) {
            log.error("批改失败", e);
            throw new BaseException("批改失败");
        }
    }

    @Override
    public ReadingPersonalPassagesVo getpersonalPassagesById(Long id) throws BaseException {
        ReadingPersonalPassagesQueryParam param = new ReadingPersonalPassagesQueryParam();
        param.setId(id);
        List<ReadingPersonalPassagesVo> passagesVos = baseMapper.selectReadingPersonalPassagesVoList(param);
        if (CollectionUtils.isEmpty(passagesVos)) {
            throw new BaseException("无效的练习id");
        }
        ReadingPersonalPassagesVo passagesVo = passagesVos.get(0);
        passagesVo.setGenreName(GenreEnum.getByName(passagesVo.getGenre()));
        // 获取题目信息
        List<ReadingPersonalPassageQuestionVo> questionVoList = baseMapper.getPersonalPassageQuestionList(id);
        ReadingQuestionUtil.convertQuestionFields(questionVoList);
        passagesVo.setQuestions(questionVoList);
        return passagesVo;
    }

    /**
     * @description: 【废弃】
     * @author: lifengxu
     * @date: 2025/6/23 14:40
     */
    @Override
    public ReadingPersonalPassagesVo identifyQRcode(String objectKey) throws BaseException {
        try {
            String result = aliOssUtils.recognizeQrCode(objectKey);
            if (StringUtils.isNotBlank(result) && result.startsWith(inkQCh)) {
                result = result.replace(inkQCh, "");
                //return this.getPersonalPassagesByPrintId(Long.parseLong(result));
                return null;
            }
            throw new BaseException("未识别到有效二维码");
        } catch (Exception e) {
            throw new BaseException(e.getMessage());
        }
    }

    @Override
    public ReadingPassages getPassageInfoByPersonalPassagesId(Long id) {
        return baseMapper.getPassageInfoByPersonalPassagesId(id);
    }

    @Override
    public void sendPassagePdfEmail(String recipient, Long personalPrintId) throws BaseException {
        CommonResponse.ERROR.assertNotNull(personalPrintId, "练习id不能为空！");
        MailParam param = MailParam.builder()
                .from("")
                .to(recipient)
                .subject("智能学习平台试卷")
                .content("试卷打印")
                .build();
        ReadingPersonalPrint readingPersonalPrint = readingPersonalPrintMapper.selectById(personalPrintId);
        String objectKey = readingPersonalPrint.getObjectKey();
        emailService.sendOssPdf(param,objectKey,objectKey.substring(objectKey.lastIndexOf("/") + 1));
    }

    @Override
    public void deletePersonalPassage(Long id) throws BaseException {
         ReadingPersonalPassages passages = baseMapper.selectById(id);
         if(passages != null){
             /*if(passages.getStatus() == PassageStatusEnum.PENDING.getCode()){
                 baseMapper.deleteById(id);
             } else {
                 throw new BaseException("该试卷正在答题中，无法删除");
             }*/
             baseMapper.deleteById(id);
         }
    }

    @Override
    public ReadingPersonalPassagesVo getStudentPersonalVoDoing(ReadingPersonalPassagesQueryParam param) {
        param.setStatus(1);
        List<ReadingPersonalPassagesVo> passagesVos = baseMapper.selectReadingPersonalPassagesVoList(param);
        if (!CollectionUtils.isEmpty(passagesVos)){
            ReadingPersonalPassagesVo passagesVo = null;
            for (ReadingPersonalPassagesVo vo : passagesVos) {
                if(ObjectUtil.isNotEmpty(vo.getEntryType()) && vo.getEntryType() != 2){
                    passagesVo = vo;
                    break;
                }
            }
            if (ObjectUtil.isEmpty(passagesVo)) {return null;}
            passagesVo.setGenreName(GenreEnum.getByName(passagesVo.getGenre()));
            LambdaQueryWrapper<ReadingPersonalPassagesQuestions> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ReadingPersonalPassagesQuestions::getPersonalPassageId, passagesVo.getId());
            queryWrapper.isNull(ReadingPersonalPassagesQuestions::getDeletedAt);
            List<ReadingPersonalPassageQuestionVo> passagesQuestions
                    = readingPersonalPassagesQuestionMapper.selectPersonalPassageQuestionVoByPassageId(passagesVo.getId());
            passagesQuestions.forEach(questions1 -> {
                Map<String, Object> questionMap = DataUtil.processQuestionContent(questions1.getQuestionContent());
                questions1.setQuestionContent(questionMap.get("questionContent").toString());
                questions1.setOptions((cn.hutool.json.JSONArray)questionMap.get("options"));
            });
            passagesVo.setQuestions(passagesQuestions);
            return passagesVo;
        }
        return null;
    }

    @Override
    public List<UUID> getQuestionIdsByPersonalPassagesId(Long id) throws BaseException {
        LambdaQueryWrapper<ReadingPersonalPassagesQuestions> queryWrapper = new LambdaQueryWrapper<ReadingPersonalPassagesQuestions>()
                .select(ReadingPersonalPassagesQuestions::getQuestionId)
                .eq(ReadingPersonalPassagesQuestions::getPersonalPassageId, id)
                .isNull(ReadingPersonalPassagesQuestions::getDeletedAt).orderByAsc(ReadingPersonalPassagesQuestions::getQuestionNo);
        List<ReadingPersonalPassagesQuestions> passagesQuestions = readingPersonalPassagesQuestionMapper.selectList(queryWrapper);
        return passagesQuestions.stream().map(ReadingPersonalPassagesQuestions::getQuestionId).collect(Collectors.toList());
    }

    @Override
    public void updateStatusById(Long id,Integer status,String pdfUrl) {
        ReadingPersonalPassages personalPassages = new ReadingPersonalPassages();
        personalPassages.setId(id);
        personalPassages.setUpdatedAt(new Date());
        if(pdfUrl != null){
            personalPassages.setPdfUrl(pdfUrl);
        }
        if (status != null){
            personalPassages.setStatus(status);
        }
        baseMapper.updateById(personalPassages);
    }

    /**
     * @description: 扫码解析处理
     * @author: lifengxu
     * @date: 2025/6/20 9:41
     */
    @Override
    public ReadingPersonalPassagesVo getQrDataAnalysis(String qrStr, Long studentId) throws BaseException {
        CommonResponse.ERROR.assertNotNull(qrStr, "二维码无效,请重新扫码！");
        CommonResponse.ERROR.assertNotNull(studentId, "学生id不能为空");
        Long printId = null;
        if (qrStr.startsWith(inkQChas)) {
            // 活动关联题目ID
            printId = Long.parseLong(qrStr.replace(inkQChas, ""));
            CommonResponse.ERROR.assertNotNull(printId, "二维码失效,请重新扫描");
            return getPersonalPassagesByActivitySetsId(printId, studentId);
        } else if (qrStr.startsWith(inkQCh)) {
            // 练习记录ID
            printId = Long.parseLong(qrStr.replace(inkQCh, ""));
            CommonResponse.ERROR.assertNotNull(printId, "二维码失效,请重新扫描");
            return getPersonalPassagesByPrintId(printId, studentId);
        } else {
            throw new BaseException("扫描二维码错误,请检查二维码！");
        }
    }

    @Override
    public ReadingPersonalPassagesVo getPersonalPassagesByPrintId(Long printId, Long studentId) throws BaseException {
        ReadingPersonalPrint print = readingPersonalPrintMapper.selectById(printId);
        if (print != null) {
            ReadingPassages passages = readingPassagesMapper.selectById(print.getPassageId());
            if (passages == null) {
                throw new BaseException("无效的二维码");
            }
            if (print.getPlanId() != null) {
                if (print.getEntryType() == ReadingEntryTypeEnum.HOLIDAY_INTENSIVE_TRAINING.getCode() ||
                        print.getEntryType() == ReadingEntryTypeEnum.HOLIDAY_CONSOLIDATION_EXERCISES.getCode()) {
                    // 暑期强化训练或巩固练习，需要校验活动信息
                    checkActivity(print.getPlanId(), studentId);
                } else {
                    // 训练计划，需要校验学生是否参加该计划
                    ReadingPersonalPlan studentPlan = readingPlanMapper.selectById(print.getPlanId());
                    if (studentPlan == null || !studentPlan.getStudentId().equals(studentId)) {
                        print.setPlanId(null);
                    }
                }
            }

            ReadingPersonalPassagesVo passagesVo = new ReadingPersonalPassagesVo();
            passagesVo.setPassageId(passages.getId());
            passagesVo.setTitle(passages.getTitle());
            passagesVo.setContent(passages.getContent());
            passagesVo.setGenre(passages.getGenre());
            passagesVo.setGenreName(GenreEnum.getByName(passagesVo.getGenre()));
            passagesVo.setEntryType(print.getEntryType());
            passagesVo.setSetsId(print.getSetsId());
            passagesVo.setPlanId(print.getPlanId());
            List<UUID> questionIds = JSONArray.parseArray(print.getQuestionIds(), UUID.class);
            List<ReadingPersonalPassageQuestionVo> passagesQuestionsF =
                    readingPersonalPassagesQuestionMapper.getPersonalPassageQuestionVoList(print.getPassageId(), questionIds);
            // passagesQuestionsF 根据 questionIds 排序
            List<ReadingPersonalPassageQuestionVo> passagesQuestions = new ArrayList<>();
            questionIds.forEach(questionId -> {
                for (ReadingPersonalPassageQuestionVo question : passagesQuestionsF) {
                    if (questionId.equals(question.getQuestionId())) {
                        passagesQuestions.add(question);
                        break;
                    }
                }
            });
            // 题目类型名称转换
            passagesQuestions.forEach(item -> {
                item.setQuestionType(ReadingQuestionTypeEnum.getByName(item.getQuestionType()));
                if (StrUtil.isNotBlank(item.getOptionStr())) {
                    item.setOptions(JSONUtil.parseArray(item.getOptionStr()));
                }
            });
            passagesVo.setQuestions(passagesQuestions);
            return passagesVo;
        }
        return null;
    }

    /**
     * @description: 校验活动，获取活动信息
     * @author: lifengxu
     * @date: 2025/6/23 14:11
     */
    private ReadingActivityWithStudentDTO checkActivity(Long activityId, Long studentId) throws BaseException {
        ReadingActivityWithStudentDTO activity = activityMapper.getActivityWithStudent(activityId, studentId);
        CommonResponse.ERROR.assertNotNull(activity, "学生未参加活动或活动不存在");
        LocalDateTime beginTime = activity.getActivityBeginTime();
        CommonResponse.ERROR.assertNotNull(beginTime.isBefore(LocalDateTime.now()), "活动未开始");
        LocalDateTime endTime = activity.getActivityEndTime();
        CommonResponse.ERROR.assertNotNull(endTime.isAfter(LocalDateTime.now()), "活动已结束");
        return activity;
    }

    @Override
    public ReadingPersonalPassagesVo getPersonalPassagesByActivitySetsId(Long activitySetsId, Long studentId) throws BaseException {
        // 根据活动套题关联表获取套题ID和活动ID
        ReadingActivitySets activitySets = activitySetsMapper.selectById(activitySetsId);
        CommonResponse.ERROR.assertNotNull(activitySets, "无效的二维码");
        CommonResponse.ERROR.assertIsTrue(activitySets.getDeletedAt() == null, "二维码已失效");

        // 【活动】获取活动信息
        Long activityId = activitySets.getActivityId();
        CommonResponse.ERROR.assertNotNull(activityId, "无效的二维码,获取活动信息失败");
        ReadingActivityWithStudentDTO activity = checkActivity(activityId, studentId);

        // 【套题】根据套题ID获取文章和题目信息
        ReadingPersonalPassagesVo result = passageQuestionSetsService.getQuestionsBySetsId(activitySets.getSetsId());
        CommonResponse.ERROR.assertNotNull(result, "未查询到套题信息");
        /*CommonResponse.ERROR.assertIsTrue(result.getGrade().equals(activity.getGrade()), "非用户参加活动年级题目");*/

        // 【活动】假期强化训练
        result.setEntryType(ReadingEntryTypeEnum.HOLIDAY_INTENSIVE_TRAINING.getCode());
        result.setPlanId(activityId);
        return result;
    }

    @Override
    public ReadingPersonalPassagesVo doPersonaldoAgain(Long id) throws BaseException {
        ReadingPersonalPassagesVo passagesVo = this.getpersonalPassagesById(id);
        if (passagesVo == null){
            throw new BaseException("无效的练习id ");
        }
        //保存练习记录
        ReadingPersonalPassages passages = new ReadingPersonalPassages();
        passages.setStudentId(passagesVo.getStudentId());
        passages.setPassageId(passagesVo.getPassageId());
        ReadingPersonalPlan plan = readingPlanMapper.getPlanInfoByStudentId(passagesVo.getStudentId());
        if(plan !=null){
            passages.setPlanId(passagesVo.getPlanId());
        }
        passages.setStatus(PassageStatusEnum.PENDING.getCode());
        passages.setCreatedAt(new Date());
        passages.setIsDiscard(0);
        passages.setKnowledgePoints(passagesVo.getKnowledgePoints());
        passages.setEntryType(passagesVo.getEntryType());
        baseMapper.insert(passages);
        int questionNo = 1;
        for (ReadingPersonalPassageQuestionVo question : passagesVo.getQuestions()){
            ReadingPersonalPassagesQuestions questions = new ReadingPersonalPassagesQuestions();
            questions.setPersonalPassageId(passages.getId());
            questions.setQuestionId(question.getQuestionId());
            questions.setQuestionType(question.getQuestionType());
            questions.setCreatedAt(new Date());
            questions.setIsBlocked(0);
            questions.setIsFeedback(0);
            questions.setQuestionNo(questionNo++);
            readingPersonalPassagesQuestionMapper.insert(questions);
            question.setId(questions.getId());
        }
        passagesVo.setId(passages.getId());
        return passagesVo;
    }

    @Override
    public Page<ReadingWeekKnowledgePointVo> weakKnowledgePointList(ReadingStudentWeakKnowledgePointParam pageParam) throws BaseException {
        CommonResponse.ERROR.assertNotNull(pageParam.getStudentId(), "studentId不能为空");
        Page<ReadingWeekKnowledgePointVo> page = new Page<>(pageParam.getCurrent(), pageParam.getSize());

        List<ReadingWeekKnowledgePointVo> list = baseMapper.queryStudentWeekKnowledgePointList(page, pageParam.getStudentId(), null);
        page.setRecords(list);
        return page;
    }

    @Override
    public List<ReadingWeekKnowledgePointVo> getClearWeekKnowledgePointList(Long personalPassageId, Long studentId) throws BaseException {
        CommonResponse.ERROR.assertNotNull(personalPassageId, "阅读训练id不能为空");
        CommonResponse.ERROR.assertNotNull(studentId, "studentId不能为空");

        List<ReadingWeekKnowledgePointVo> oldList = baseMapper.queryStudentWeekKnowledgePointList(studentId, personalPassageId);
        log.info("clearWeekKnowledgePointList oldList:{}", oldList);
        List<ReadingWeekKnowledgePointVo> newList = baseMapper.queryStudentWeekKnowledgePointList(studentId, null);
        log.info("clearWeekKnowledgePointList newList:{}", newList);
        //创建新数据ID集合用于快速查找
        Set<UUID> newIds = newList.stream()
                .map(ReadingWeekKnowledgePointVo::getId)  // 假设有getId()方法
                .collect(Collectors.toSet());

        // 过滤出oldList中存在但newList中不存在的记录
        return oldList.stream()
                .filter(oldVo -> !newIds.contains(oldVo.getId()))
                .collect(Collectors.toList());
    }

    @Override
    public ReadingPersonalPassagesVo studentPersonalDetail(Long id) throws BaseException {
        CommonResponse.ERROR.assertNotNull(id, "阅读训练id不能为空");
        ReadingPersonalPassagesVo result = baseMapper.getPersonalPassagesById(id);
        CommonResponse.ERROR.assertNotNull(result, "阅读训练记录不存在");
        result.setGenreName(GenreEnum.getByName(result.getGenre()));
        // 校验，如果是暑期训练营，则限制每天（无论线上线下）只能作答目标题数,超出则不关联暑期活动
        if (result.getEntryType() == ReadingEntryTypeEnum.HOLIDAY_INTENSIVE_TRAINING.getCode() ||
                result.getEntryType() == ReadingEntryTypeEnum.HOLIDAY_CONSOLIDATION_EXERCISES.getCode()) {
            if (planIdIsEmpty(result.getPlanId(), result.getEntryType(), result.getStudentId())){
                baseMapper.updatePlanIdIsNullById(id);
            }
        }
        // 获取题目信息
        List<ReadingPersonalPassageQuestionVo> questionVoList = baseMapper.getPersonalPassageQuestionList(id);
        ReadingQuestionUtil.convertQuestionFields(questionVoList);
        result.setQuestions(questionVoList);
        return result;
    }

    @Override
    public BigDecimal getAccuracyRateByParam(ReadingPersonalPassageDataParam param) throws BaseException {
        return baseMapper.getAccuracyRateByParam(param);
    }

    @Override
    public ReadingReportPosterVO getReportPosterData(Long studentId) throws BaseException {
        CommonResponse.ERROR.assertNotNull(studentId, "学生id不能为空");
        LocalDate nowDate = LocalDate.now();
        //获取训练记录表数据
        ReadingPersonalPassageDataParam trainingRecordParam = ReadingPersonalPassageDataParam.builder()
                .startDate(nowDate).endDate(nowDate).studentId(studentId).build();
        Map<String, Object> trainingRecordData = readingPersonalAnalysisReportService.queryPersonalPassageData(trainingRecordParam);
        ReadingReportPosterVO reportPosterData = new ReadingReportPosterVO();
        if(trainingRecordData != null){
            reportPosterData.setPassageCount(trainingRecordData.get("passageCount") == null ? 0 : Integer.parseInt(trainingRecordData.get("passageCount").toString()));
            reportPosterData.setKnowledgePointCount(trainingRecordData.get("totalKnowledgePoints") == null ? 0 : Integer.parseInt(trainingRecordData.get("totalKnowledgePoints").toString()));
            reportPosterData.setWeekKnowledgePointCount(trainingRecordData.get("weakPointCount") == null ? 0 : Integer.parseInt(trainingRecordData.get("weakPointCount").toString()));
            // 获取爆破数
            reportPosterData.setBlastingCount(getBlastingCount(studentId, nowDate, nowDate));
        }else{
            reportPosterData.setPassageCount(0);
            reportPosterData.setKnowledgePointCount(0);
            reportPosterData.setWeekKnowledgePointCount(0);
            reportPosterData.setBlastingCount(0);
        }
        //获取坚持天数
        reportPosterData.setPersistDays(getPersistDays(studentId));
        return reportPosterData;
    }

    public Integer getBlastingCount(Long studentId, LocalDate startDate, LocalDate endDate) throws BaseException {
        Date sDate = Date.from(startDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
        Date eDate = Date.from(endDate.plusDays(1).atStartOfDay(ZoneId.systemDefault()).toInstant());
        CommonResponse.ERROR.assertNotNull(studentId, "学生id不能为空");
        long count = count(Wrappers.<ReadingPersonalPassages>lambdaQuery()
                .eq(ReadingPersonalPassages::getStudentId, studentId)
                .ge(ReadingPersonalPassages::getEndAt, sDate)
                .le(ReadingPersonalPassages::getEndAt, eDate)
                .in(ReadingPersonalPassages::getEntryType, Arrays.asList(2, 5))
                .eq(ReadingPersonalPassages::getStatus, 3)
                .isNull(ReadingPersonalPassages::getDeletedAt));
        return (int) count;
    }

    public Integer getPersistDays(Long studentId) throws BaseException {
        Integer result = baseMapper.getPersistDays(studentId);
        return result == null ? 0 : result;
    }

    @Override
    public ReadingPeriodicReportPosterVO getPeriodicReportPosterData(Long studentId, Integer reportType) throws BaseException {
        CommonResponse.ERROR.assertNotNull(studentId, "学生id不能为空");
        CommonResponse.ERROR.assertNotNull(reportType, "报告类型不能为空");
        LocalDate nowDate = LocalDate.now();
        LocalDate startDate = null;
        if(reportType == 1){
            startDate = nowDate.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
        }else{
            startDate = nowDate.with(TemporalAdjusters.firstDayOfMonth());
        }

        //获取训练记录表数据
        ReadingPersonalPassageDataParam trainingRecordParam = ReadingPersonalPassageDataParam.builder()
                .startDate(startDate).endDate(nowDate).studentId(studentId).build();
        Map<String, Object> trainingRecordData = readingPersonalAnalysisReportService.queryPersonalPassageData(trainingRecordParam);
        ReadingPeriodicReportPosterVO reportPosterData = new ReadingPeriodicReportPosterVO();
        if(ObjectUtil.isNotEmpty(trainingRecordData)){
            reportPosterData.setPassageCount(trainingRecordData.get("passageCount") == null ? 0 : Integer.parseInt(trainingRecordData.get("passageCount").toString()));
            reportPosterData.setKnowledgePointCount(trainingRecordData.get("totalKnowledgePoints") == null ? 0 : Integer.parseInt(trainingRecordData.get("totalKnowledgePoints").toString()));
            reportPosterData.setWeekKnowledgePointCount(trainingRecordData.get("weakPointCount") == null ? 0 : Integer.parseInt(trainingRecordData.get("weakPointCount").toString()));
            // 获取爆破数
            reportPosterData.setBlastingCount(getBlastingCount(studentId, startDate, nowDate));
            reportPosterData.setAllTimeSpent(getAllTimeSpent(studentId, startDate, nowDate));
        }else{
            reportPosterData.setPassageCount(0);
            reportPosterData.setKnowledgePointCount(0);
            reportPosterData.setWeekKnowledgePointCount(0);
            reportPosterData.setBlastingCount(0);
            reportPosterData.setAllTimeSpent(0);
        }
        //获取坚持天数
        reportPosterData.setPersistDays(getPersistDays(studentId));
        return reportPosterData;
    }

    @Override
    public Integer getRemainingPrintTimes(Long studentId) {
        CommonResponse.ERROR.assertNotNull(studentId, "学生id不能为空");
        Integer remainingPrintTimes=memberBenefitPrintTimesSetting;
        Integer result = baseMapper.getPrintTimes(studentId);
        if(result != null){
            remainingPrintTimes = remainingPrintTimes - result;
            remainingPrintTimes=remainingPrintTimes > 0 ? remainingPrintTimes : 0;
        }
        return remainingPrintTimes;
    }

    public Integer getAllTimeSpent(Long studentId, LocalDate startDate, LocalDate nowDate) throws BaseException {
        Integer result = baseMapper.getAllTimeSpent(studentId, startDate, nowDate);
        return result == null ? 0 : result;
    }

    /**
     * @description: 保存或更新个人练习记录和练习题目
     * @author: lifengxu
     * @date: 2025/6/18 9:44
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdatePersonalPassage(ReadingPersonalPassagesVo vo, Integer entryType) throws BaseException {
        // 有练习记录ID, 则更新,如需更新，先删后增
        if (ObjectUtil.isNotEmpty(vo.getId())) {
            // 删除原有的练习记录
            baseMapper.deleteById(vo.getId());
            // 删除原有练习记录的题目
            readingPersonalPassagesQuestionMapper.delete(
                    Wrappers.<ReadingPersonalPassagesQuestions>lambdaQuery()
                            .eq(ReadingPersonalPassagesQuestions::getPersonalPassageId, vo.getId())
            );
        }

        ReadingPersonalPassages passages = new ReadingPersonalPassages();
        passages.setStudentId(vo.getStudentId());
        passages.setPassageId(vo.getPassageId());
        if (ObjectUtil.isNotEmpty(vo.getPlanId())) {
            passages.setPlanId(vo.getPlanId());
        }
        passages.setStatus(PassageStatusEnum.PENDING.getCode());
        passages.setEntryType(entryType);
        passages.setCreatedAt(new Date());
        passages.setSetsId(vo.getSetsId());

        // 获取题目知识点保存薄弱点
        List<UUID> questionIds = vo.getQuestions().stream().map(ReadingPersonalPassageQuestionVo::getQuestionId).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(questionIds)) {
            List<ReadingQuestionknowledgePintVo> pointIdsByQuestionIds = readingPersonalPassagesQuestionMapper.getPointIdsByQuestionIds(questionIds);
            String knowledgePoints = String.join(",", pointIdsByQuestionIds.stream().map(ReadingQuestionknowledgePintVo::getName).distinct().collect(Collectors.toList()));
            passages.setKnowledgePoints(knowledgePoints);
        }

        // 保存reading_personal_passages
        baseMapper.insert(passages);
        vo.setId(passages.getId());

        // 保存reading_personal_passages_questions
        int questionNo = 1;
        for (ReadingPersonalPassageQuestionVo question : vo.getQuestions()) {
            ReadingPersonalPassagesQuestions questions = new ReadingPersonalPassagesQuestions();
            questions.setIsBlocked(0);
            questions.setIsFeedback(0);
            questions.setCreatedAt(new Date());
            questions.setQuestionNo(questionNo++);
            questions.setPersonalPassageId(passages.getId());
            questions.setQuestionId(question.getQuestionId());
            questions.setQuestionType(question.getQuestionType());
            readingPersonalPassagesQuestionMapper.insert(questions);
            question.setId(questions.getId());
        }
    }

    /**
     * @description: 【暑期训练营】获取活动对应上次练习记录信息
     * @author: lifengxu
     * @date: 2025/6/5
     */
    @Override
    public ReadingActivityLastPracticeDto getActivityLastPractice(Long planId, Long studentId) throws BaseException {
        /* 根据上一次练习确定是假期强化训练还是巩固练习 */
        List<ReadingActivityLastPracticeDto> lastPracticeDto = baseMapper.getActivityLastPractice(planId, studentId);
        if (ObjectUtil.isNotEmpty(lastPracticeDto)) {
            // 上次练习最后一条记录
            ReadingActivityLastPracticeDto lastPractice = lastPracticeDto.get(0);
            if (ObjectUtil.isEmpty(lastPractice)) {
                return null;
            }

            // 返回结果
            ReadingActivityLastPracticeDto result = new ReadingActivityLastPracticeDto();
            // 获取上一次练习的类型
            Integer lastEntryType = lastPractice.getEntryType();
            result.setEntryType(lastEntryType);

            // 上次练习记录IDs
            List<Long> personalPassageIds = new ArrayList<>();
            lastPracticeDto.forEach(practiceDto -> {
                // 同一天，同类型的记录，认为是同一天的练习
                if (practiceDto.getCreatedDate().equals(lastPractice.getCreatedDate())
                        && practiceDto.getEntryType().equals(lastPractice.getEntryType())) {
                    personalPassageIds.add(practiceDto.getPersonalPassagesId());
                }
            });
            result.setPersonalPassagesIds(personalPassageIds);

            // 根据personalPassageIds，查询错误知识点
            List<ReadingWeekKnowledgePointVo> weekKnowledgePointIds = baseMapper.getActivityStudentWeakKnowledgePoint(personalPassageIds);
            result.setWeekKnowledgePoints(weekKnowledgePointIds);

            return result;
        }
        return null;
    }

    /**
     * @description: 【暑期训练营】判断是强化训练还是巩固复习
     * @author: lifengxu
     * @date: 2025/6/5
     */
    @Override
    public int getActivityEntryType(Long planId, Long studentId) throws BaseException {
        ReadingActivityLastPracticeDto lastPracticeDto = getActivityLastPractice(planId, studentId);

        // 没有上次练习信息，或者上一次是巩固练习，或者没有错误知识点，返回假期强化训练
        if (ObjectUtil.isEmpty(lastPracticeDto)
                || ObjectUtil.isEmpty(lastPracticeDto.getWeekKnowledgePoints())
                || ReadingEntryTypeEnum.HOLIDAY_CONSOLIDATION_EXERCISES.getCode() == lastPracticeDto.getEntryType()) {
            return ReadingEntryTypeEnum.HOLIDAY_INTENSIVE_TRAINING.getCode();
        }

        // 上一次是假期强化训练且有错误知识点，返回巩固练习
        return ReadingEntryTypeEnum.HOLIDAY_CONSOLIDATION_EXERCISES.getCode();
    }

    @Override
    public List<ReadingWeekTimeStatisticsVO> getWeekReadingTimeSpent(Long studentId, LocalDate startDate, LocalDate endDate) throws BaseException {
        CommonResponse.ERROR.assertNotNull(studentId, "学生id不能为空");
        CommonResponse.ERROR.assertNotNull(startDate, "开始时间不能为空");
        CommonResponse.ERROR.assertNotNull(endDate, "结束时间不能为空");
        return this.baseMapper.getWeekReadingTimeSpent(studentId, startDate, endDate);
    }

    @Override
    public List<ReadingGrowthCycleVO> getWeekReadingGrowthCycle(Long studentId, LocalDate startDate, LocalDate endDate) throws BaseException {
        CommonResponse.ERROR.assertNotNull(studentId, "学生id不能为空");
        CommonResponse.ERROR.assertNotNull(startDate, "开始时间不能为空");
        CommonResponse.ERROR.assertNotNull(endDate, "结束时间不能为空");
        return this.baseMapper.getWeekReadingGrowthCycle(studentId, startDate, endDate);
    }
}
