package com.joinus.study.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.joinus.common.model.response.CommonResponse;
import com.joinus.study.mapper.MathExamsMapper;
import com.joinus.study.model.bo.JumpUrlBo;
import com.joinus.study.model.dto.ExamHasKnowledgeDto;
import com.joinus.study.model.entity.MathActivityWeekUnit;
import com.joinus.study.model.entity.MathExamsEntity;
import com.joinus.study.model.entity.PersonalExam;
import com.joinus.study.model.enums.ExamSourceType;
import com.joinus.study.model.enums.MathActivityWeekUnitTypeEnum;
import com.joinus.study.model.enums.PublisherEnum;
import com.joinus.study.model.param.ExamHasKnowledgeParam;
import com.joinus.study.model.vo.CheckExamExsitenceVo;
import com.joinus.study.service.EduKnowledgeHubService;
import com.joinus.study.service.ExamAnalyzeResultService;
import com.joinus.study.service.MathAnonService;
import com.joinus.study.service.PersonalExamService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.UUID;

@Service
@Slf4j
public class MathAnonServiceImpl implements MathAnonService {


    @Autowired
    private MathExamsMapper mathExamsMapper;
    @Autowired
    private PersonalExamService personalExamService;
    @Autowired
    private EduKnowledgeHubService eduKnowledgeHubService;
    @Autowired
    private ExamAnalyzeResultService examAnalyzeResultService;

    @Value("${ijx.ink.q.math:https://ijx.ink/q/math/}")
    private String inkQMath;
    @Value("${ijx.ink.q.math.qr.url.exam_papers:https://cdn-ali-static.ijx.ink/h5-learning-tools-dev/index.html#/questionMarking?buttonType=scanBtn}")
    private String mathQrJumpUrlExamPapers;
    @Value("${ijx.ink.q.math.qr.url.special_training:https://cdn-ali-static.ijx.ink/h5-learning-tools-dev/index.html#/localPaperRecommend/specialPaperMask?params={}}")
    private String mathQrJumpUrlSpecialTraing;
    @Value("${ijx.ink.q.math.qr.url.regular_exam_papers:https://cdn-ali-static.ijx.ink/h5-learning-tools-dev/index.html#/localPaperRecommend/testPreview?examId={}}")
    private String mathQrJumpUrlRegularExamPapers;
    @Value("${ijx.ink.q.math.photos.url.exam_papers:https://cdn-ali-static.ijx.ink/h5-learning-tools-dev/index.html#/questionMarking?buttonType=popBtn}")
    private String mathPhotosJumpUrlExamPapers;
    @Value("${ijx.ink.q.math.photos.url.special_training:https://cdn-ali-static.ijx.ink/h5-learning-tools-dev/index.html#/questionMarking?buttonType=popBtn}")
    private String mathPhotosJumpUrlSpecialTraing;
    @Value("${ijx.ink.q.math.photos.url.regular_exam_papers:https://cdn-ali-static.ijx.ink/h5-learning-tools-dev/index.html#/localPaperRecommend/testPreview?examId={}}")
    private String mathPhotosJumpUrlRegularExamPapers;
    @Value("${ijx.ink.q.chinese.qr.url:https://cdn-ali-static.ijx.ink/h5-learning-tools-dev/index.html#/chineseClass/questionLabeling?qrStr={}&keyboard={}}")
    private String chineseJumpUrl;

    @Override
    public JumpUrlBo queryJumpUrlForMath(String qrStr, Long studentId) {
        CommonResponse.ERROR.assertNotEmpty(qrStr, "请输入试卷id");
        UUID examId = null;
        if (!qrStr.startsWith(inkQMath)) {
            CommonResponse.assertError("二维码不符合规则");
        }
        CheckExamExsitenceVo.DataDTO examData = new CheckExamExsitenceVo.DataDTO();
        MathExamsEntity mathExamsEntity = null;
            String printId = qrStr.replace(inkQMath, "");


        // 判断printId是数字还是UUID
        if (StrUtil.isNumeric(printId)) {
            // 如果是数字，转换为Long类型并查询
            Long id = Long.parseLong(printId);
            PersonalExam personalExam = personalExamService.getById(id);
            if (null == personalExam) {
                CommonResponse.assertError("个人考试不存在");
            }
            if (null == personalExam.getExamId()) {
                CommonResponse.assertError("个人考试的试卷id不存在");
            }
            mathExamsEntity = mathExamsMapper.selectById(personalExam.getExamId());
        } else {
            // 如果不是数字，尝试作为UUID处理
            try {
                UUID uuidId = UUID.fromString(printId);
                mathExamsEntity = mathExamsMapper.selectById(uuidId);

            } catch (IllegalArgumentException uuidException) {
                log.error("printId既不是有效的数字也不是有效的UUID: {}", printId, uuidException);
            }
        }

        CommonResponse.ERROR.assertNotNull(mathExamsEntity, "试卷不存在");
        examId = mathExamsEntity.getId();
        examData.setPublisher(mathExamsEntity.getPublisher() != null ? PublisherEnum.ofCustomerName(mathExamsEntity.getPublisher()) : null);
        examData.setExamSource(mathExamsEntity.getSource());

        boolean hasKnowledge = false;
        ExamHasKnowledgeDto examHasKnowledgeDto = eduKnowledgeHubService.examHasKnowledge(ExamHasKnowledgeParam.builder().examId(examId).build());
        if (examHasKnowledgeDto != null && examHasKnowledgeDto.getCode() == CommonResponse.SUCCESS.getCode()) {
            hasKnowledge = examHasKnowledgeDto.getData().isExist();
        }

        examData.setKnowledgePointsExist(hasKnowledge);
        examData.setExamId(examId);
        examData.setExist(true);
        examData.setSpecialTraining(true);

        JumpUrlBo jumpUrlBo = new JumpUrlBo();
        jumpUrlBo.setParams(JSONUtil.parseObj(examData));

        String url = "";
        switch (mathExamsEntity.getSource()) {

            case PAST_EXAM_PAPER:
            case REGULAR_EXAM_PAPER:
                url = StrUtil.format(mathQrJumpUrlRegularExamPapers, examId);
                break;
            case USER_UPLOAD:
                url = mathQrJumpUrlExamPapers;
                break;
            case SPECIAL_TRAINING:
            case EXAM_BLIND_SPOTS_TRAINING:
            case HOLIDAY_TRAINING:
                url = getMathQrJumpurlForHolidayTraining(examId, studentId);
                break;
            default:
                break;
        }
        jumpUrlBo.setUrl(url);
        return jumpUrlBo;
    }

    @Override
    public JumpUrlBo queryJumpUrlForChiniese(String qrStr, String keyboard) {
        return JumpUrlBo.builder()
                .url(StrUtil.format(chineseJumpUrl, qrStr, keyboard))
                .build();
    }

    @Override
    public String getJumpUrlByExamSource(ExamSourceType examSource, UUID examId) {
        String url = "";
        if (null == examSource) {
            return url;
        }
        switch (examSource) {

            case PAST_EXAM_PAPER:
            case REGULAR_EXAM_PAPER:
                url = StrUtil.format(mathPhotosJumpUrlRegularExamPapers, examId);
                break;
            case USER_UPLOAD:
                url = mathPhotosJumpUrlExamPapers;
                break;
            case SPECIAL_TRAINING:
            case EXAM_BLIND_SPOTS_TRAINING:
                url = mathPhotosJumpUrlExamPapers;
                break;
            case HOLIDAY_TRAINING:
//                url = getMathPhotosJumpurlForHolidayTraining(examId);
                url = mathPhotosJumpUrlExamPapers;
                break;
            default:
                break;
        }
        return url;
    }

    private String getMathPhotosJumpurlForHolidayTraining(UUID examId) {
        MathActivityWeekUnit mathActivityWeekUnit = examAnalyzeResultService.getMathActivityWeekUnitByTrainingExamId(examId);
        if (null != mathActivityWeekUnit && mathActivityWeekUnit.getType() == MathActivityWeekUnitTypeEnum.COMPREHENSIVE_TEST) {
            return StrUtil.format(mathPhotosJumpUrlRegularExamPapers, examId);
        }
        return mathPhotosJumpUrlSpecialTraing;
    }

    private String getMathQrJumpurlForHolidayTraining(UUID examId, Long studentId) {
        MathActivityWeekUnit mathActivityWeekUnit = examAnalyzeResultService.getMathActivityWeekUnitByTrainingExamId(examId);
        MathActivityWeekUnitTypeEnum weekUnitType = null != mathActivityWeekUnit ? mathActivityWeekUnit.getType() : null;
        PersonalExam personalExam = personalExamService.getLatestNoAnalyzedPersonalExam(examId, studentId);
        JSONObject params = new JSONObject();
        params.set("examId", examId);
        params.set("personalExamId", null == personalExam ? null : personalExam.getId());
        params.set("weekUnitType", null == weekUnitType ? null : weekUnitType.name());
        String format = StrUtil.format(mathQrJumpUrlSpecialTraing, JSONUtil.toJsonStr(params));
        return format;
    }


}
