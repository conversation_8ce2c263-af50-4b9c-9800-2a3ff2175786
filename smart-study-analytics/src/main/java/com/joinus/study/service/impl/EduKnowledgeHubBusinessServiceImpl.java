package com.joinus.study.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.joinus.common.model.response.CommonResponse;
import com.joinus.study.interceptor.util.DataUtils;
import com.joinus.study.mapper.*;
import com.joinus.study.model.dto.*;
import com.joinus.study.model.entity.*;
import com.joinus.study.model.enums.*;
import com.joinus.study.model.param.*;
import com.joinus.study.model.vo.*;
import com.joinus.study.service.*;
import com.joinus.study.utils.StreamResponseCollector;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import javax.annotation.Resource;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * @Description: edu-knowledge-hub （AI）项目关联业务
 * @Author: anpy
 * @date: 2025/3/17 13:54
 */
@Service
@Slf4j
public class EduKnowledgeHubBusinessServiceImpl implements EduKnowLedgeHubBusinessService {

    @Resource
    private EduKnowledgeHubService eduKnowledgeHubService;
    @Resource
    private PersonalExamMapper personalExamMapper;
    @Resource
    private MathQuestionsMapper mathQuestionsMapper;
    @Resource
    private GradeService gradeService;
    @Resource
    private StudyRecordMapper studyRecordMapper;
    @Autowired
    private StudyRecordQuestionMapper studyRecordQuestionMapper;
    @Resource
    private PersonalExamService personalExamService;
    @Resource
    private MathAnonService mathAnonService;

    @Value("${question.demo.single:}")
    private String singleQuestionJson;
    @Value("${question.demo.mutil:}")
    private String fullPageQuestionJson;
    @Value("${exam_has_grade_semester:false}")
    private boolean examHasGradeSemester;
    @Autowired
    private MathExamsMapper mathExamsMapper;

    @Override
    public QuestionCoordinateVo questionCoordinateInfo(QueryQuestionCoordinateParam param) {
        // 调用AI服务器获取题目坐标
        QuestionCoordinateDto questionCoordinateDto = eduKnowledgeHubService.questionCoordinateInfo(param);
        if (questionCoordinateDto == null || questionCoordinateDto.getCode() != CommonResponse.SUCCESS.getCode()) {
            CommonResponse.assertError("获取题目坐标服务失败");
        }

        // 转换DTO到VO对象
        QuestionCoordinateVo questionCoordinateVo = QuestionCoordinateVo.builder().build();
        BeanUtil.copyProperties(questionCoordinateDto, questionCoordinateVo);
        QuestionCoordinateVo.DataDTO data = questionCoordinateVo.getData();

        // 验证数据有效性
        if (data == null || CollUtil.isEmpty(data.getSubjects())) {
            CommonResponse.assertError("获取题目坐标失败");
        }

        List<QuestionCoordinateVo.DataDTO.SubjectsDTO> subjects = data.getSubjects();
        // 根据题目类型处理坐标数据
        processSubjectsByQuestionType(param, subjects);

        // 处理学习记录
        if (isMultiplePageSecondQuestion(param)) {
            // 更新跨页题第二道题，只保存key，不添加学习记录
            updateStudyRecordMultiplePageData(param, data, subjects);
        } else {
            // 计算有效题目数量
            int questionCount = (int) subjects.stream()
                    .filter(subject -> StrUtil.isNotBlank(subject.getText()))
                    .count();

            // 序列化坐标数据
            String positions = JSONUtil.toJsonStr(subjects);

            // 学习记录表入库
            StudyRecord studyRecord = saveStudyRecord(param, positions, questionCount);
            data.setId(studyRecord.getId());

            // 学习记录详情表入库
            List<StudyRecordQuestion> studyRecordQuestionList = saveStudyRecordQuestion(param, subjects, studyRecord.getId());

            // 异步保存学习记录详情ossKey
            saveStudyRecordOssKey(param, studyRecordQuestionList, data);
        }

        return questionCoordinateVo;
    }

    /**
     * 判断是否为跨页题的第二道题
     */
    private boolean isMultiplePageSecondQuestion(QueryQuestionCoordinateParam param) {
        return param.getPhotoQuestionTypeEnum() == PhotoQuestionTypeEnum.MULTIPLE_PAGE && param.getStudyRecordId() != null;
    }

    /**
     * 根据题目类型处理坐标数据
     */
    private void processSubjectsByQuestionType(QueryQuestionCoordinateParam param, List<QuestionCoordinateVo.DataDTO.SubjectsDTO> subjects) {
        if (subjects.size() <= 1) {
            return; // 只有一个或没有坐标时不需要处理
        }

        if (param.getPhotoQuestionTypeEnum() == PhotoQuestionTypeEnum.SINGLE) {
            // 单题取第一题坐标
            int validIndex = findFirstValidIndex(subjects);
            CommonResponse.ERROR.assertIsTrue(validIndex >= 0, "获取题目坐标失败");
            // 保留第一个有效的坐标，删除其他坐标
            QuestionCoordinateVo.DataDTO.SubjectsDTO validSubject = subjects.get(validIndex);
            subjects.clear();
            subjects.add(validSubject);
        } else if (isMultiplePageSecondQuestion(param)) {
            // 跨页题第二张图片，只保存第一题坐标
            if (!subjects.isEmpty()) {
                QuestionCoordinateVo.DataDTO.SubjectsDTO firstSubject = subjects.get(0);
                subjects.clear();
                subjects.add(firstSubject);
            }
        } else if (param.getPhotoQuestionTypeEnum() == PhotoQuestionTypeEnum.MULTIPLE_PAGE && param.getStudyRecordId() == null) {
            // 跨页题第一张图片，取最后一个坐标
            int validIndex = findLastValidIndex(subjects);
            CommonResponse.ERROR.assertIsTrue(validIndex >= 0, "获取题目坐标失败");
            // 保留最后一个有效的坐标，删除其他坐标
            QuestionCoordinateVo.DataDTO.SubjectsDTO validSubject = subjects.get(validIndex);
            subjects.clear();
            subjects.add(validSubject);
        }
    }

    /**
     * 找到第一个有效的坐标索引
     *
     * @param subjects 坐标列表
     * @return 第一个有效的坐标索引，-1表示没有找到有效坐标
     */
    private int findFirstValidIndex(List<QuestionCoordinateVo.DataDTO.SubjectsDTO> subjects) {
        for (int i = 0; i < subjects.size(); i++) {
            if (StrUtil.isNotBlank(subjects.get(i).getText())) {
                return i;
            }
        }
        return -1;
    }

    /**
     * 找到最后一个有效的坐标索引
     *
     * @param subjects 坐标列表
     * @return 最后一个有效的坐标索引，-1表示没有找到有效坐标
     */
    private int findLastValidIndex(List<QuestionCoordinateVo.DataDTO.SubjectsDTO> subjects) {
        for (int i = subjects.size() - 1; i >= 0; i--) {
            if (StrUtil.isNotBlank(subjects.get(i).getText())) {
                return i;
            }
        }
        return -1; // 没有找到有效坐标
    }

    /**
     * 更新跨页题第二道题，只保存key，不添加学习记录
     */
    private void updateStudyRecordMultiplePageData(QueryQuestionCoordinateParam param, QuestionCoordinateVo.DataDTO data, List<QuestionCoordinateVo.DataDTO.SubjectsDTO> subjects) {
        StudyRecord studyRecord = studyRecordMapper.selectById(param.getStudyRecordId());
        String ossUrl = studyRecord.getOssUrl();
        if (StrUtil.isNotBlank(ossUrl)) {
            ossUrl = ossUrl + "," + param.getOssKey();
        } else {
            ossUrl = param.getOssKey();
        }
        studyRecord.setOssUrl(ossUrl);
        studyRecordMapper.updateById(studyRecord);
        data.setId(studyRecord.getId());
        List<StudyRecordQuestion> studyRecordQuestions = studyRecordQuestionMapper.selectList(Wrappers.<StudyRecordQuestion>lambdaQuery()
                .eq(StudyRecordQuestion::getStudyRecordId, studyRecord.getId()));
        if (CollUtil.isNotEmpty(studyRecordQuestions)) {
            StudyRecordQuestion studyRecordQuestion = studyRecordQuestions.get(0);
            subjects.get(0).setId(studyRecordQuestion.getId());
            subjects.get(0).setQuestionId(studyRecordQuestion.getQuestionId());
        }
    }

    /**
     * 学习记录表入库
     */
    @NotNull
    private StudyRecord saveStudyRecord(QueryQuestionCoordinateParam param, String positions, int questionCount) {
        StudyRecord studyRecord = StudyRecord
                .builder()
                .ossUrl(param.getOssKey())
                .studentId(param.getStudentId())
                .subject(SubjectEnum.math)
                .positions(positions)
                .questionsCount(questionCount)
                .build();
        studyRecordMapper.insert(studyRecord);

        if (studyRecord.getId() == null) {
            CommonResponse.assertError("获取题目学习记录失败");
        }
        return studyRecord;
    }

    /**
     * 学习记录详情表入库
     *
     * @param param         getPhotoQuestionTypeEnum
     *                      拍题类型：SINGLE 单题
     *                      跨页题：MULTIPLE_PAGE
     * @param subjects      坐标数据
     * @param studyRecordId 学习记录主键id
     */
    private List<StudyRecordQuestion> saveStudyRecordQuestion(QueryQuestionCoordinateParam param, List<QuestionCoordinateVo.DataDTO.SubjectsDTO> subjects,
                                                              Long studyRecordId) {
        List<StudyRecordQuestion> studyRecordQuestionList = new ArrayList<>();
        for (int i = 0; i < subjects.size(); i++) {
            QuestionCoordinateVo.DataDTO.SubjectsDTO subject = subjects.get(i);
            if (StrUtil.isNotBlank(subject.getText())) {
                UUID questionId = UUID.randomUUID();
                StudyRecordQuestion studyRecordQuestion = StudyRecordQuestion.builder()
                        .questionId(questionId)
                        .sortNo(i)
                        .studyRecordId(studyRecordId)
                        .type(SingleQuestionType.COORDINATE.name())
                        .build();
                studyRecordQuestionMapper.insert(studyRecordQuestion);
                studyRecordQuestionList.add(studyRecordQuestion);

                subject.setId(studyRecordQuestion.getId());
                subject.setQuestionId(questionId);

                //单题和跨页题，只取第一题
                if (param.getPhotoQuestionTypeEnum() == PhotoQuestionTypeEnum.SINGLE || param.getPhotoQuestionTypeEnum() == PhotoQuestionTypeEnum.MULTIPLE_PAGE) {
                    break;
                }
            }
        }
        return studyRecordQuestionList;
    }

    private void saveStudyRecordOssKey(QueryQuestionCoordinateParam param, List<StudyRecordQuestion> studyRecordQuestionList, QuestionCoordinateVo.DataDTO data) {
        // 异步处理，不阻塞主线程
        final ExecutorService executorService = Executors.newFixedThreadPool(Math.min(studyRecordQuestionList.size(), 10));
        CompletableFuture.runAsync(() -> {
            try {
                List<CompletableFuture<Void>> futures = new ArrayList<>();

                for (StudyRecordQuestion studyRecordQuestion : studyRecordQuestionList) {
                    for (int i = 0; i < data.getSubjects().size(); i++) {
                        final int index = i;
                        CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                            try {
                                List<CoordinatePoint> coordinatePoints = getCoordinatePoints(data, index);
                                SingleCutImageFromPositionsParam build = SingleCutImageFromPositionsParam.builder()
                                        .positions(coordinatePoints)
                                        .ossKey(param.getOssKey())
                                        .ossEnum(param.getOssEnum())
                                        .build();
                                CutImageFromPositionsDto cutImageFromPositionsDto = eduKnowledgeHubService.cutImageFromPositions(build);
                                if (cutImageFromPositionsDto != null) {
                                    StudyRecordQuestion studyRecordQuestionData = studyRecordQuestionMapper.selectById(studyRecordQuestion.getId());
                                    if (StrUtil.isBlank(studyRecordQuestionData.getOssKeys())) {
                                        studyRecordQuestionMapper.update(null, new LambdaUpdateWrapper<StudyRecordQuestion>()
                                                .eq(StudyRecordQuestion::getId, studyRecordQuestion.getId())
                                                .set(StudyRecordQuestion::getOssKeys, cutImageFromPositionsDto.getData().getKey())
                                        );
                                    }
                                }
                            } catch (Exception e) {
                                log.error("异步处理图像切割任务异常", e);
                            }
                        }, executorService);
                        futures.add(future);
                    }
                }

                // 等待所有异步任务完成
                CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
            } catch (Exception e) {
                log.error("异步处理图像切割总任务异常", e);
            } finally {
                // 关闭线程池
                executorService.shutdown();
            }
        });
    }

    @NotNull
    private static List<CoordinatePoint> getCoordinatePoints(QuestionCoordinateVo.DataDTO data, int index) {
        List<QuestionCoordinateVo.DataDTO.SubjectsDTO.PosListDTO> posListDtos = data.getSubjects().get(index).getPos_list().get(0);
        List<CoordinatePoint> coordinatePoints = new ArrayList<>();
        for (QuestionCoordinateVo.DataDTO.SubjectsDTO.PosListDTO posListDTO : posListDtos) {
            CoordinatePoint coordinatePoint = new CoordinatePoint();
            coordinatePoint.setX((double) posListDTO.getX());
            coordinatePoint.setY((double) posListDTO.getY());
            coordinatePoints.add(coordinatePoint);
        }
        return coordinatePoints;
    }


    @Override
    public Flux<String> chatStream(SolveQuestionFromImgParam param) {
        Flux<String> originalFlux = eduKnowledgeHubService.chatStream(param);
        // 生成请求ID
        String requestId = UUID.randomUUID().toString();
        return StreamResponseCollector.collectFluxNonBlocking(
                originalFlux,
                "textStream-" + requestId,
                // 流结束时的异步处理函数
                fullContent -> {
                    // 使用StreamContentStorageService存储内容
                    return storeContent(
                            requestId,
                            param,
                            fullContent
                    );
                }
        );

    }

    private Mono<String> storeContent(String requestId, SolveQuestionFromImgParam param, String fullContent) {
        return Mono.fromRunnable(() -> {
                    // 打印收集到的完整内容和请求参数（仅用于演示）
                    log.info("============ 流式响应完整内容 ============");
                    log.info("请求ID: {}", requestId);
                    log.info("提示词: {}", param.getPrompt());
//                    log.info("模型客户端: {}", param.getModelClient());
                    log.info("请求参数: {}", JSONUtil.toJsonStr(param));
                    log.info("响应内容长度: {} 字符", fullContent.length());
                    log.info("响应内容摘要: {}", fullContent.length() > 100 ? fullContent.substring(0, 100) + "..." : fullContent);


                    saveToDatabase(requestId, param, fullContent);
                    log.info("已将流式响应内容存储到数据库，ID {}", requestId);

                }).subscribeOn(Schedulers.boundedElastic())
                .then(Mono.just("success"));
    }

    private void saveToDatabase(String requestId, SolveQuestionFromImgParam param, String fullContent) {

    }

    @Override
    public Flux<String> chatStreamMock(SolveQuestionFromImgParam param) {
        int chunkSize = 20; // 每块20字符
        String result = param.getPrompt();
        Flux<String> streamFlux = Flux.range(0, (result.length() + chunkSize - 1) / chunkSize)
                .map(i ->
                        result.substring(i * chunkSize,
                                Math.min((i + 1) * chunkSize, result.length())))
                .delayElements(Duration.ofMillis(20));
        return streamFlux;
    }

    @Override
    public EraseHandwritingFromQuestionVo eraseHandwritingFromQuestion(EraseHandwritingFromQuestionParam param) {
        //调用AI服务器
        EraseHandwritingFromQuestionDto eraseHandwritingFromQuestionDto = eduKnowledgeHubService.eraseHandwritingFromQuestion(param);
        EraseHandwritingFromQuestionVo eraseHandwritingFromQuestionVo = EraseHandwritingFromQuestionVo.builder().build();
        BeanUtil.copyProperties(eraseHandwritingFromQuestionDto, eraseHandwritingFromQuestionVo);
        return eraseHandwritingFromQuestionVo;
    }

    @Override
    public Flux<String> queryMultiQuestionAnalysisResults(SolveQuestionFromImgParam param) {
        Flux<String> originalFlux = eduKnowledgeHubService.querySingleQuestionAnalysisResults(param);
        // 生成请求ID
        String requestId = UUID.randomUUID().toString();
        return originalFlux;
//        return StreamResponseCollector.collectFluxNonBlocking(
//                originalFlux,
//                "textStream-" + requestId,
//                // 流结束时的异步处理函数
//                fullContent -> {
//                    // 使用StreamContentStorageService存储内容
//                    return storeContent(
//                            requestId,
//                            param,
//                            fullContent
//                    );
//                }
//        );
    }

    @Override
    public Flux<String> querySingleQuestionAnalysisResults(SolveQuestionFromImgParam param) throws JsonProcessingException {

        // 参数校验
        if (param == null || param.getQuestionId() == null) {
            log.error("参数错误: param={}", JSONUtil.toJsonStr(param));
            return Flux.error(new IllegalArgumentException("参数错误"));
        }

        // 获取学生年级信息
        GradeInfo grade = gradeService.getGradeByStudentId(param.getStudentId());
        log.info("学生年级信息: {}", JSONUtil.toJsonStr(grade));

        // 3. 检查题目是否存在
        CheckExistQuestionParam checkParam = CheckExistQuestionParam.builder()
                .grade(grade.getCurrentGradeLevel())
                .semester(grade.getCurrentSemester())
                .objectNames(param.getObjectNames())
                .ossEnum(param.getOssEnum())
                .build();
        CheckExistQuestionDto checkExistQuestionDto = eduKnowledgeHubService.checkExistQuestion(checkParam);
        if (checkExistQuestionDto == null || checkExistQuestionDto.getData() == null) {
            log.info("题目不存在，将请求流式数据");
            return getAiResultFluxData(param);
        }

        // 1. 先检查是否有反馈答案
        AnswerIdForFeedback answerIdForFeedback = mathQuestionsMapper.selectAnswerIdByRequestIdForFeedback(param.getQuestionId());
        if (answerIdForFeedback != null) {
            log.info("找到反馈答案ID: {}", answerIdForFeedback.getAnswerId());
            QuestionAnswerDto questionAnswerDto = mathQuestionsMapper.selectAnswersById(answerIdForFeedback.getAnswerId());
            if (questionAnswerDto != null) {
                studyRecordQuestionMapper.update(null, new LambdaUpdateWrapper<StudyRecordQuestion>()
                        .eq(StudyRecordQuestion::getId, param.getStudyRecordQuestionId())
                        .set(StudyRecordQuestion::getType, SingleQuestionType.ANSWER_START.name()));
                Flux<String> streamFromQuestionAnswerDto = createStreamFromQuestionAnswerDto(questionAnswerDto);
                return streamFromQuestionAnswerDto
                        .doOnSubscribe(s -> log.info("控制器开始订阅流式数据"))
                        .doOnNext(chunk -> {
                        })
                        .doOnComplete(() -> {
                            log.info("流式数据接收完成，requestId: {}", param.getQuestionId());
                            streamFromQuestionAnswerDto.cache()
                                    .collectList()
                                    .subscribeOn(Schedulers.boundedElastic())
                                    .subscribe(list -> {
                                        String fullContent = String.join("", list);
                                        log.info("============ 流式响应完整内容 ============");
                                        log.info("请求ID: {}", param.getQuestionId());
                                        log.info("提示词: {}", param.getPrompt());
                                        log.info("请求参数: {}", JSONUtil.toJsonStr(param));
                                        log.info("响应内容长度: {} 字符", fullContent.length());
                                        log.info("响应内容摘要: {}", fullContent.length() > 100 ? fullContent.substring(0, 100) + "..." : fullContent);
                                        log.info("已将流式响应内容存储到数据库，ID {}", param.getQuestionId());

                                        ParseAnswerVo parseAnswerVo = eduKnowledgeHubService.parseAnswer(ParseAnswerParam.builder().text(StreamResponseCollector.fluxToText(fullContent)).build());
//                                        System.out.printf("解析结果: %s%n", parseAnswerVo);

//                                        saveStudyRecord(param, questionAnswerDto);
                                        saveStudyRecord(param,
                                                QuestionAnswerDto.builder()
                                                        .answer(parseAnswerVo.getData().getAnswer())
                                                        .content(parseAnswerVo.getData().getContent()).build(), SingleQuestionType.ANSWER_END.name());
                                    });

                        })
                        .doOnError(e -> log.error("控制器发送流式数据出错: {}", e.getMessage(), e));
            }
        }

        // 2. 检查是否有题目答案
        QuestionAnswerDto questionAnswerDto = mathQuestionsMapper.selectAnswersByQuestionId(param.getQuestionId());
        if (questionAnswerDto != null) {
            studyRecordQuestionMapper.update(null, new LambdaUpdateWrapper<StudyRecordQuestion>()
                    .eq(StudyRecordQuestion::getId, param.getStudyRecordQuestionId())
                    .set(StudyRecordQuestion::getType, SingleQuestionType.ANSWER_START.name()));
            return createStreamFromQuestionAnswerDto(questionAnswerDto)
                    .doOnSubscribe(s -> {
                    })
                    .doOnNext(chunk -> {
                    })
                    .doOnComplete(() -> {
                        saveStudyRecord(param, questionAnswerDto, SingleQuestionType.ANSWER_END.name());
                    })
                    .doOnError(e -> log.error("控制器发送流式数据出错: {}", e.getMessage(), e));
        }

        // 4. 如果题目存在但没有答案，请求流式数据
        return getAiResultFluxData(param);
    }

    private void saveStudyRecord(SolveQuestionFromImgParam param, QuestionAnswerDto questionAnswerDto, String type) {
        String answer = "";
        String content = "";
        if (questionAnswerDto != null) {
            answer = questionAnswerDto.getAnswer();
            content = questionAnswerDto.getContent();
        }

        studyRecordQuestionMapper.update(null, new LambdaUpdateWrapper<StudyRecordQuestion>()
                .eq(StudyRecordQuestion::getId, param.getStudyRecordQuestionId())
                .set(StudyRecordQuestion::getQuestionAnswerContent, content)
                .set(StudyRecordQuestion::getType, type)
                .set(StudyRecordQuestion::getQuestionAnswer, answer));
    }

    @Override
    public Flux<String> createStreamFromQuestionAnswerDto(QuestionAnswerDto questionAnswerDto) {
        String answer = questionAnswerDto.getAnswer();
        String content = questionAnswerDto.getContent();
        String mergedContent = answer + "\n" + content;

        // 将内容分成更大的块，每块约20个字符
        List<String> chunks = new ArrayList<>();
        int chunkSize = 20; // 每个块的大小

        for (int i = 0; i < mergedContent.length(); i += chunkSize) {
            int end = Math.min(i + chunkSize, mergedContent.length());
            String chunk = mergedContent.substring(i, end);
            // 处理特殊字符的转义，确保JSON格式正确
            chunk = escapeJsonString(chunk);

            if (end >= mergedContent.length()) {
                // 最后一个块，finish设为true
                chunks.add("{\"content\":\"" + chunk + "\",\"finish\":true}");
            } else {
                chunks.add("{\"content\":\"" + chunk + "\",\"finish\":false}");
            }
        }

        // 如果内容为空，添加一个空块
        if (chunks.isEmpty()) {
            chunks.add("{\"content\":\"\",\"finish\":true}");
        }

        // 将chunks转换为Flux并添加延迟
        return Flux.fromIterable(chunks)
                .delayElements(Duration.ofMillis(50))
                // 添加缓存以防止多次订阅重新执行
                .cache()
                // 使用ServerSentEvent格式包装每个数据块，这是标准的SSE格式
                .map(chunk -> chunk + "\n\n");
    }

    /**
     * 转义JSON字符串中的特殊字符
     */
    private String escapeJsonString(String input) {
        if (input == null) {
            return "";
        }

        StringBuilder result = new StringBuilder();
        for (int i = 0; i < input.length(); i++) {
            char ch = input.charAt(i);
            switch (ch) {
                case '"':
                    result.append("\\\""); // 转义双引号
                    break;
                case '\\':
                    result.append("\\\\"); // 转义反斜杠
                    break;
                case '\b':
                    result.append("\\b"); // 退格符
                    break;
                case '\f':
                    result.append("\\f"); // 换页符
                    break;
                case '\n':
                    result.append("\\n"); // 换行符
                    break;
                case '\r':
                    result.append("\\r"); // 回车符
                    break;
                case '\t':
                    result.append("\\t"); // 制表符
                    break;
                default:
                    // 处理其他不可打印字符
                    if (ch < ' ') {
                        String hex = Integer.toHexString(ch);
                        result.append("\\u");
                        for (int j = 0; j < 4 - hex.length(); j++) {
                            result.append('0');
                        }
                        result.append(hex);
                    } else {
                        result.append(ch);
                    }
            }
        }
        return result.toString();
    }

    @Override
    @NotNull
    public Flux<String> getAiResultFluxData(SolveQuestionFromImgParam param) {
        UUID questionId = param.getQuestionId();
        log.info("开始请求流式数据，参数: {}", JSONUtil.toJsonStr(param));

        // 获取原始的流数据
        studyRecordQuestionMapper.update(null, new LambdaUpdateWrapper<StudyRecordQuestion>()
                .eq(StudyRecordQuestion::getId, param.getStudyRecordQuestionId())
                .set(StudyRecordQuestion::getType, SingleQuestionType.AI_ANSWER_START.name()));
        Flux<String> originalFlux = eduKnowledgeHubService.querySingleQuestionAnalysisResults(param);

        // 添加日志记录每个数据块的接收情况，并确保数据能够逐步传输
        StringBuilder contentCollector = new StringBuilder();
        return originalFlux
                .doOnSubscribe(s -> log.info("流式数据订阅开始，requestId: {}", questionId))
                .doOnNext(chunk -> {
                    // 收集数据块
                    contentCollector.append(chunk);

                    // 尝试提前解析单个数据块，减少最终处理时间
                    try {
                        // 这里不做实际处理，只是预热内容
                        if (chunk != null && !chunk.isEmpty()) {
                            // 预热处理，不需要保存结果
                            StreamResponseCollector.fluxToText(chunk);
                        }
                    } catch (Exception e) {
                        // 忽略预处理错误
                    }
                })
                .delayElements(Duration.ofMillis(10))
                .doOnComplete(() -> {
                    log.info("流式数据接收完成，requestId: {}", questionId);

                    // 开启一个新线程处理解析工作，不阻塞主流程
                    CompletableFuture.runAsync(() -> {
                        try {
                            String fullContent = contentCollector.toString();
                            // 先解析内容，再打印日志
                            String parsedContent = StreamResponseCollector.fluxToText(fullContent);

                            log.info("============ 流式响应完整内容 ============");
                            log.info("请求ID: {}", questionId);
                            log.info("提示词: {}", param.getPrompt());
                            log.info("请求参数: {}", JSONUtil.toJsonStr(param));
                            log.info("响应内容长度: {} 字符", fullContent.length());
                            log.info("响应内容摘要: {}", fullContent.length() > 100 ? fullContent.substring(0, 100) + "..." : fullContent);
                            log.info("已将流式响应内容存储到数据库，ID {}", questionId);

                            ParseAnswerVo parseAnswerVo = eduKnowledgeHubService.parseAnswer(ParseAnswerParam
                                    .builder()
                                    .text(parsedContent)
                                    .build());
                            System.out.println("fullContent: " + parsedContent);
                            System.out.printf("解析结果: %s%n", parseAnswerVo);

                            // 保存学习记录
                            saveStudyRecord(param,
                                    QuestionAnswerDto.builder()
                                            .answer(parseAnswerVo.getData().getAnswer())
                                            .content(parseAnswerVo.getData().getContent()).build(), SingleQuestionType.AI_ANSWER_END.name());
                        } catch (Exception e) {
                            log.error("处理流式响应数据时发生错误: {}", e.getMessage(), e);
                        }
                    });
                })
                .doOnError(e -> log.error("流式数据处理出错，questionId: {}, 错误: {}", questionId, e.getMessage(), e));
    }

    @Override
    public String exampleQuestionPicture(ExampleQuestionEnum type) {
        ObjectMapper mapper = new ObjectMapper();
        ExampleQuestionJsonVo exampleQuestion = null;
        try {
            if (type == ExampleQuestionEnum.SINGLE_QUESTION) {
                exampleQuestion = mapper.readValue(singleQuestionJson, ExampleQuestionJsonVo.class);
            }
            if (type == ExampleQuestionEnum.FULL_PAGE_QUESTION) {
                exampleQuestion = mapper.readValue(fullPageQuestionJson, ExampleQuestionJsonVo.class);
            }
            if (StringUtils.isNotBlank(exampleQuestion.getOssKey())) {
                PresignedUrlParam param = new PresignedUrlParam();
                param.setOssEnum(OssEnum.ofTypeAndBucket("aliyun", "edu-knowledge-hub"));
                param.setOssKey(exampleQuestion.getOssKey());
                return (presignedUrl(param).getData().getPresignedUrl());
            }
            return null;

        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return null;
    }

    @Override
    public MultiImageAnalyticsResultVo queryMultiImageAnalyticsResult(MultiImageAnalysicsParam param) {
        return null;
    }

    @Override
    public ExamAnalysisQuestionLabelVo examAnalysisQuestionLabel(ExamAnalysisQuestionLabelParam param) {
        //调用AI服务器
        ExamAnalysisQuestionLabelDto examAnalysisQuestionLabelDto = eduKnowledgeHubService.examAnalysisQuestionLabel(param);
        ExamAnalysisQuestionLabelVo examAnalysisQuestionLabelVo = ExamAnalysisQuestionLabelVo.builder().build();
        BeanUtils.copyProperties(examAnalysisQuestionLabelDto, examAnalysisQuestionLabelVo);
        //业务处理...
        return examAnalysisQuestionLabelVo;
    }

    @Override
    public CheckExamExsitenceVo.DataDTO checkExamExistenceV2(CheckExamExsitenceParam param) {
        //调用AI服务器
        CheckExamExsitenceDtoV2 checkExamExistenceDto = eduKnowledgeHubService.checkExamExistenceV2(param);
        if (checkExamExistenceDto == null || checkExamExistenceDto.getCode() != CommonResponse.SUCCESS.getCode()) {
            CommonResponse.assertError("试卷获取失败！");
        }

        CheckExamExsitenceDtoV2.DataDTO examData = checkExamExistenceDto.getData();
        if (examData == null) {
            CommonResponse.assertError("试卷获取失败");
        }
        CheckExamExsitenceVo.DataDTO data = new CheckExamExsitenceVo.DataDTO();
        if (!examData.isExist() || CollUtil.isEmpty(examData.getExamIds())) {
            data.setExist(false);
            return data;
        }
        //AI库相似试卷列表
        UUID examId = examData.getExamId();
        if (checkExamExistenceDto.getData().getExamName().contains("薄弱点专项训练")) {
            data.setSpecialTraining(true);
        }

        data.setExist(true);
        data.setExamId(examId);
        data.setExamSource(examData.getExamSource());
        boolean hasKnowledge = false;
        ExamHasKnowledgeDto examHasKnowledgeDto = eduKnowledgeHubService.examHasKnowledge(ExamHasKnowledgeParam.builder().examId(examId).build());
        if (examHasKnowledgeDto != null && examHasKnowledgeDto.getCode() == CommonResponse.SUCCESS.getCode()) {
            hasKnowledge = examHasKnowledgeDto.getData().isExist();
        }

        data.setKnowledgePointsExist(hasKnowledge);

        List<PersonalExam> personalExams = personalExamMapper.selectList(Wrappers.<PersonalExam>lambdaQuery()
                .eq(PersonalExam::getExamId, examId));
        if (CollUtil.isEmpty(personalExams)) {
            //试卷入库
            PersonalExam build = PersonalExam.builder()
                    .examId(examId)
                    .examName(checkExamExistenceDto.getData().getExamName())
                    .studentId(param.getStudentId())
                    .publisher(param.getPublisher())
                    .build();
            personalExamMapper.insert(build);
        }

        List<String> photos = personalExamMapper.selectExistExamPhoto(examId);
        List<String> photosUrls = new ArrayList<>();
        if (CollUtil.isNotEmpty(photos)) {
            photos.forEach(photo -> {
                PresignedUrlParam param1 = new PresignedUrlParam();
                param1.setOssEnum(OssEnum.ALIYUN_EDU_KNOWLEDGE_HUB);
                param1.setOssKey(photo);
                PresignedUrlDto presignedUrlDto = eduKnowledgeHubService.presignedUrl(param1);
                photosUrls.add(presignedUrlDto.getData().getPresignedUrl());
            });
        }
        data.setUrl(mathAnonService.getJumpUrlByExamSource(data.getExamSource(), examId));
        data.setEncodedUrl(data.getUrl());
        data.setPhotoUrls(photosUrls);
        return data;
    }

    @Override
    public void reanalyzeExamKnowledgePoint(AiAnalyticsExamParam examAnalyzeParam) {
        eduKnowledgeHubService.reanalyzeExamKnowledgePoint(examAnalyzeParam);
    }


    @Override
    public Flux<String> flexiblyGenerating(FlexiblyQuestionParam param) {
        UUID questionId = eduKnowledgeHubService.queryFlexiblyGenerating(param);
        if (DataUtils.isNotEmpty(questionId)) {
            QuestionAnswerDto questionAnswerDto = mathQuestionsMapper.selectAnswersByQuestionId(questionId);
            if (DataUtils.isNotEmpty(questionAnswerDto)) {
                return this.createStreamFromQuestionAnswerDto(questionAnswerDto);
            } else {
                return null;
            }
        } else {
            return null;
        }
    }

    @Override
    public FlexiblyGeneratedVo flexiblyGenerated(FlexiblyQuestionParam param) {
        UUID questionId = eduKnowledgeHubService.queryFlexiblyGenerating(param);
        if (DataUtils.isNotEmpty(questionId)) {
            List<FlexiblyGeneratedVo> resultList = mathQuestionsMapper.selectQuestionInfo(questionId);
            if (DataUtils.isNotEmpty(resultList)) {
                FlexiblyGeneratedVo flexiblyGeneratedVo = resultList.get(0);
                if (DataUtils.isNotEmpty(flexiblyGeneratedVo.getQuestionOssUrl())) {
                    PresignedUrlParam p = new PresignedUrlParam();
                    p.setOssEnum(OssEnum.ALIYUN_EDU_KNOWLEDGE_HUB);
                    p.setOssKey(flexiblyGeneratedVo.getQuestionOssUrl());
                    PresignedUrlDto presignedUrlDto = eduKnowledgeHubService.presignedUrl(p);
                    if (DataUtils.isNotEmpty(presignedUrlDto) && DataUtils.isNotEmpty(presignedUrlDto.getData())) {
                        flexiblyGeneratedVo.setQuestionOssUrl(presignedUrlDto.getData().getPresignedUrl());
                    }
                }
                List<KnowledgePointsVO.DataDTO.KnowledgePointsDTO> knowledgePointsList = new ArrayList<>();
                if (DataUtils.isNotEmpty(flexiblyGeneratedVo.getKnowledgePoints())) {
                    String knowledgePoints = flexiblyGeneratedVo.getKnowledgePoints();
                    String[] split = knowledgePoints.split(",");
                    for (String str : split) {
                        KnowledgePointsVO.DataDTO.KnowledgePointsDTO knowledgePointsDTO = new KnowledgePointsVO.DataDTO.KnowledgePointsDTO();
                        String[] split1 = str.split(":");
                        if (split1.length > 1) {
                            knowledgePointsDTO.setId(UUID.fromString(split1[0]));
                            knowledgePointsDTO.setName(split1[1]);
                            knowledgePointsList.add(knowledgePointsDTO);
                        }

                    }
                }
                flexiblyGeneratedVo.setKnowledgePointsList(knowledgePointsList);
                return flexiblyGeneratedVo;
            } else {
                return null;
            }
        } else {
            return null;
        }
    }

    @Override
    public KnowledgePointsVO knowledgePoints(KnowledgePointsParams param, UUID examId, PublisherEnum publisher) {
        GradeInfo gradeByStudentId = gradeService.getGradeByStudentId(param.getStudentId());
        if(examHasGradeSemester){
            param.setGrade(gradeByStudentId.getCurrentGradeLevel());
            param.setSemester(gradeByStudentId.getCurrentSemester());
        }
        param.setFromExam(param.getFromExam() != null && param.getFromExam());
        KnowledgePointsVO knowledgePointsVO = eduKnowledgeHubService.knowledgePoints(param);
        if (knowledgePointsVO == null || knowledgePointsVO.getData() == null) {
            CommonResponse.assertError("获取知识点失败");
        }
        KnowledgePointsVO.DataDTO data = knowledgePointsVO.getData();
        int difficultyInt = 0;
        String difficulty = data.getDifficulty();
        if (StrUtil.isNotBlank(difficulty)) {
            if (NumberUtil.isNumber(difficulty)) {
                difficultyInt = Integer.parseInt(difficulty);
                if (null != param.getStudyRecordQuestionId()) {
                    studyRecordQuestionMapper.updateKnowledgePoint(param.getStudyRecordQuestionId(), difficultyInt, JSONUtil.toJsonStr(data.getKnowledgePoints()));
                }
                data.setDifficulty(QuestionDifficultyType.getDesc(difficultyInt));
            }
        }

        return knowledgePointsVO;
    }

    @Override
    public CutImageFromPositionsVo cutImageFromPositions(CutImageFromPositionsParam param) {

        if (CollUtil.isEmpty(param.getPositions())) {
            CommonResponse.assertError("坐标点不能为空");
        }

        List<CutImageFromPositionsParam.PositionsDTO> positions = param.getPositions();
        if (CollUtil.isEmpty(positions)) {
            CommonResponse.assertError("坐标点不能为空");
        }

        List<CutImageFromPositionsImageVo> result = CollUtil.newArrayList();
        CutImageFromPositionsVo.CutImageFromPositionsVoBuilder builder = null;
        List<String> objectNames = new ArrayList<>();
        for (CutImageFromPositionsParam.PositionsDTO position : positions) {
            if (CollUtil.isEmpty(position.getPosition())) {
                CommonResponse.assertError("坐标点不能为空");
            }

            SingleCutImageFromPositionsParam build = SingleCutImageFromPositionsParam.builder()
                    .positions(position.getPosition())
                    .ossKey(position.getOssKey())
                    .ossEnum(param.getOssEnum())
                    .build();

            CutImageFromPositionsDto cutImageFromPositionsDto = eduKnowledgeHubService.cutImageFromPositions(build);
            if (cutImageFromPositionsDto == null || cutImageFromPositionsDto.getCode() != CommonResponse.SUCCESS.getCode()) {
                CommonResponse.assertError("调用切题服务失败");
            }

            CutImageFromPositionsDto.DataDTO data = cutImageFromPositionsDto.getData();
            if (builder == null) {
                builder = CutImageFromPositionsVo.builder()
                        .ossBucket(data.getOssBucket())
                        .ossType(data.getOssType())
                        .ossEnum(data.getOssEnum())
                        .questionId(param.getQuestionId());
            }

            CutImageFromPositionsImageVo cutImageFromPositionsImageVo = CutImageFromPositionsImageVo.builder()
                    .key(data.getKey())
                    .presignedUrl(data.getPresignedUrl())
                    .build();
            result.add(cutImageFromPositionsImageVo);

            objectNames.add(data.getKey());
        }

        // 更新学习记录坐标
        updateStudyRecordPositions(param, positions);

        studyRecordQuestionMapper.update(null,
                new LambdaUpdateWrapper<StudyRecordQuestion>()
                        .eq(StudyRecordQuestion::getId, param.getStudyRecordQuestionId())
                        .set(StudyRecordQuestion::getUpdatedAt, new Date())
                        .set(StudyRecordQuestion::getType, SingleQuestionType.CUT.name())
                        .set(StudyRecordQuestion::getOssKeys, CollUtil.join(objectNames, ",")));

        if (builder != null) {
            return builder.imagesData(result).build();
        }
        return null;
    }

    /**
     * 更新学习记录坐标
     */
    private void updateStudyRecordPositions(CutImageFromPositionsParam param, List<CutImageFromPositionsParam.PositionsDTO> positions) {
        StudyRecord studyRecord = studyRecordMapper.selectById(param.getStudyRecordId());
        List<QuestionCoordinateVo.DataDTO.SubjectsDTO> subjects = JSONUtil.toList(studyRecord.getPositions(), QuestionCoordinateVo.DataDTO.SubjectsDTO.class);
        QuestionCoordinateVo.DataDTO.SubjectsDTO subjectsDTO = subjects.get(param.getSortNo());
        List<List<QuestionCoordinateVo.DataDTO.SubjectsDTO.PosListDTO>> posList = new ArrayList<>();
        for (int i = 0; i < positions.size(); i++) {
            CutImageFromPositionsParam.PositionsDTO positionsDTO = positions.get(i);
            List<QuestionCoordinateVo.DataDTO.SubjectsDTO.PosListDTO> posListDTOList = new ArrayList<>();
            List<CoordinatePoint> position = positionsDTO.getPosition();
            for (CoordinatePoint coordinatePoint : position) {
                QuestionCoordinateVo.DataDTO.SubjectsDTO.PosListDTO posListDTO = new QuestionCoordinateVo.DataDTO.SubjectsDTO.PosListDTO();
                posListDTO.setX(coordinatePoint.getX());
                posListDTO.setY(coordinatePoint.getY());
                posListDTOList.add(posListDTO);
            }
            posList.add(posListDTOList);
        }
        subjectsDTO.setPos_list(posList);
        studyRecord.setPositions(JSONUtil.toJsonStr(subjects));
        studyRecordMapper.updateById(studyRecord);
    }

    @Override
    public OssTokenVo ossToken(OssTokenParam param) {
        OssTokenDto ossTokenDto = eduKnowledgeHubService.ossToken(param);
        if (ossTokenDto == null) {
            CommonResponse.assertError("调用AI服务器失败");
        }
        OssTokenVo ossTokenVo = OssTokenVo.builder().build();
        BeanUtil.copyProperties(ossTokenDto, ossTokenVo);
        return ossTokenVo;
    }

    @Override
    public CheckExistGraphicsVo checkExistGraphics(CheckExistGraphicsParam param) {
        CheckExistGraphicsDto checkExistGraphicsDto = eduKnowledgeHubService.checkExistGraphics(param);
        if (checkExistGraphicsDto == null) {
            CommonResponse.assertError("调用AI服务器失败");
        }
        CheckExistGraphicsVo checkExistGraphicsVo = CheckExistGraphicsVo.builder().build();
        BeanUtil.copyProperties(checkExistGraphicsDto, checkExistGraphicsVo);
        return checkExistGraphicsVo;
    }

    @Override
    public PresignedUrlVo presignedUrl(PresignedUrlParam param) {
        PresignedUrlDto presignedUrlDto = eduKnowledgeHubService.presignedUrl(param);
        if (presignedUrlDto == null) {
            CommonResponse.assertError("调用AI服务器失败");
        }
        PresignedUrlVo presignedUrlVo = PresignedUrlVo.builder().build();
        BeanUtil.copyProperties(presignedUrlDto, presignedUrlVo);
        return presignedUrlVo;
    }

    @Override
    public ExamQuestionInfoListData createExam(CreateExamParam param) {
        if (CollUtil.isEmpty(param.getObjectNames())) {
            CommonResponse.assertError("试卷图片不能为空");
        }
        //生成试卷抹除笔迹
        List<String> handWritingRemovedOssKeys = erasePenMarks(param.getObjectNames(), param.getOssEnum());
        param.setObjectNames(param.getObjectNames());
        param.setHandwritingRemovedObjectNames(handWritingRemovedOssKeys);

        GradeInfo gradeByStudentId = gradeService.getGradeByStudentId(param.getStudentId());
        param.setGrade(gradeByStudentId.getCurrentGradeLevel());
        param.setSemester(gradeByStudentId.getCurrentSemester());
        CreateExamDto exam = eduKnowledgeHubService.createExam(param);

        String examName = mathExamsMapper.getExamName(UUID.fromString(exam.getData()));
        PersonalExam personalExam = PersonalExam.builder()
                .examId(UUID.fromString(exam.getData()))
                .examName(examName)
                .studentId(param.getStudentId())
                .publisher(param.getPublisher())
                .build();
        personalExamMapper.insert(personalExam);
        if (exam != null && exam.getData() != null) {
            return personalExamService.examById(param.getStudentId(), UUID.fromString(exam.getData()),false,personalExam.getId(), param.getPublisher());
        }
        return null;
    }

    private List<String> erasePenMarks(List<String> objectNames, OssEnum ossEnum) {
        List<String> ossKeys = new ArrayList<>();
        objectNames.forEach(ossKey -> {
            EraseHandwritingFromQuestionDto eraseHandwritingFromQuestionDto = eduKnowledgeHubService.eraseHandwritingFromQuestion(EraseHandwritingFromQuestionParam.builder()
                    .ossEnum(ossEnum)
                    .ossKey(ossKey)
                    .build());
            if (eraseHandwritingFromQuestionDto == null
                    || eraseHandwritingFromQuestionDto.getCode() != CommonResponse.SUCCESS.getCode()
                    || eraseHandwritingFromQuestionDto.getData() == null
                    || StrUtil.isBlank(eraseHandwritingFromQuestionDto.getData().getKey())) {
                CommonResponse.assertError("抹除笔迹失败，请调整图片后重试");
            }

            ossKeys.add(eraseHandwritingFromQuestionDto.getData().getKey());
        });
        return ossKeys;
    }

    @Override
    public ExamAnalyzeVo examAnalyze(AiAnalyticsExamParam param) {
        return eduKnowledgeHubService.examAnalyze(param);
    }

    @Override
    public ParseAnswerVo parseAnswer(ParseAnswerParam param) {
        return eduKnowledgeHubService.parseAnswer(param);
    }

    @Override
    public ExampleQuestionJsonVo exampleQuestion(ExampleQuestionEnum type) throws JsonProcessingException {
        ObjectMapper mapper = new ObjectMapper();
        ExampleQuestionJsonVo exampleQuestion = null;
        if (type == ExampleQuestionEnum.SINGLE_QUESTION) {
            exampleQuestion = mapper.readValue(singleQuestionJson, ExampleQuestionJsonVo.class);
        }
        if (type == ExampleQuestionEnum.FULL_PAGE_QUESTION) {
            exampleQuestion = mapper.readValue(fullPageQuestionJson, ExampleQuestionJsonVo.class);
        }
        exampleQuestion.getExampleQuestionVos().forEach(exampleQuestionVo -> {
            PresignedUrlParam param1 = new PresignedUrlParam();
            param1.setOssEnum(OssEnum.ofTypeAndBucket("aliyun", "edu-knowledge-hub"));
            param1.setOssKey(exampleQuestionVo.getOssKey());
            PresignedUrlVo questionPresignedUrlVo = this.presignedUrl(param1);
            if (questionPresignedUrlVo != null && questionPresignedUrlVo.getData() != null) {
                exampleQuestionVo.setOssKey(questionPresignedUrlVo.getData().getPresignedUrl());
            }
        });
        return exampleQuestion;
    }

    @Override
    public MathExamVO getExamsByExamId(UUID examId) {
        return eduKnowledgeHubService.getExamsByExamId(examId);
    }

    @Override
    public QuestionCoordinateDto coordinatePointMulti(QueryQuestionCoordinateParam param) {
        QuestionCoordinateDto questionCoordinateDto = eduKnowledgeHubService.questionCoordinateInfo(param);
        return questionCoordinateDto;
    }

    @Override
    public List<MathExamQuestionVO> getQuestionsByExamId(UUID examId) {
        List<MathExamQuestionVO> questionsByExamId = eduKnowledgeHubService.getQuestionsByExamId(examId);
        return questionsByExamId;
    }

    @Override
    public QuestionDetailDTO editQuestion(UUID examId, UUID questionId, AddExamQuestionParam param) {
        QuestionDetailDTO questionDetailDTO = eduKnowledgeHubService.editQuestion(examId, questionId, param);
        return questionDetailDTO;
    }

    @Override
    public QuestionDeleteDto deleteQuestions(UUID examId, UUID questionId) {
        QuestionDeleteDto questiondetailDTO = eduKnowledgeHubService.deleteQuestions(examId, questionId);
        return questiondetailDTO;
    }

    @Override
    public QuestionDetailDTO addQuestion(UUID examId, AddExamQuestionParam param) {
        QuestionDetailDTO questionDetailDTO = eduKnowledgeHubService.addQuestion(examId, param);
        return questionDetailDTO;
    }
}
