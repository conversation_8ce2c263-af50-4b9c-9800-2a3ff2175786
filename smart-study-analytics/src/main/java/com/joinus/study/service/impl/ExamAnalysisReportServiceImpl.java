package com.joinus.study.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.joinus.common.model.response.CommonResponse;
import com.joinus.study.kafka.ExamSituationSendMsgSender;
import com.joinus.study.kafka.ExamSituationSendSmsSender;
import com.joinus.study.mapper.*;
import com.joinus.study.model.KnowledgePointsData;
import com.joinus.study.model.dto.CorrectRateDto;
import com.joinus.study.model.dto.ExamQuestionInfoDto;
import com.joinus.study.model.dto.GradeInfo;
import com.joinus.study.model.dto.KnowledgePointChapterDto;
import com.joinus.study.model.entity.*;
import com.joinus.study.model.enums.*;
import com.joinus.study.model.param.AiAnalyticsExamParam;
import com.joinus.study.model.param.CreateExamAnalysisReportParam;
import com.joinus.study.model.param.CreateExamAnalysisReportParam.QuestionData;
import com.joinus.study.model.param.KnowledgePointsParams;
import com.joinus.study.model.vo.*;
import com.joinus.study.service.*;
import com.joinus.study.utils.AliOssUtils;
import com.joinus.study.utils.DataUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description: 考情分析报告
 * @Author: anpy
 * @date: 2025/3/17 14:45
 */
@Slf4j
@Service
public class ExamAnalysisReportServiceImpl implements ExamAnalysisReportService {

    @Resource
    private PersonalExamQuestionMapper personalExamQuestionMapper;
    @Resource
    private ExamAnalyzeResultMapper examAnalyzeResultMapper;
    @Resource
    private MathQuestionsMapper mathQuestionsMapper;
    @Resource
    private EduKnowLedgeHubBusinessService eduKnowLedgeHubBusinessService;
    @Resource
    private QuestionKnowledgePointService knowledgePointService;
    @Resource
    private GradeService gradeService;
    @Resource
    private PersonalExamMapper personalExamMapper;
    @Resource
    private QuestionKnowledgePointService questionKnowledgePointService;

    @Autowired
    private ExamSituationSendMsgSender examSituationSendMsgSender;
    @Autowired
    private ExamSituationSendSmsSender examSituationSendSmsSender;

    @Autowired
    private MistakeBookService mistakeBookService;
    @Resource
    private QuestionKnowledgePointMapper questionKnowledgePointMapper;
    @Resource
    private AliOssUtils aliOssUtils;
    @Resource
    private AiRequestService aiRequestService;
    @Autowired
    private ClassStatisticsService classStatisticsService;
    @Autowired
    private ExamErrorCorrectionFeedbackMapper examErrorCorrectionFeedbackMapper;
    @Resource
    private MathExamsMapper mathExamsMapper;
    @Resource
    private MathActivityWeekUnitStudentMapper mathActivityWeekUnitStudentMapper;
    @Resource
    private MathActivityWeekUnitStudentHistoryMapper mathActivityWeekUnitStudentHistoryMapper;
    @Resource
    private SpecializedTrainingService specializedTrainingService;


    /**
     * 生成考情分析报告
     */
    @Override
    public void createExamAnalysisReport(CreateExamAnalysisReportParam param) {
        // 参数校验
        if (param == null || CollUtil.isEmpty(param.getExamDataList())) {
            CommonResponse.assertError("参数错误: 考试数据为空");
        }

        PersonalExamAnalysisVo personalExam = saveExamAndQuestion(param);

        AiAnalyticsExamParam.ExtraParamsDTO extraParamsDTO = AiAnalyticsExamParam.ExtraParamsDTO.builder()
                .personalExamId(personalExam.getPersonalExamId())
                .studentId(param.getStudentId())
                .analysisReportId(personalExam.getAnalyzeReportId())
                .hasExam(false)
                .parentName(param.getParentName())
                .telNumber(param.getTelNumber())
                .parentId(param.getParentId()).build();
        AiAnalyticsExamParam examAnalyzeParam = AiAnalyticsExamParam.builder()
                .examId(param.getExamId())
                .publisher(param.getPublisher())
                .extraParams(extraParamsDTO).build();
        eduKnowLedgeHubBusinessService.examAnalyze(examAnalyzeParam);
    }

    /**
     * 保存考试结果
     *
     * @param param
     * @return
     */
    public PersonalExamAnalysisVo saveExamAndQuestion(CreateExamAnalysisReportParam param) {
        MathExamsEntity mathExamsEntity = mathExamsMapper.selectById(param.getExamId());
        if (null == mathExamsEntity || null == mathExamsEntity.getSource()) {
            CommonResponse.assertError("参数错误: 试卷不存在");
        }
        //试卷入库
        Long personalExamId = param.getPersonalExamId();
        if (personalExamId == null) {
            //常规考试卷、中考真题、扫别人的专项练习卷
            PublisherEnum publisher = ExamSourceType.listTraining().contains(mathExamsEntity.getSource()) ?
                    PublisherEnum.ofCustomerName(mathExamsEntity.getPublisher()) : param.getPublisher();
            PersonalExam personalExam = PersonalExam.builder()
                    .examId(param.getExamId())
                    .examName(mathExamsMapper.getExamName(param.getExamId()))
                    .studentId(param.getStudentId())
                    .publisher(publisher)
                    .build();
            personalExamMapper.insert(personalExam);
            personalExamId = personalExam.getId();
        }
        List<ExamAnalyzeResult> examAnalyzeResults = examAnalyzeResultMapper.selectList(Wrappers.lambdaQuery(ExamAnalyzeResult.class)
                .eq(ExamAnalyzeResult::getPersonalExamId, personalExamId));
        if (CollUtil.isNotEmpty(examAnalyzeResults)) {
            List<ExamAnalyzeResult> collect = examAnalyzeResults.stream().filter(examAnalyzeResult ->
                    examAnalyzeResult.getResult() != ExamAnalyzeResultEnum.FINISHED_ERROR).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(collect)) {
                CommonResponse.assertError("考试结果已存在！");
            }
        }
        for (QuestionData q : param.getExamDataList()) {
            // 保存个人试卷做题结果
            List<PersonalExamQuestion> personalExamQuestions = personalExamQuestionMapper
                    .selectList(Wrappers.lambdaQuery(PersonalExamQuestion.class)
                            .eq(PersonalExamQuestion::getPersonalExamId, personalExamId)
                            .eq(PersonalExamQuestion::getQuestionId, q.getQuestionId()));
            if (CollUtil.isEmpty(personalExamQuestions)) {
                PersonalExamQuestion build = PersonalExamQuestion.builder()
                        .personalExamId(personalExamId)
                        .questionType(q.getQuestionType())
                        .questionId(q.getQuestionId())
                        .sortNo(q.getSortNo())
                        .result(q.getResult())
                        .build();
                // 使用自定义的插入方法，处理 UUID 类型
                personalExamQuestionMapper.insertPersonalExamQuestion(build);
            }
        }
        ExamAnalyzeResult build = ExamAnalyzeResult.builder()
                .examId(param.getExamId())
                .studentId(param.getStudentId())
                .result(ExamAnalyzeResultEnum.IN_PROGRESS)
                .personalExamId(personalExamId)
                .parentId(param.getParentId())
                .build();

        Long id;
        if (param.getExamAnalyzeResultId() != null) {
            id = param.getExamAnalyzeResultId();
            build.setId(id);
            build.setUpdatedAt(new Date());
            examAnalyzeResultMapper.updateById(build);
        } else {
            id = examAnalyzeResultMapper.insertExamAnalyzeResult(build);
        }
//        Long id = examAnalyzeResultMapper.insertExamAnalyzeResult(build);
        return PersonalExamAnalysisVo.builder()
                .personalExamId(personalExamId)
                .examId(param.getExamId())
                .studentId(param.getStudentId())
                .analyzeReportId(id)
                .build();
    }

    @Override
    public Long createExamAnalysisReport(AiAnalyticsExamParam param) {
        AiAnalyticsExamParam.ExtraParamsDTO extraParams = param.getExtraParams();
        if (param.getAutoAnalyzeSuccess() != null && !param.getAutoAnalyzeSuccess()) {
            //试题反馈入库
            ExamErrorCorrectionFeedback feedback = new ExamErrorCorrectionFeedback();
            BeanUtil.copyProperties(param, feedback);
            MathExamsEntity mathExamsEntity = mathExamsMapper.selectById(param.getExamId());
            feedback.setExamName(mathExamsEntity.getName());
            feedback.setResult(ExamErrorCorrectionFeedbackEnum.PENDING);
            feedback.setExamAnalyzeResultId(param.getExtraParams().getAnalysisReportId());
            feedback.setFeedbackType(FeedbackTypeEnum.SYSTEM_RECOGNITION);
            feedback.setParentId(extraParams.getParentId());
            feedback.setParentName(extraParams.getParentName());
            feedback.setStudentId(extraParams.getStudentId());
            feedback.setTelephoneNumber(extraParams.getTelNumber());
            examErrorCorrectionFeedbackMapper.insert(feedback);
            return null;
        }

        PersonalExam personalExam = personalExamMapper.selectById(extraParams.getPersonalExamId());
        CommonResponse.ERROR.assertNotNull(personalExam, "试卷不存在");
        MathExamsEntity mathExamsEntity = mathExamsMapper.selectById(param.getExamId());
        PublisherEnum publisher = personalExam.getPublisher();
        if (ExamSourceType.HOLIDAY_TRAINING.equals(mathExamsEntity.getSource())
                || ExamSourceType.SPECIAL_TRAINING.equals(mathExamsEntity.getSource())
                || ExamSourceType.EXAM_BLIND_SPOTS_TRAINING.equals(mathExamsEntity.getSource())) {
            publisher = PublisherEnum.ofCustomerName(mathExamsEntity.getPublisher());
        }
        LambdaQueryWrapper<PersonalExamQuestion> wrapper = Wrappers.lambdaQuery(PersonalExamQuestion.class)
                .eq(PersonalExamQuestion::getPersonalExamId, extraParams.getPersonalExamId());
        List<PersonalExamQuestion> personalExamQuestionList = personalExamQuestionMapper.selectList(wrapper);

        if (CollUtil.isEmpty(personalExamQuestionList)) {
            CommonResponse.assertError("试卷生成错误，请重新生成！");
        }

        //获取所有题目的所有知识点
        List<KnowledgePointsData> knowledgePointsDataList = saveExamResultsAndAnalyzeKnowledgePoints(personalExamQuestionList, personalExam, publisher);

        // 1. 计算考试成绩和正确率
        ExamScoreData scoreData = calculateExamScore(personalExamQuestionList);

        // 2. 按题型分类并计算各题型正确率
        List<QuestionResultData> typeStats = calculateQuestionTypeStats(personalExamQuestionList);

        // 5. 分析已掌握和未掌握的知识点
        KnowledgePointStats knowledgeStats = analyzeKnowledgePoints(knowledgePointsDataList, personalExamQuestionList, personalExam);

        // 6. 计算用户超越的百分比并保存报告
        saveExamAnalysisReport(personalExam,
                scoreData, typeStats,
                knowledgeStats,
                extraParams,
                param.getExtraParams().getAnalysisReportId(),
                param.getExtraParams().getHasExam());

        updateHolidayTrainingRecords(param);

        ActiveStudentVo activeStudentVo = classStatisticsService.getClassIdByStudentId(personalExam.getStudentId());
        // 更新班级统计数据
        updateClassStatistics(activeStudentVo.getClassId(), activeStudentVo.getGradeId(), personalExam.getExamId());

        return param.getExtraParams().getAnalysisReportId();
    }

    private void updateHolidayTrainingRecords(AiAnalyticsExamParam param) {
        //判断暑假专项训练 生成报告后更新math_activity_week_unit_student_history学生参与活动历史记录表
        //和math_activity_week_unit_student中exam_id，exam_analyze_result_id，exam_id_history字段
        try {
            log.info("updateHolidayTrainingRecords param: {}", param);
            MathExamsEntity mathExamsEntity = mathExamsMapper.selectById(param.getExamId());
            if (mathExamsEntity != null && ExamSourceType.HOLIDAY_TRAINING.equals(mathExamsEntity.getSource())) {
                Long weekUnitStudentId = mathActivityWeekUnitStudentMapper.selectIdByExamIdHistory(param.getExamId().toString(), param.getExtraParams().getStudentId());
                if (weekUnitStudentId != null) {
                    MathActivityWeekUnitStudent mathActivityWeekUnitStudent = new MathActivityWeekUnitStudent();
                    mathActivityWeekUnitStudent.setId(weekUnitStudentId);
                    mathActivityWeekUnitStudent.setExamId(param.getExamId());
                    mathActivityWeekUnitStudent.setExamAnalyzeResultId(param.getExtraParams().getAnalysisReportId());
                    mathActivityWeekUnitStudent.setUpdatedAt(new Date());
                    mathActivityWeekUnitStudent.setFinishResult(MathActivityStudentFinishEnum.FINISHIED);
                    mathActivityWeekUnitStudentMapper.updateById(mathActivityWeekUnitStudent);
                    //添加学生参与活动历史记录
                    MathActivityWeekUnitStudentHistory mathActivityWeekUnitStudentHistory = new MathActivityWeekUnitStudentHistory();
                    mathActivityWeekUnitStudentHistory.setStudentId(param.getExtraParams().getStudentId());
                    mathActivityWeekUnitStudentHistory.setWeekUnitStudentId(weekUnitStudentId);
                    mathActivityWeekUnitStudentHistory.setFinishedAt(new Date());
                    mathActivityWeekUnitStudentHistoryMapper.insert(mathActivityWeekUnitStudentHistory);
                }
            }
        } catch (Exception e) {
            log.error("更新学生参与活动历史记录失败: {}", e.getMessage(), e);
        }

    }

    /**
     * 更新班级统计数据
     */
    private void updateClassStatistics(Long classId, Long gradeId, UUID examId) {
        try {
            // 调用班级统计服务更新班级考试统计信息
            classStatisticsService.updateClassExamStatistics(
                    classId, gradeId,
                    examId);

            log.info("班级统计数据更新成功，班级ID: {}，试卷ID: {}", classId, examId);
        } catch (Exception e) {
            // 记录异常但不影响主流程
            log.error("更新班级统计数据失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 计算考试成绩和正确率
     * <p>
     * 根据考试数据计算总体正确率和成绩等级
     * 成绩等级判断标准：
     * - 正确率 >= 70%: 优秀(EXCELLENT)
     * - 正确率 >= 30% 且 < 70%: 良好(GOOD)
     * - 正确率 < 30%: 一般(FAIR)
     * </p>
     *
     * @param examDataList 考试数据列表
     * @return 考试成绩数据，包含题目总数、正确题数、正确率、格式化后的正确率字符串和成绩等级
     * @throws IllegalArgumentException 如果examDataList为null或空
     */
    private ExamScoreData calculateExamScore(List<PersonalExamQuestion> examDataList) {
        // 参数校验
        if (examDataList == null) {
            throw new IllegalArgumentException("考试数据列表不能为null");
        }
        if (examDataList.isEmpty()) {
            throw new IllegalArgumentException("考试数据列表不能为空");
        }

        // 定义成绩等级阈值常量
        final double EXCELLENT_THRESHOLD = 70.0;
        final double GOOD_THRESHOLD = 30.0;

        // 计算题目总数
        int questionNum = examDataList.size();

        // 计算正确题目数量
        long correctNum = examDataList.stream()
                .filter(q -> q != null && PersonalExamQuestionResultEnum.correct.equals(q.getResult()))
                .count();

        // 计算正确率（百分比）
        double correctRate = (double) correctNum / questionNum * 100;

        // 限制正确率不超过100%
        correctRate = Math.min(correctRate, 100.0);

        // 根据正确率判断等级：优、良、差
        OverallScoreEnum level = determineScoreLevel(correctRate, EXCELLENT_THRESHOLD, GOOD_THRESHOLD);

        // 格式化正确率为字符串，保留两位小数
        String correctRateStr = String.format("%.2f", correctRate);

        return new ExamScoreData(questionNum, (int) correctNum, correctRate, correctRateStr, level);
    }

    /**
     * 根据正确率确定成绩等级
     *
     * @param correctRate        正确率
     * @param excellentThreshold 优秀阈值
     * @param goodThreshold      良好阈值
     * @return 成绩等级
     */
    private OverallScoreEnum determineScoreLevel(double correctRate, double excellentThreshold, double goodThreshold) {
        if (correctRate >= excellentThreshold) {
            return OverallScoreEnum.EXCELLENT;
        } else if (correctRate >= goodThreshold) {
            return OverallScoreEnum.GOOD;
        } else {
            return OverallScoreEnum.FAIR;
        }
    }

    /**
     * 按题型分类并计算各题型正确率
     *
     * @param examDataList 考试数据列表
     * @return 题型统计数据
     */
    private List<QuestionResultData> calculateQuestionTypeStats(List<PersonalExamQuestion> examDataList) {
        // 按题型分类
        Map<QuestionTypeEnum, List<PersonalExamQuestion>> questionTypeMap = examDataList.stream()
                .collect(Collectors.groupingBy(PersonalExamQuestion::getQuestionType));

        // 使用枚举类中的方法获取所有题型
        QuestionTypeEnum[] types = QuestionTypeEnum.getAllTypes();

        // 结果列表
        List<QuestionResultData> resultList = new ArrayList<>();

        // 计算每种题型的数量和正确率
        for (QuestionTypeEnum type : types) {
            List<PersonalExamQuestion> questions = questionTypeMap.getOrDefault(type, new ArrayList<>());
            int count = questions.size();

            double correctRate = 0.0;
            if (count > 0) {
                long correctCount = questions.stream()
                        .filter(q -> PersonalExamQuestionResultEnum.correct.equals(q.getResult()))
                        .count();
                correctRate = (double) correctCount / count * 100;
            }

            // 创建QuestionResultData对象并添加到结果列表
            QuestionResultData resultData = QuestionResultData.builder()
                    .questionType(type)
                    .count(count)
                    .rate(correctRate)
                    .build();
            resultList.add(resultData);
        }

        // 返回统计结果列表
        return resultList;
    }

    /**
     * 打印分析结果
     *
     * @param scoreData 考试成绩数据
     * @param typeStats 题型统计数据
     */
    private void printAnalysisReport(ExamScoreData scoreData, List<QuestionResultData> typeStats) {
        System.out.println("========== 考情分析报告 ==========");
        System.out.println("1、题目总数：" + scoreData.getQuestionNum());
        System.out.println("2、考试成绩：" + scoreData.getLevel().getDesc());
        System.out.println("3、正确率：" + scoreData.getCorrectRateStr() + "%");
        for (QuestionResultData typeStat : typeStats) {
            System.out.println(typeStat.getQuestionType().name() + "：题目总数 " + typeStat.getCount() +
                    "，正确率 " + String.format("%.2f", typeStat.getRate()) + "%");
        }
        System.out.println("================================");
    }

    /**
     * 保存个人试卷做题结果并分析知识点
     *
     * @param examDataList 考试数据列表
     * @param publisher
     * @return 知识点数据列表
     */
    private List<KnowledgePointsData> saveExamResultsAndAnalyzeKnowledgePoints(List<PersonalExamQuestion> examDataList, PersonalExam param, PublisherEnum publisher) {
        List<QuestionKnowledgePoint> questionKnowledgePoints = questionKnowledgePointMapper.selectList(Wrappers.lambdaQuery(QuestionKnowledgePoint.class)
                .eq(QuestionKnowledgePoint::getExamId, param.getExamId())
                .eq(QuestionKnowledgePoint::getPublisher,  publisher)
        );
        //知识点存在，直接返回
        if (CollUtil.isNotEmpty(questionKnowledgePoints)) {
            return examHasKnowledge(questionKnowledgePoints, param);
        }

        // 并发处理每个题目
        return examNoKnowledge(examDataList, param, publisher);
    }

    /**
     * 试卷没有知识点，调用AI获取
     */
    private List<KnowledgePointsData> examNoKnowledge(List<PersonalExamQuestion> examDataList, PersonalExam param, PublisherEnum publisher) {
        //异步获取AI知识点，所有题目的所有知识点集合
        List<KnowledgePointsAndQuestionVo> allKnowledgePointsDataList = asyncGetAllAiKnowledgePoints(examDataList, param, publisher);
        log.info("parms {} allKnowledgePointsDataList {}", JSONUtil.toJsonStr(examDataList) , JSONUtil.toJsonStr(allKnowledgePointsDataList));
        //对所有的知识点集合的knowledgePointId和questionId一起去重  uniqueKnowledgePointsDataList 去重后的所有知识点集合
        List<KnowledgePointsAndQuestionVo> uniqueKnowledgePointsDataList = allKnowledgePointUniqueList(allKnowledgePointsDataList);
        log.info("uniqueKnowledgePointsDataList {}", JSONUtil.toJsonStr(uniqueKnowledgePointsDataList));
        //将去重后的知识点封装，添加到需要计算掌握度的集合中，最终返回
        List<KnowledgePointsData> calculateKnowledgePointGraspList = addUniqueKnowledgePointsDataList(uniqueKnowledgePointsDataList);
        //试卷知识点入库
        saveExamKnowledgePoints(param, uniqueKnowledgePointsDataList, publisher);
        return calculateKnowledgePointGraspList;
    }

    /**
     * 试卷知识点入库
     */
    private void saveExamKnowledgePoints(PersonalExam param, List<KnowledgePointsAndQuestionVo> uniqueKnowledgePointsDataList, PublisherEnum publisher) {
        //批量入库的数据
        List<QuestionKnowledgePoint> saveKnowledgePointList = Collections.synchronizedList(new ArrayList<>());
        uniqueKnowledgePointsDataList.forEach(knowledgePoint -> {
            QuestionKnowledgePoint questionKnowledgePoint = QuestionKnowledgePoint.builder()
                    .knowledgePointId(knowledgePoint.getKnowledgePointId())
                    .knowledgePointName(knowledgePoint.getKnowledgePointName())
                    .examId(param.getExamId())
                    .publisher(publisher)
                    .questionId(knowledgePoint.getQuestionId())
                    .build();
            saveKnowledgePointList.add(questionKnowledgePoint);
        });

        // 知识点入库
        if (CollUtil.isNotEmpty(saveKnowledgePointList)) {
            log.info("试卷分析->{},学生：{}, 入库知识点:{}条", param.getExamId(), param.getStudentId(), saveKnowledgePointList.size());
            questionKnowledgePointService.saveBatch(saveKnowledgePointList);
        }
    }

    private List<KnowledgePointsData> addUniqueKnowledgePointsDataList(List<KnowledgePointsAndQuestionVo> uniqueKnowledgePointsDataList) {
        List<KnowledgePointsData> calculateKnowledgePointGraspList = Collections.synchronizedList(new ArrayList<>());
        //根据questionId分组封装数据
        uniqueKnowledgePointsDataList.stream()
                .collect(Collectors.groupingBy(KnowledgePointsAndQuestionVo::getQuestionId))
                .forEach((questionId, knowledgePoints) -> {
                    List<KnowledgePointsVO.DataDTO.KnowledgePointsDTO> list = new ArrayList<>();
                    knowledgePoints.forEach(knowledgePoint -> {
                        KnowledgePointsVO.DataDTO.KnowledgePointsDTO knowledgePointsDTO = new KnowledgePointsVO.DataDTO.KnowledgePointsDTO();
                        knowledgePointsDTO.setId(knowledgePoint.getKnowledgePointId());
                        knowledgePointsDTO.setName(knowledgePoint.getKnowledgePointName());
                        list.add(knowledgePointsDTO);
                    });

                    KnowledgePointsData build = KnowledgePointsData.builder()
                            .questionId(questionId)
                            .knowledgePoints(list)
                            .build();
                    calculateKnowledgePointGraspList.add(build);
                });
        return calculateKnowledgePointGraspList;
    }

    /**
     * 对所有的知识点集合的knowledgePointId和questionId一起去重
     */
    private static List<KnowledgePointsAndQuestionVo> allKnowledgePointUniqueList(List<KnowledgePointsAndQuestionVo> allKnowledgePointsDataList) {
        return allKnowledgePointsDataList.stream()
                .collect(Collectors.collectingAndThen(
                        Collectors.toMap(
                                point -> new AbstractMap.SimpleEntry<>(point.getKnowledgePointId(), point.getQuestionId()),
                                point -> point,
                                (existing, replacement) -> existing
                        ),
                        map -> new ArrayList<>(map.values())
                ));
    }

    /**
     * 异步获取AI知识点
     */
    private List<KnowledgePointsAndQuestionVo> asyncGetAllAiKnowledgePoints(List<PersonalExamQuestion> examDataList, PersonalExam param, PublisherEnum publisher) {
        // 创建一个线程池
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        // 使用线程安全的集合保存结果数据
        List<KnowledgePointsAndQuestionVo> allKnowledgePointsDataList = Collections.synchronizedList(new ArrayList<>());

        for (PersonalExamQuestion q : examDataList) {
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                try {
                    // 分析题目知识点
                    KnowledgePointsParams knowledgePointsParams = KnowledgePointsParams
                            .builder()
                            .ossEnum(OssEnum.ALIYUN_EDU_KNOWLEDGE_HUB)
                            .questionId(q.getQuestionId())
                            .publisher(publisher)
                            .fromExam(true)
                            .studentId(param.getStudentId())
                            .build();
                    //获取每道题的知识点
                    KnowledgePointsVO knowledgePointsAndDifficultyDTO = eduKnowLedgeHubBusinessService.knowledgePoints(knowledgePointsParams, param.getExamId(), publisher);
                    if (knowledgePointsAndDifficultyDTO == null || CollUtil.isEmpty(knowledgePointsAndDifficultyDTO.getData().getKnowledgePoints())) {
                        return;
                    }
                    List<KnowledgePointsVO.DataDTO.KnowledgePointsDTO> knowledgePoints = knowledgePointsAndDifficultyDTO.getData().getKnowledgePoints();
                    knowledgePoints.forEach(knowledgePoint -> {
                        KnowledgePointsAndQuestionVo build = KnowledgePointsAndQuestionVo.builder()
                                .questionId(q.getQuestionId())
                                .knowledgePointId(knowledgePoint.getId())
                                .knowledgePointName(knowledgePoint.getName())
                                .build();
                        allKnowledgePointsDataList.add(build);
                    });
                    log.info("试卷分析->{}, questionId: {} 获取知识点:{}条", param.getExamId(), q.getQuestionId(), knowledgePoints.size());
                } catch (Exception e) {
                    // 记录异常信息，但不中断处理流程
                    System.err.println("Error inserting question data: " + e.getMessage());
                }
            });
            futures.add(future);
        }

        // 等待所有线程完成
        try {
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).get();
        } catch (Exception e) {
            // 记录异常信息
            Thread.currentThread().interrupt();
        }


        return allKnowledgePointsDataList;
    }

    /**
     * 试卷已有知识点，从本地获取
     */
    private List<KnowledgePointsData> examHasKnowledge(List<QuestionKnowledgePoint> questionKnowledgePoints, PersonalExam param) {
        List<KnowledgePointsData> knowledgePointsDataList = new ArrayList<>();
        questionKnowledgePoints.stream()
                .collect(Collectors.groupingBy(QuestionKnowledgePoint::getQuestionId))
                .forEach((questionId, knowledgePoints) -> {
                    List<KnowledgePointsVO.DataDTO.KnowledgePointsDTO> list = new ArrayList<>();
                    knowledgePoints.forEach(knowledgePoint -> {
                        KnowledgePointsVO.DataDTO.KnowledgePointsDTO knowledgePointsDTO = new KnowledgePointsVO.DataDTO.KnowledgePointsDTO();
                        knowledgePointsDTO.setId(knowledgePoint.getKnowledgePointId());
                        knowledgePointsDTO.setName(knowledgePoint.getKnowledgePointName());
                        list.add(knowledgePointsDTO);
                    });

                    KnowledgePointsData build = KnowledgePointsData.builder()
                            .questionId(questionId)
                            .knowledgePoints(list)
                            .build();
                    knowledgePointsDataList.add(build);
                });
        log.info("试卷分析->{},学生：{},知识点已存在:{}条", param.getExamId(), param.getStudentId(), knowledgePointsDataList.size());
        return knowledgePointsDataList;
    }

    /**
     * 分析已掌握和未掌握的知识点
     *
     * @param knowledgePointsDataList 知识点数据列表
     * @param examDataList            考试数据列表
     * @param param
     * @return 知识点统计数据
     */
    private KnowledgePointStats analyzeKnowledgePoints(List<KnowledgePointsData> knowledgePointsDataList, List<PersonalExamQuestion> examDataList, PersonalExam param) {
        // 创建两个集合分别存储已掌握和未掌握的知识点
        List<KnowledgePointsVO.DataDTO.KnowledgePointsDTO> acquireKnowledgePoints = new ArrayList<>();
        List<KnowledgePointsVO.DataDTO.KnowledgePointsDTO> notAcquireKnowledgePoints = new ArrayList<>();

        // 创建一个Map用于跟踪每个知识点的状态
        // key: 知识点ID, value: 是否所有相关题目都正确 (true: 全部正确, false: 有错误)
        Map<UUID, Boolean> knowledgePointStatusMap = new HashMap<>();

        // 遍历知识点数据列表，根据题目的正确与否分类知识点
        if (CollUtil.isNotEmpty(knowledgePointsDataList) && CollUtil.isNotEmpty(examDataList)) {
            // 创建一个Map用于快速查找题目的结果状态
            Map<UUID, PersonalExamQuestionResultEnum> questionResultMap = examDataList.stream()
                    .collect(Collectors.toMap(
                            PersonalExamQuestion::getQuestionId,
                            PersonalExamQuestion::getResult,
                            (r1, r2) -> r1));

            // 创建一个Map用于存储每个知识点的实例，以便后续添加到结果列表中
            Map<UUID, KnowledgePointsVO.DataDTO.KnowledgePointsDTO> knowledgePointMap = new HashMap<>();

            // 遍历每个知识点数据，根据题目的正确与否更新知识点状态
            knowledgePointsDataList.forEach(knowledgePointsData -> {
                UUID questionId = knowledgePointsData.getQuestionId();
                List<KnowledgePointsVO.DataDTO.KnowledgePointsDTO> knowledgePoints = knowledgePointsData.getKnowledgePoints();

                // 获取题目的结果状态
                PersonalExamQuestionResultEnum result = questionResultMap.get(questionId);

                if (result != null && CollUtil.isNotEmpty(knowledgePoints)) {
                    // 遍历该题目的所有知识点
                    for (KnowledgePointsVO.DataDTO.KnowledgePointsDTO point : knowledgePoints) {
                        // 存储知识点实例
                        knowledgePointMap.putIfAbsent(point.getId(), point);

                        // 更新知识点状态
                        boolean isCorrect = PersonalExamQuestionResultEnum.correct.equals(result);
                        if (knowledgePointStatusMap.containsKey(point.getId())) {
                            // 如果知识点已存在于状态Map中，只有当前题和之前的题都正确时，状态才保持为true
                            knowledgePointStatusMap.put(point.getId(),
                                    knowledgePointStatusMap.get(point.getId()) && isCorrect);
                        } else {
                            // 初次添加知识点到状态Map
                            knowledgePointStatusMap.put(point.getId(), isCorrect);
                        }
                    }
                }
            });

            // 根据状态Map分类知识点
            knowledgePointStatusMap.forEach((id, allCorrect) -> {
                KnowledgePointsVO.DataDTO.KnowledgePointsDTO point = knowledgePointMap.get(id);
                if (point != null) {
                    if (allCorrect) {
                        // 所有相关题目都正确，添加到已掌握集合
                        acquireKnowledgePoints.add(point);
                    } else {
                        // 有题目错误，添加到未掌握集合
                        notAcquireKnowledgePoints.add(point);
                    }
                }
            });
        }

        log.info("试卷分析-> :{},学生：{}, 知识点已掌握:{}条，未掌握:{}条", param.getExamId(), param.getStudentId(), acquireKnowledgePoints.size(), notAcquireKnowledgePoints.size());
        return new KnowledgePointStats(
                acquireKnowledgePoints,
                notAcquireKnowledgePoints
        );
    }

    /**
     * 计算用户超越的百分比并保存报告
     *
     * @param param            创建考情分析报告参数
     * @param scoreData        考试成绩数据
     * @param typeStats        题型统计数据
     * @param knowledgeStats   知识点统计数据
     * @param analysisReportId
     * @param hasExam
     * @return
     */
    private void saveExamAnalysisReport(PersonalExam param, ExamScoreData scoreData,
                                        List<QuestionResultData> typeStats, KnowledgePointStats knowledgeStats,
                                        AiAnalyticsExamParam.ExtraParamsDTO extraParams, Long analysisReportId, Boolean hasExam) {
        BigDecimal percentile;
        // 计算用户超越的百分比
        percentile = examAnalyzeResultMapper.calculatePercentile(param.getExamId(), scoreData.getCorrectRateStr(), param.getStudentId());

        // 构建正确率JSON对象
        JSONObject jsonObject = new JSONObject();
        // 将percentile也格式化为整数，并添加百分号
        jsonObject.set("percentile", String.format("%d", (int) Double.parseDouble(scoreData.getCorrectRateStr())) + "%");
        // 只有当题型的题目数量大于0时才添加到jsonObject中，并且将百分比格式化为整数
        for (QuestionResultData typeStat : typeStats) {
            if (typeStat.getCount() > 0) {
                jsonObject.set(typeStat.getQuestionType().name(), String.format("%d", typeStat.getRate().intValue()) + "%");
            }
        }
        // 报告入库
        ExamAnalyzeResult examAnalyzeResult = ExamAnalyzeResult.builder()
                .examId(param.getExamId())
                .studentId(param.getStudentId())
                .overallScore(scoreData.getLevel())
                .correctRate(jsonObject.toString())
                .percentile(percentile)
                .totalKnowledgePoints(knowledgeStats.getUniqueAcquireKnowledgePoints().size() +
                        knowledgeStats.getUniqueNotAcquireKnowledgePoints().size())
                .masteredKnowledgePoints(knowledgeStats.getUniqueAcquireKnowledgePoints().size())
                .weakKnowledgePoints(knowledgeStats.getUniqueNotAcquireKnowledgePoints().size())
                .result(ExamAnalyzeResultEnum.FINISHED)
                .build();
        examAnalyzeResult.setId(analysisReportId);
        examAnalyzeResult.setUpdatedAt(new Date());
        examAnalyzeResultMapper.updateById(examAnalyzeResult);

        // 打印超越百分比信息
        System.out.println("您已经超越 " + percentile + "% 的用户，继续加油哦。");
        if (hasExam) {
            return;
        }
        //=====================================
        /*
         * message.put("content","")内容
         * message.put("messageTitle","")标题
         * message.put("bizId","")业务ID（分析报告表exam_analyze_result.ID）
         * message.put("studentId","")
         * */
        Map<String, String> msgMap = new HashMap<>();
        msgMap.put("messageTitle", "考试分析报告已生成");
        msgMap.put("content", "点击查看失分知识点，开启薄弱知识精准专项练习");
        msgMap.put("bizId", String.valueOf(analysisReportId)); // 其他键值对
        msgMap.put("jumpUrlType", "1");
        msgMap.put("jumpUrl", "https://cdn-ali-static.ijx.ink/h5-learning-tools/index.html#/mathEnter?type=notice");
        msgMap.put("studentId", String.valueOf(extraParams.getStudentId())); // 其他键值对
        examSituationSendMsgSender.sendMessageToQylESMsg(msgMap);
        Map<String, String> smsMap = new HashMap<>();
        smsMap.put("studentId", String.valueOf(extraParams.getStudentId()));
        smsMap.put("parentId", String.valueOf(extraParams.getParentId()));
        smsMap.put("reportId", String.valueOf(analysisReportId));
        examSituationSendSmsSender.sendMessageToQylESSms(smsMap);
    }

    /**
     * 考试成绩数据类
     */
    private static class ExamScoreData {
        private final int questionNum;
        private final int correctNum;
        private final double correctRate;
        private final String correctRateStr;
        private final OverallScoreEnum level;

        public ExamScoreData(int questionNum, int correctNum, double correctRate, String correctRateStr, OverallScoreEnum level) {
            this.questionNum = questionNum;
            this.correctNum = correctNum;
            this.correctRate = correctRate;
            this.correctRateStr = correctRateStr;
            this.level = level;
        }

        public int getQuestionNum() {
            return questionNum;
        }

        public int getCorrectNum() {
            return correctNum;
        }

        public double getCorrectRate() {
            return correctRate;
        }

        public String getCorrectRateStr() {
            return correctRateStr;
        }

        public OverallScoreEnum getLevel() {
            return level;
        }
    }

    /**
     * 知识点统计数据类
     */
    public static class KnowledgePointStats {
        private final List<KnowledgePointsVO.DataDTO.KnowledgePointsDTO> uniqueAcquireKnowledgePoints;
        private final List<KnowledgePointsVO.DataDTO.KnowledgePointsDTO> uniqueNotAcquireKnowledgePoints;

        public KnowledgePointStats(List<KnowledgePointsVO.DataDTO.KnowledgePointsDTO> uniqueAcquireKnowledgePoints, List<KnowledgePointsVO.DataDTO.KnowledgePointsDTO> uniqueNotAcquireKnowledgePoints) {
            this.uniqueAcquireKnowledgePoints = uniqueAcquireKnowledgePoints;
            this.uniqueNotAcquireKnowledgePoints = uniqueNotAcquireKnowledgePoints;
        }

        public List<KnowledgePointsVO.DataDTO.KnowledgePointsDTO> getUniqueAcquireKnowledgePoints() {
            return uniqueAcquireKnowledgePoints;
        }

        public List<KnowledgePointsVO.DataDTO.KnowledgePointsDTO> getUniqueNotAcquireKnowledgePoints() {
            return uniqueNotAcquireKnowledgePoints;
        }
    }

    @Override
    public ExamAnalysisReportVo getExamAnalysisReport(Long id) {
        log.info("当前起始时间1:" + System.currentTimeMillis());
        ExamAnalyzeResult analyzeResult = examAnalyzeResultMapper.selectById(id);
        CommonResponse.ERROR.assertNotNull(analyzeResult, "请选择正确考试记录！");
        UUID examId = analyzeResult.getExamId();
        Long studentId = Long.parseLong(String.valueOf(analyzeResult.getStudentId()));
        analyzeResult.setId(id);
        // 从数据库查询考情分析报告
        ExamAnalysisReportVo result = examAnalyzeResultMapper.selectExamAnalyzeResultByExamId(analyzeResult.getPersonalExamId());
        MathActivityWeekUnitTypeEnum holidayExamType = null;
        if (ExamSourceType.HOLIDAY_TRAINING == result.getSource()) {
            MathActivityWeekUnit mathActivityWeekUnit = mathActivityWeekUnitStudentMapper.getMathActivityWeekUnitByTrainingExamId(examId);
            holidayExamType = null != mathActivityWeekUnit ? mathActivityWeekUnit.getType() : null;
        }
        result.setHolidayExamType(holidayExamType);

        log.info("执行过直接从表查询2:" + System.currentTimeMillis());
        result.setId(id);
        result.setStudentId(studentId);
        result.setCreatedAt(analyzeResult.getCreatedAt());
        result.setPersonalExamId(analyzeResult.getPersonalExamId());
        //做此套题的人数
        Integer testPapersCount = examAnalyzeResultMapper.selectTestPapersCount(examId);
        log.info("查询做题人数3:" + System.currentTimeMillis());
        result.setTestPapersCount(testPapersCount);
        log.info("执行过试卷处理4:" + System.currentTimeMillis());
        String correctRate = result.getCorrectRate();
        if (StringUtils.isNotBlank(correctRate)) {
            ObjectMapper mapper = new ObjectMapper();
            try {
                CorrectRateDto testData = mapper.readValue(correctRate, CorrectRateDto.class);
                result.setCorrectRates(testData);
            } catch (Exception e) {
                log.error(e.getMessage());
            }
        }
        List<KnowledgePointStatisticsVo> knowledgePointStatisticsPos = knowledgePointService.selectKnowledgePointStatisticsByExamId(analyzeResult.getPersonalExamId());
        log.info("执行过知识点列表查询处理5:" + System.currentTimeMillis());
        List<StudentInfoVo> students = knowledgePointService.getStudentIds(studentId);
        List<Long> studentIds = students.stream().map(StudentInfoVo::getStudentId).collect(Collectors.toList());
        List<Long> classIds = students.stream().map(StudentInfoVo::getClassId).collect(Collectors.toList());
        List<KnowledgePointStatisticsVo> classKnowledgePointStatics = knowledgePointService.getClassKnowledgePointStatics(studentIds, classIds, examId);
        log.info("执行过知识点列表查询处理5:" + System.currentTimeMillis());
        //根据学生id 和试卷id 查询做过该试卷的学生总人数
        if (CollectionUtil.isNotEmpty(knowledgePointStatisticsPos)) {
            knowledgePointStatisticsPos.forEach(statisticsVo -> {
                Integer myMasteredCount = statisticsVo.getTotalCorrectCount();
                Integer myTotalKnowledgePoints = statisticsVo.getTotalQuestionCount();
                KnowledgePointStatisticsVo classTotal = classKnowledgePointStatics.stream().filter(vo -> vo.getKnowledgePointId().equals(statisticsVo.getKnowledgePointId())).findFirst().orElse(null);
                Integer classMasteredCount = classTotal.getTotalCorrectCount();
                myMasteredCount = myMasteredCount == null ? 0 : myMasteredCount;
                Integer classTotalKnowledgePoints = classTotal.getTotalQuestionCount();
                myMasteredCount = myMasteredCount == null ? 0 : myMasteredCount;
                classMasteredCount = classMasteredCount == null ? 0 : classMasteredCount;
                classTotalKnowledgePoints = classTotalKnowledgePoints == null ? 0 : classTotalKnowledgePoints;
                myTotalKnowledgePoints = myTotalKnowledgePoints == null ? 0 : myTotalKnowledgePoints;
                statisticsVo.setMyNotMasteredCount(myMasteredCount);
                //计算百分比字符串保留两位小数
                String classMasteredCountPercent;
                String myMasteredCountPercent;
                if (classTotalKnowledgePoints == 0 || classMasteredCount == 0) {
                    classMasteredCountPercent = "0%";
                } else {
                    classMasteredCountPercent = String.format("%d%%", Math.round((double) classMasteredCount / classTotalKnowledgePoints * 100));
                }
                if (classTotalKnowledgePoints == 0 || myMasteredCount == 0) {
                    myMasteredCountPercent = "0%";
                } else {
                    myMasteredCountPercent = String.format("%d%%", Math.round((double) myMasteredCount / myTotalKnowledgePoints * 100));

                }
                statisticsVo.setMyMasteredCountPercent(myMasteredCountPercent);
                statisticsVo.setClassMasteredCountPercent(classMasteredCountPercent);
            });
        }
        log.info("执行过知识点掌握度计算处理6:" + System.currentTimeMillis());

        // 使用自定义比较器按百分比大小排序
        Collections.sort(knowledgePointStatisticsPos, new Comparator<KnowledgePointStatisticsVo>() {
            @Override
            public int compare(KnowledgePointStatisticsVo o1, KnowledgePointStatisticsVo o2) {
                int percent1 = Integer.parseInt(o1.getMyMasteredCountPercent().replace("%", ""));
                int percent2 = Integer.parseInt(o2.getMyMasteredCountPercent().replace("%", ""));
                return Integer.compare(percent1, percent2);
            }
        });
        result.setKnowledgePointStatisticsPos(knowledgePointStatisticsPos);
        log.info("执行过排序知识点处理7:" + System.currentTimeMillis());
        //查询是否已经加入错题
        List<MistakeBook> isAddMistakesBook = mistakeBookService.getIsAddMistakesBook(studentId, null, analyzeResult.getPersonalExamId(), MistakeBookSourceEnum.exam_diagnosis);
        result.setIsAddMistakesBook(isAddMistakesBook.size() > 0 ? 1 : 0);
        log.info("执行过是否加入过错题本处理8:" + System.currentTimeMillis());
        return result;
    }

    @Override
    public Long createExamAnalysisReportHasExam(CreateExamAnalysisReportParam param) {
        PersonalExamAnalysisVo personalExam = saveExamAndQuestion(param);
        AiAnalyticsExamParam.ExtraParamsDTO extraParamsDTO = AiAnalyticsExamParam
                .ExtraParamsDTO.builder()
                .personalExamId(personalExam.getPersonalExamId())
                .analysisReportId(personalExam.getAnalyzeReportId())
                .studentId(param.getStudentId())
                .hasExam(true)
                .build();
        AiAnalyticsExamParam aiAnalyticsExamParam = AiAnalyticsExamParam
                .builder()
                .examId(personalExam.getExamId())
                .extraParams(extraParamsDTO)
                .publisher(param.getPublisher())
                .build();
        return this.createExamAnalysisReport(aiAnalyticsExamParam);
    }

    @Override
    public List<ExamQuestionFileVo> getExamknowledgePointQuestions(Long personalExamId, String knowledgePointId) {
        List<ExamQuestionFileVo> examknowledgePointQuestions = personalExamQuestionMapper.selectQuestionInfoByknowledgePointId(personalExamId, UUID.fromString(knowledgePointId));
        examknowledgePointQuestions.stream().forEach(item -> {
            item.setQuestionContent(specializedTrainingService.decodeContentV2(item.getQuestionContent()));
            if (StringUtils.isNotBlank(item.getQuestionOssUrl())) {
                String ossUrl = "";
                String[] OssUrl = item.getQuestionOssUrl().split(",");
                for (String s : OssUrl) {
                    if (ossUrl.contains(s)) {
                        continue;
                    }
                    ossUrl += aliOssUtils.generatePresignedUrl(s) + ",";
                }
                item.setQuestionOssUrl(ossUrl.substring(0, ossUrl.length() - 1));
            }
        });
        return examknowledgePointQuestions;
    }

    @Override
    public ExamAnalysisReportPdfVo getExamAnalysisReportPdfInfo(Long id, String publisher) {
        ExamAnalysisReportVo report = this.getExamAnalysisReport(id);
        if (report != null) {
            GradeInfo grade = gradeService.getGradeByStudentId(report.getStudentId());
            ExamAnalysisReportPdfVo reportPdfVo = new ExamAnalysisReportPdfVo();
            Map<String, Object> studentInfo = examAnalyzeResultMapper.getStudentInfo(report.getStudentId());
            reportPdfVo.setGradeName(studentInfo.get("gradeName").toString());
            reportPdfVo.setCurrentSemester(grade.getCurrentSemester() == 2 ? "下册" : "上册");
            reportPdfVo.setStudentName(studentInfo.get("studentName").toString());
            reportPdfVo.setSchoolName(studentInfo.get("schoolName").toString());
            BeanUtils.copyProperties(report, reportPdfVo);
            reportPdfVo.setCreateTime(report.getCreatedAt());
            ClassExamStatistics classExamStatisticsData = classStatisticsService.getClassExamStatistics((Long) studentInfo.get("classId"), report.getExamId());
            if (classExamStatisticsData == null || classExamStatisticsData.getStudentCount() < 10) {
                reportPdfVo.setPercentile(null);
            }
            List<ExamAnalysisReportPdfVo.KnowledgePointChapterVo> knowledgePointChapterVos = new ArrayList<>();
            if (CollectionUtil.isNotEmpty(report.getKnowledgePointStatisticsPos())) {
                String points = report.getKnowledgePointStatisticsPos().stream().map(KnowledgePointStatisticsVo::getKnowledgePointId).collect(Collectors.toList()).stream()
                        .filter(uuid -> uuid != null)
                        .map(UUID::toString)
                        .collect(Collectors.joining(","));
                List<KnowledgePointChapterDto> chapterDtos = aiRequestService.mathKnowledgePointsChapter(publisher, grade.getCurrentGradeLevel(), grade.getCurrentSemester(), points);
                if (!CollectionUtil.isEmpty(chapterDtos)) {
                    List<String> chapterIds = new ArrayList<>();
                    for (KnowledgePointChapterDto chapterDto : chapterDtos) {
                        if (!chapterIds.contains(chapterDto.getChapterId())) {
                            chapterIds.add(chapterDto.getChapterId());
                        }
                    }
                    reportPdfVo.setTotalChapter(chapterIds.size());
                    Map<String, List<KnowledgePointChapterDto>> map = chapterDtos.stream()
                            .collect(Collectors.groupingBy(
                                    chapterDto -> chapterDto.getSectionId()
                            ));
                    for (Map.Entry<String, List<KnowledgePointChapterDto>> entry : map.entrySet()) {
                        ExamAnalysisReportPdfVo.KnowledgePointChapterVo knowledgePointChapterVo = new ExamAnalysisReportPdfVo.KnowledgePointChapterVo();
                        knowledgePointChapterVo.setChapterId(entry.getValue().get(0).getChapterId());
                        knowledgePointChapterVo.setChapterName(entry.getValue().get(0).getChapterName());
                        knowledgePointChapterVo.setSectionId(entry.getValue().get(0).getSectionId());
                        knowledgePointChapterVo.setSectionName(entry.getValue().get(0).getSectionName());
                        List<String> pointList = entry.getValue().stream().map(KnowledgePointChapterDto::getKnowledgePointId).collect(Collectors.toList());
                        List<KnowledgePointStatisticsVo> statisticsPos =
                                report.getKnowledgePointStatisticsPos().stream()
                                        .filter(knowledgePointStatisticsVo ->
                                                pointList.contains(knowledgePointStatisticsVo.getKnowledgePointId().toString()))
                                        .collect(Collectors.toList());
                        statisticsPos.stream().forEach(po -> {
                            String questionNos = po.getQuestionNos();
                            String replaced = questionNos.replaceAll(QuestionTypeEnum.MULTIPLE_CHOICE.name(), "选择题")
                                    .replaceAll(QuestionTypeEnum.TRUE_FALSE.name(), "判断题")
                                    .replaceAll(QuestionTypeEnum.FILL_IN_THE_BLANK.name(), "填空题")
                                    .replaceAll(QuestionTypeEnum.CALCULATION.name(), "计算题")
                                    .replaceAll(QuestionTypeEnum.PROOF.name(), "证明题")
                                    .replaceAll(QuestionTypeEnum.APPLICATION.name(), "应用题")
                                    .replaceAll(QuestionTypeEnum.PROBLEM_SOLVING.name(), "解答题")
                                    .replaceAll(QuestionTypeEnum.OTHER.name(), "其他");
                            po.setQuestionNos(replaced);
                        });
                        knowledgePointChapterVo.setKnowledgePointStatisticsPos(statisticsPos);
                        knowledgePointChapterVos.add(knowledgePointChapterVo);
                    }
                    knowledgePointChapterVos.sort(Comparator.comparing(ExamAnalysisReportPdfVo.KnowledgePointChapterVo::getSectionId));
                }
            }
            reportPdfVo.setKnowledgePointChapterVos(knowledgePointChapterVos);
            //处理各类型
            if (report.getCorrectRates() != null) {
                List<ExamAnalysisReportPdfVo2.QuestionTypeVo> questionTypeVos = this.analyzeQuestionTypeData(report, report.getCorrectRates(), false);
                reportPdfVo.setCorrectRate(report.getCorrectRates().getPercentile());
                reportPdfVo.setCorrectRates(questionTypeVos);
                // 错题数
                reportPdfVo.setErrorQuestions((int) Math.round(report.getTotalQuestions() * (1 - Double.parseDouble("0." + report.getCorrectRates().getPercentile().replace("%", "")))));
            }
            return reportPdfVo;
        }
        return null;
    }

    /**
     * 添加知识点数据到列表
     */
    private static void addKnowledgePointsDataList(List<KnowledgePointsData> knowledgePointsDataList,
                                                   List<KnowledgePointsAndQuestionVo> uniqueKnowledgePointsDataList) {
        if (knowledgePointsDataList == null || uniqueKnowledgePointsDataList == null) {
            return;
        }

        uniqueKnowledgePointsDataList.stream()
                .filter(point -> point != null && point.getQuestionId() != null)
                .collect(Collectors.groupingBy(KnowledgePointsAndQuestionVo::getQuestionId))
                .forEach((questionId, knowledgePoints) -> {
                    List<KnowledgePointsVO.DataDTO.KnowledgePointsDTO> list = new ArrayList<>();
                    knowledgePoints.stream()
                            .filter(point -> point != null && point.getKnowledgePointId() != null)
                            .forEach(knowledgePoint -> {
                                KnowledgePointsVO.DataDTO.KnowledgePointsDTO knowledgePointsDTO = new KnowledgePointsVO.DataDTO.KnowledgePointsDTO();
                                knowledgePointsDTO.setId(knowledgePoint.getKnowledgePointId());
                                knowledgePointsDTO.setName(StringUtils.trimToEmpty(knowledgePoint.getKnowledgePointName()));
                                list.add(knowledgePointsDTO);
                            });

                    if (CollUtil.isNotEmpty(list)) {
                        KnowledgePointsData build = KnowledgePointsData.builder()
                                .questionId(questionId)
                                .knowledgePoints(list)
                                .build();
                        knowledgePointsDataList.add(build);
                    }
                });
    }


    @Override
    public ExamAnalysisReportPdfVo2 getExamAnalysisReportPdfInfoV2(Long id) {

        ExamAnalyzeResult analyzeResult = examAnalyzeResultMapper.selectById(id);
        CommonResponse.ERROR.assertNotNull(analyzeResult, "请选择正确考试记录！");

        ExamAnalysisReportPdfVo2 reportPdfVo = new ExamAnalysisReportPdfVo2();
        reportPdfVo.setExamId(analyzeResult.getExamId());
        reportPdfVo.setId(id);
        reportPdfVo.setCreateTime(analyzeResult.getCreatedAt());
        // 从数据库查询考情分析报告
        ExamAnalysisReportVo result = examAnalyzeResultMapper.selectExamAnalyzeResultByExamId(analyzeResult.getPersonalExamId());
        PublisherEnum publisherEnum = PublisherEnum.BEI_SHI_DA;
        if(StrUtil.isNotBlank(result.getPublisher())){
            publisherEnum = PublisherEnum.ofCustomerName(result.getPublisher());
        }
        reportPdfVo.setName(result.getName());
        reportPdfVo.setTotalQuestions(result.getTotalQuestions());
        reportPdfVo.setPublisher(publisherEnum.getDescription());
        reportPdfVo.setOverallScore(OverallScoreEnum.valueOf(result.getOverallScore().name()));
        reportPdfVo.setTotalKnowledgePoints(result.getTotalKnowledgePoints());
        reportPdfVo.setMasteredKnowledgePoints(result.getMasteredKnowledgePoints());
        reportPdfVo.setWeakKnowledgePoints(result.getWeakKnowledgePoints());
        String correctRate = result.getCorrectRate();
        if (StringUtils.isNotBlank(correctRate)) {
            try {
                ObjectMapper objectMapper = new ObjectMapper();
                CorrectRateDto rateDto = objectMapper.readValue(correctRate, CorrectRateDto.class);
                List<ExamAnalysisReportPdfVo2.QuestionTypeVo> questionTypeVos = this.analyzeQuestionTypeData(result, rateDto, true);
                reportPdfVo.setCorrectRates(questionTypeVos);
                reportPdfVo.setPercentile(rateDto.getPercentile());
                if (CollectionUtil.isNotEmpty(questionTypeVos)) {
                    reportPdfVo.setErrorQuestions(questionTypeVos.stream()
                            .mapToInt(ExamAnalysisReportPdfVo2.QuestionTypeVo::getErrorQuestionCount)
                            .sum());
                }
            } catch (Exception e) {
                log.error("getExamAnalysisReportPdfInfoV2 方法执行出错，参数: {} 错误 {}", id, e.getMessage());
            }
        }
        // 学生基础数据
        Long studentId = Long.parseLong(String.valueOf(analyzeResult.getStudentId()));
        //获取年级信息
        /*GradeInfo grade = gradeService.getGradeByStudentId(studentId);
        reportPdfVo.setGradeName(GradeSemesterEnum.getByName("GRADE" + grade.getCurrentGradeLevel()));
        reportPdfVo.setCurrentSemester(GradeSemesterEnum.getByName("SEMESTER" + grade.getCurrentSemester()));*/
        Map<String, Object> studentInfo = examAnalyzeResultMapper.getStudentInfo(studentId);
        reportPdfVo.setStudentName(studentInfo.get("studentName").toString());
        reportPdfVo.setSchoolName(studentInfo.get("schoolName").toString());
        reportPdfVo.setGradeName(DataUtil.convertDigitsToChinese(studentInfo.get("gradeName").toString()));
        // 知识点统计
        List<KnowledgePointStatisticsVo> knowledgePointStatisticsPos = knowledgePointService.selectKnowledgePointStatisticsByExamId(analyzeResult.getPersonalExamId());
        if (CollectionUtil.isNotEmpty(knowledgePointStatisticsPos)) {
            String points = knowledgePointStatisticsPos.stream().map(KnowledgePointStatisticsVo::getKnowledgePointId)
                    .collect(Collectors.toList()).stream()
                    .filter(uuid -> uuid != null)
                    .map(UUID::toString)
                    .collect(Collectors.joining(","));
            Map<String, List<KnowledgePointStatisticsVo>> statsMap = knowledgePointStatisticsPos.stream()
                    .collect(Collectors.groupingBy(vo -> vo.getKnowledgePointId().toString()));
            List<KnowledgePointChapterDto> chapterDtos = aiRequestService.mathKnowledgePointsChapter(publisherEnum.getValue(),
                    null,
                    null, points);
            if (!CollectionUtil.isEmpty(chapterDtos)) {
                Map<String, List<KnowledgePointChapterDto>> chapterMap = chapterDtos.stream()
                        .collect(Collectors.groupingBy(
                                chapterDto -> chapterDto.getChapterId()
                        ));
                List<ExamAnalysisReportPdfVo2.ChapterKnowledgePoint> chapterKnowledgePointList = new ArrayList<>();
                List<String> textbookList = new ArrayList<>();
                for (Map.Entry<String, List<KnowledgePointChapterDto>> entry : chapterMap.entrySet()) {
                    ExamAnalysisReportPdfVo2.ChapterKnowledgePoint knowledgePointChapterVo = new ExamAnalysisReportPdfVo2.ChapterKnowledgePoint();
                    knowledgePointChapterVo.setChapterId(entry.getValue().get(0).getChapterId());
                    knowledgePointChapterVo.setChapterName(entry.getValue().get(0).getChapterName());
                    String textbook = GradeSemesterEnum.getByGrade(entry.getValue().get(0).getGrade()) + GradeSemesterEnum.getBySemester(entry.getValue().get(0).getSemester());
                    if (!textbookList.contains(textbook)) {
                        textbookList.add(textbook);
                    }
                    knowledgePointChapterVo.setBookSortNo(entry.getValue().get(0).getGrade()+""+entry.getValue().get(0).getSemester());
                    knowledgePointChapterVo.setTextbook(textbook);
                    knowledgePointChapterVo.setChapterSortNo(entry.getValue().get(0).getChapterSortNo());
                    List<KnowledgePointChapterDto> sectionList = entry.getValue();
                    // 小节对应知识点列表
                    Map<String, List<KnowledgePointChapterDto>> sectionMap = sectionList.stream()
                            .collect(Collectors.groupingBy(
                                    chapterDto -> chapterDto.getSectionId()
                            ));
                    List<ExamAnalysisReportPdfVo2.SectionKnowledgePoint> sectionKnowledgePointList = new ArrayList<>();
                    for (Map.Entry<String, List<KnowledgePointChapterDto>> sectionEntry : sectionMap.entrySet()) {
                        ExamAnalysisReportPdfVo2.SectionKnowledgePoint sectionKnowledgePoint = new ExamAnalysisReportPdfVo2.SectionKnowledgePoint();
                        sectionKnowledgePoint.setSectionName(sectionEntry.getValue().get(0).getSectionName());
                        sectionKnowledgePoint.setSectionId(sectionEntry.getValue().get(0).getSectionId());
                        sectionKnowledgePoint.setSectionSortNo(sectionEntry.getValue().get(0).getSectionSortNo());
                        List<KnowledgePointChapterDto> knowledgePointList = sectionEntry.getValue();
                        List<ExamAnalysisReportPdfVo2.KnowledgePointQuestionVo> knowledgePointQuestionVoList = new ArrayList<>();
                        for (KnowledgePointChapterDto knowledgePoint : knowledgePointList) {
                            ExamAnalysisReportPdfVo2.KnowledgePointQuestionVo knowledgePointQuestionVo = new ExamAnalysisReportPdfVo2.KnowledgePointQuestionVo();
                            knowledgePointQuestionVo.setKnowledgePointId(knowledgePoint.getKnowledgePointId());
                            knowledgePointQuestionVo.setName(knowledgePoint.getKnowledgePointName());
                            List<KnowledgePointStatisticsVo> vos = statsMap.get(knowledgePoint.getKnowledgePointId());
                            if (!CollectionUtil.isEmpty(vos)) {
                                knowledgePointQuestionVo.setQuestionCount(vos.get(0).getTotalQuestionCount());
                                String myMasteredCountPercent = String.format("%d%%", Math.round((double) vos.get(0).getTotalCorrectCount() / vos.get(0).getTotalQuestionCount() * 100));
                                knowledgePointQuestionVo.setMyMasteredCountPercent(myMasteredCountPercent);
                                List<ExamAnalysisReportPdfVo2.QuestionTypeVo> questionNos = this.splitQuestionNos(vos.get(0).getQuestionNos());
                                if (questionNos != null) {
                                    String otherQuestionNos = "";
                                    for (ExamAnalysisReportPdfVo2.QuestionTypeVo questionTypeVo : questionNos) {
                                        if (questionTypeVo.getQuestionType().equals(QuestionTypeEnum.MULTIPLE_CHOICE.name())) {
                                            knowledgePointQuestionVo.setMultipleChoiceNos(questionTypeVo.getQuestionNos());
                                        } else if (questionTypeVo.getQuestionType().equals(QuestionTypeEnum.FILL_IN_THE_BLANK.name())) {
                                            knowledgePointQuestionVo.setFillBlankNos(questionTypeVo.getQuestionNos());
                                        } else {
                                            if (otherQuestionNos.equals("")) {
                                                otherQuestionNos = questionTypeVo.getQuestionNos();
                                            } else {
                                                otherQuestionNos = otherQuestionNos + "," + questionTypeVo.getQuestionNos();
                                            }
                                            knowledgePointQuestionVo.setOtherNos(otherQuestionNos);
                                        }
                                    }
                                }
                                knowledgePointQuestionVo.setQuestions(this.splitQuestionNos(vos.get(0).getQuestionNos()));
                            }
                            knowledgePointQuestionVoList.add(knowledgePointQuestionVo);
                        }
                        sectionKnowledgePoint.setKnowledgePointStatisticsPos(knowledgePointQuestionVoList);
                        sectionKnowledgePointList.add(sectionKnowledgePoint);
                    }
                    // 章节排序
                    sectionKnowledgePointList.sort(Comparator.comparingInt(ExamAnalysisReportPdfVo2.SectionKnowledgePoint::getSectionSortNo));
                    knowledgePointChapterVo.setSections(sectionKnowledgePointList);
                    chapterKnowledgePointList.add(knowledgePointChapterVo);
                }
                chapterKnowledgePointList.sort(Comparator.comparing(ExamAnalysisReportPdfVo2.ChapterKnowledgePoint::getBookSortNo)
                        .thenComparingInt(ExamAnalysisReportPdfVo2.ChapterKnowledgePoint::getChapterSortNo));
                reportPdfVo.setKnowledgePointChapterVos(chapterKnowledgePointList);
                reportPdfVo.setTotalTextbook(textbookList.size());
            }
        }
        return reportPdfVo;
    }

    @Override
    public List<ExamQuestionInfoDto> listExamQuestions(UUID examId) {
        return personalExamMapper.getExamQuestionInfo(examId);
    }

    @Override
    public Boolean checkExistExamShouldReanalyze(CreateExamAnalysisReportParam param) {
        if (null != param.getExamAnalyzeResultId()) {
            //后台手动纠错完成的，不需要重新分析
            ExamAnalyzeResult examAnalyzeResult = examAnalyzeResultMapper.selectById(param.getExamAnalyzeResultId());
            if (ExamAnalyzeResultEnum.FINISHED_ERROR == examAnalyzeResult.getResult()) {
                return false;
            }
        }
        MathExamsEntity mathExamsEntity = mathExamsMapper.selectById(param.getExamId());
        boolean samePublisher = false;
        if (null == mathExamsEntity.getPublisher()) {
            samePublisher = false;
        } else {
            samePublisher = param.getPublisher() == PublisherEnum.ofCustomerName(mathExamsEntity.getPublisher());
        }
        if (samePublisher) {
            return false;
        }
        log.info("checkExistExamShouldReanalyze {} {} {}", param.getExamId(), mathExamsEntity.getPublisher(), param.getPublisher());
        List<QuestionKnowledgePoint> questionKnowledgePoints = questionKnowledgePointService.listKnowledgePointsByExamIdAndPublisher(param.getExamId(), param.getPublisher());
        return CollUtil.isEmpty(questionKnowledgePoints);
    }

    @Override
    public void rebuildExamQuestionPublisher() {
        questionKnowledgePointService.rebuildExamQuestionPublisher();
    }

    @Override
    public void reanalyzeExamKnowledgePoint(CreateExamAnalysisReportParam param) {
        // 参数校验
        if (param == null || CollUtil.isEmpty(param.getExamDataList())) {
            CommonResponse.assertError("参数错误: 考试数据为空");
        }
        CommonResponse.ERROR.assertNotNull(param.getPublisher(), "教材版本不能为空");

        PersonalExamAnalysisVo personalExam = saveExamAndQuestion(param);

        AiAnalyticsExamParam.ExtraParamsDTO extraParamsDTO = AiAnalyticsExamParam.ExtraParamsDTO.builder()
                .personalExamId(personalExam.getPersonalExamId())
                .studentId(param.getStudentId())
                .analysisReportId(personalExam.getAnalyzeReportId())
                .hasExam(false)
                .parentId(param.getParentId())
                .parentName(param.getParentName())
                .telNumber(param.getTelNumber())
                .build();
        AiAnalyticsExamParam examAnalyzeParam = AiAnalyticsExamParam.builder()
                .examId(param.getExamId())
                .publisher(param.getPublisher())
                .extraParams(extraParamsDTO).build();
        eduKnowLedgeHubBusinessService.reanalyzeExamKnowledgePoint(examAnalyzeParam);
    }

    /**
     * 方法描述 解析分析报告各题型数据
     *
     * @param: [report, rateDto, ifShowErrorQuestion]
     * @return: java.util.List<com.joinus.study.model.vo.ExamAnalysisReportPdfVo2.QuestionTypeVo>
     * @author: DELL
     * @date: 2025/5/22 14:07
     */
    private List<ExamAnalysisReportPdfVo2.QuestionTypeVo> analyzeQuestionTypeData(ExamAnalysisReportVo report, CorrectRateDto rateDto, Boolean ifShowErrorQuestion) {
        List<ExamAnalysisReportPdfVo2.QuestionTypeVo> questionTypeVos = new ArrayList<>();
        if (rateDto != null) {
            // 定义题型与对应的获取方法映射
            Map<String, Function<ExamAnalysisReportVo, Integer>> totalMap = new LinkedHashMap<>();
            totalMap.put(QuestionTypeEnum.MULTIPLE_CHOICE.name(), ExamAnalysisReportVo::getMultipleChoiceTotal);
            totalMap.put(QuestionTypeEnum.FILL_IN_THE_BLANK.name(), ExamAnalysisReportVo::getFillBlankTotal);
            totalMap.put(QuestionTypeEnum.PROBLEM_SOLVING.name(), ExamAnalysisReportVo::getFreeResponseTotal);
            totalMap.put(QuestionTypeEnum.CALCULATION.name(), ExamAnalysisReportVo::getCalculationTotal);
            totalMap.put(QuestionTypeEnum.TRUE_FALSE.name(), ExamAnalysisReportVo::getTrueFalseTotal);
            totalMap.put(QuestionTypeEnum.APPLICATION.name(), ExamAnalysisReportVo::getApplicationTotal);
            totalMap.put(QuestionTypeEnum.PROOF.name(), ExamAnalysisReportVo::getProofTotal);
            totalMap.put(QuestionTypeEnum.OTHER.name(), ExamAnalysisReportVo::getOtherTotal);
            // 对应的正确率获取方式
            Map<String, Function<CorrectRateDto, String>> rateMap = new HashMap<>();
            rateMap.put(QuestionTypeEnum.MULTIPLE_CHOICE.name(), CorrectRateDto::getMultipleChoice);
            rateMap.put(QuestionTypeEnum.FILL_IN_THE_BLANK.name(), CorrectRateDto::getFillBlank);
            rateMap.put(QuestionTypeEnum.PROBLEM_SOLVING.name(), CorrectRateDto::getFreeResponse);
            rateMap.put(QuestionTypeEnum.CALCULATION.name(), CorrectRateDto::getCalculation);
            rateMap.put(QuestionTypeEnum.TRUE_FALSE.name(), CorrectRateDto::getTrueFalse);
            rateMap.put(QuestionTypeEnum.APPLICATION.name(), CorrectRateDto::getApplication);
            rateMap.put(QuestionTypeEnum.PROOF.name(), CorrectRateDto::getProof);
            rateMap.put(QuestionTypeEnum.OTHER.name(), CorrectRateDto::getOther);
            // 遍历并添加有效题型
            for (Map.Entry<String, Function<ExamAnalysisReportVo, Integer>> entry : totalMap.entrySet()) {
                String questionType = entry.getKey();
                int total = entry.getValue().apply(report);
                if (total > 0) {
                    ExamAnalysisReportPdfVo2.QuestionTypeVo vo = new ExamAnalysisReportPdfVo2.QuestionTypeVo();
                    vo.setQuestionType(QuestionTypeEnum.getEnumByName(questionType).getDesc());
                    vo.setTotal(total);
                    vo.setCorrectRate(rateMap.get(questionType).apply(rateDto));
                    if (ifShowErrorQuestion && vo.getCorrectRate() != null && !vo.getCorrectRate().contains("100")) {
                        String errorQuestionNos = personalExamQuestionMapper.selectErrorQuestionNosByPersonalExamId(report.getPersonalExamId(), questionType);
                        vo.setQuestionNos(errorQuestionNos);
                        vo.setErrorQuestionCount(errorQuestionNos != null ? errorQuestionNos.split(",").length : 0);
                    }
                    questionTypeVos.add(vo);
                }
            }
            return questionTypeVos;
        }
        return null;
    }

    private List<ExamAnalysisReportPdfVo2.QuestionTypeVo> splitQuestionNos(String questionNosStr) {
        // 分割字符串并过滤空值
        List<String> questionNos = Arrays.stream(questionNosStr.split(","))
                .filter(s -> !s.trim().isEmpty())
                .collect(Collectors.toList());
        // 按题号数字排序
        questionNos.sort(Comparator.comparingInt(s -> Integer.parseInt(s.trim().split("\\s+")[1])));
        Map<String, ExamAnalysisReportPdfVo2.QuestionTypeVo> questionTypeMap = new HashMap<>();
        Map<String, List<String>> questionTypeNumbersMap = new HashMap<>(); // 存储每个题型对应的题号列表
        for (String questionNo : questionNos) {
            String[] parts = questionNo.trim().split("\\s+");
            if (parts.length < 2) continue;
            String type = parts[0];
            String number = parts[1];
            questionTypeNumbersMap.computeIfAbsent(type, k -> new ArrayList<>()).add(number);
            questionTypeMap.computeIfAbsent(type, k -> {
                ExamAnalysisReportPdfVo2.QuestionTypeVo vo = new ExamAnalysisReportPdfVo2.QuestionTypeVo();
                vo.setQuestionType(type);
                return vo;
            });
        }
        questionTypeMap.forEach((type, vo) -> {
            List<String> numbers = questionTypeNumbersMap.get(type);
            String questionNosStr2 = String.join(",", numbers);
            vo.setQuestionNos(questionNosStr2);
        });
        return new ArrayList<>(questionTypeMap.values());
    }
}
