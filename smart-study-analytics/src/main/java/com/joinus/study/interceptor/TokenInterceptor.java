package com.joinus.study.interceptor;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.joinus.common.model.response.ApiResult;
import com.joinus.study.interceptor.util.*;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.List;

@Component
public class TokenInterceptor extends BaseInterceptor {

    protected static final Logger logger = LoggerFactory.getLogger(TokenInterceptor.class);
    // 白名单
    private List<String> whiteUrls;
    private int _size = 0;
    public static final Integer TOKEN_EXP = 60 * 60 * 24 * 30;

    @Resource(name = "redisConfig")
    private RedisTemplate<String, Object> redisTemplate;


    public TokenInterceptor() {
        whiteUrls = FileUtil.readFileAsStream("white/tokenWhite.txt");
        _size = null == whiteUrls ? 0 : whiteUrls.size();
    }

    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
            throws Exception {
        if ("OPTIONS".equals(request.getMethod())) {
            return super.preHandle(request, response, handler);
        }
        String url = request.getRequestURI();
        if (WebUtil.isWhiteRequest(url, _size, whiteUrls) || url.contains("doc.html")) {
            return super.preHandle(request, response, handler);
        }
        response.setContentType("application/json;charset=UTF-8");
        response.setCharacterEncoding("UTF-8");
        String token = request.getHeader("token");
        token = StringUtils.isNotBlank(token) ? token : request.getParameter("token");
        if(DataUtils.isEmpty(token)){
            response.getWriter().write(JSON.toJSONString(ApiResult.failed("token不能为空！")));
            response.getWriter().flush();
            return false;
        }
        String decryptToken = TokenUtils.decryptToken(token);
        if(DataUtils.isEmpty(decryptToken)){
            response.getWriter().write(JSON.toJSONString(ApiResult.failed("token错误！")));
            response.getWriter().flush();
            return false;
        }
        AppToken appToken = JSON.parseObject(decryptToken, AppToken.class);
        if(DataUtils.isEmpty(appToken)){
            response.getWriter().write(JSON.toJSONString(ApiResult.failed("token验证失败！")));
            response.getWriter().flush();
            return false;
        }
        Long userId = appToken.getUserId();
        String key = "JOINUS:QYL_TOKEN:" + userId + ":"
                + appToken.getRole();
        JSONObject jsonObject = JSON.parseObject(String.valueOf(redisTemplate.opsForValue().get(key)));
        if (DataUtils.isEmpty(jsonObject) || !token.equals(jsonObject.get("token"))) {
            Instant instant = Instant.ofEpochMilli(appToken.getTokenDate());
            ZoneId zone = ZoneId.systemDefault();
            final DateTimeFormatter DATETIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            logger.info("token已过期,用户身份: {}, 用户Id: {}, 上次登录时间: {}, 请求URL: {}",
                    "0".equals(appToken.getRole()) ? "老师" : "家长", appToken.getUserId(),
                    LocalDateTime.ofInstant(instant, zone).format(DATETIME_FORMATTER), url);
            logger.info("客户端token: {}", token);
            if (DataUtils.isNotEmpty(jsonObject)) {
                String decryptToken2 = TokenUtils.decryptToken(jsonObject.get("token").toString());
                AppToken appToken2 = JSON.parseObject(decryptToken2, AppToken.class);
                Instant instant2 = Instant.ofEpochMilli(appToken2.getTokenDate());
                ZoneId zone2 = ZoneId.systemDefault();
                logger.info("服务端token: {}, 存储时间: {}", jsonObject.get("token"),
                        LocalDateTime.ofInstant(instant2, zone2).format(DATETIME_FORMATTER));
            } else {
                logger.info("服务端token为空！");
            }
            response.getWriter().write(JSON.toJSONString(ApiResult.failed("token已过期")));
            response.getWriter().flush();
            return false;
        } else {
            request.setAttribute("CURRENT_USER_ID", userId);
            request.setAttribute("CURRENT_USER", jsonObject.get("userInfo"));
        }
        return true;
    }


    public static void main(String[] args) throws Exception {
        String decryptToken = TokenUtils.decryptToken("7129BB7259B8A48728513566E6068FB17DEC16285AF3BF159FB21420B1FD5FD6B7528E73F6296ECDD5320601CC5FD8A694BD7C4F7AB5AEB80D26FBD1837E8F09");
        AppToken appToken = JSON.parseObject(decryptToken, AppToken.class);
        System.out.println("appToken="+JSON.toJSONString(appToken));
    }
}
