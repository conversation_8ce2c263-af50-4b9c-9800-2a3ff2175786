package com.joinus.study.mapper;

import com.github.houbb.heaven.annotation.reflect.Param;
import com.joinus.study.model.entity.QuestionKnowledgePoint;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.joinus.study.model.vo.KnowledgePointStatisticsVo;
import com.joinus.study.model.vo.StudentInfoVo;

import java.util.List;
import java.util.UUID;

/**
* <AUTHOR>
* @description 针对表【question_knowledge_point】的数据库操作Mapper
* @createDate 2025-03-11 09:48:14
* @Entity com.joinus.study.model.entity.QuestionKnowledgePoint
*/
public interface QuestionKnowledgePointMapper extends BaseMapper<QuestionKnowledgePoint> {

    List<KnowledgePointStatisticsVo> selectKnowledgePointStatisticsByExamId(@Param("id") Long id);

    List<StudentInfoVo> getStudentIds(@Param("studentId") Long studentId);

    List<KnowledgePointStatisticsVo> getClassKnowledgePointStatics(@Param("studentIds") List<Long> studentIds,
                                                                   @Param("classIds") List<Long> classIds,
                                                                   @Param("examId") UUID examId);
}




