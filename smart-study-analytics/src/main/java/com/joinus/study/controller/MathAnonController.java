package com.joinus.study.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.alibaba.excel.EasyExcel;
import com.joinus.common.exception.BaseException;
import com.joinus.common.model.response.ApiResult;
import com.joinus.dao.po.SchoolInfoPO;
import com.joinus.study.model.bo.JumpUrlBo;
import com.joinus.study.model.entity.MathSchoolExam;
import com.joinus.study.model.param.ImportSchoolExamParam;
import com.joinus.study.model.param.MathMembershipNoticeParam;
import com.joinus.study.model.vo.MathSchoolExamVo;
import com.joinus.study.service.*;
import com.joinus.study.utils.AliOssUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;


@RestController
@AllArgsConstructor
@RequestMapping("/math/anon")
@Slf4j
@Api(value = "数学-免登录相关接口",  tags = "免登录相关接口")
public class MathAnonController {

    @Autowired
    private MembershipService membershipService;
    @Autowired
    private MathActivityStudentService mathActivityStudentService;
    @Autowired
    private MathSchoolExamService mathSchoolExamService;
    @Autowired
    private MathAnonService mathAnonService;
    @Resource
    private AliOssUtils aliOssUtils;
    @Autowired
    private BasicBusinessService basicBusinessService;

    @Operation(summary = "接收开通会员通知")
    @Parameters({
            @Parameter(name = "param", description = "请求参数", required = true, schema = @Schema(implementation = MathMembershipNoticeParam.class))
    })
    @PostMapping("/membership/notice")
    public ApiResult membershipNotice(HttpServletRequest request, @Valid @RequestBody MathMembershipNoticeParam param) {
        log.info("receive membership notice:{}", param);
        membershipService.updateMathMembershipStatus(param);
        return ApiResult.success();
    }

    @ApiIgnore
    @GetMapping("/membership/auto-cancle")
    public ApiResult autoCancleMembership(HttpServletRequest request) {
        mathActivityStudentService.autoCancleMathMemberRightsTask();
        return ApiResult.success();
    }

    @Operation(summary = "通过excel导入学校试卷关系")
    @PostMapping("/school-exam/import/excel")
    public ApiResult<String> importSchoolExamFromExcel(@RequestParam("file") MultipartFile file) {
        try {
            // 1. 验证文件
            if (file.isEmpty()) {
                return ApiResult.failed("上传的文件不能为空");
            }

            String fileName = file.getOriginalFilename();
            if (fileName == null || (!fileName.endsWith(".xls") && !fileName.endsWith(".xlsx"))) {
                return ApiResult.failed("请上传有效的Excel文件(.xls 或 .xlsx)");
            }

            // 2. 解析Excel文件
            List<ImportSchoolExamParam> dataList = EasyExcel.read(file.getInputStream())
                    .head(ImportSchoolExamParam.class)
                    .registerConverter(new com.joinus.study.config.excel.UUIDConverter())
                    .sheet()
                    .doReadSync();

            if (dataList == null || dataList.isEmpty()) {
                return ApiResult.failed("Excel文件中没有数据");
            }
            mathSchoolExamService.importSchoolExamFromExcel(dataList);
        } catch (Exception e) {
            // 记录详细的错误信息
            log.error("Excel文件解析失败", e);

            // 根据异常类型提供更具体的错误信息
            String errorMessage = "文件解析失败";
            errorMessage = "类初始化失败，可能是依赖库版本冲突或配置问题";
            Throwable cause = e.getCause();
            if (cause != null) {
                errorMessage += ": " + cause.getMessage();
            }
            throw new BaseException(errorMessage);
        }
        return ApiResult.success();
    }

    @Operation(summary = "根据跳转链接区分学科类型")
    @Parameters({
            @Parameter(name = "param", description = "请求参数", required = true, schema = @Schema(implementation = String.class))
    })
    @GetMapping("/jump-url")
    public ApiResult<JumpUrlBo> querySubjectByShortUtl(HttpServletRequest request,
                                                    @RequestParam(value = "qrStr", required = true) String qrStr) throws UnsupportedEncodingException {
        String os = request.getHeader("os");
        String studentIdStr = request.getHeader("studentid");
        Long studentId = StrUtil.isBlank(studentIdStr) ? null : Long.parseLong(studentIdStr);
        JumpUrlBo jumpUrlBo = null;
        qrStr = URLDecoder.decode(qrStr, "UTF-8");
        if (qrStr.contains("math")) {
            jumpUrlBo = mathAnonService.queryJumpUrlForMath(qrStr, studentId);
        } else if (qrStr.contains("ch/")) {
            String keyboard = "android".equals(os) ? "nqylkb" : "handlekb";
            jumpUrlBo = mathAnonService.queryJumpUrlForChiniese(qrStr, keyboard);
        }
        if (null != jumpUrlBo && StrUtil.isNotBlank(jumpUrlBo.getUrl())) {
            jumpUrlBo.setEncodedUrl(URLEncoder.encode(jumpUrlBo.getUrl(), "UTF-8"));
        }
        if (null == jumpUrlBo.getParams()) {
            jumpUrlBo.setParams(new JSONObject());
        }
        return ApiResult.success(jumpUrlBo);
    }

    @GetMapping("/logging/exams/{examId}/questions/{questionId}")
    public ApiResult<Map<String, Object>> loggingExamQuestion(
            @PathVariable("examId") Long examId,
            @PathVariable("questionId") Long questionId) {
        Map<String, Object> result = new HashMap<>();
        result.put("examId", examId);
        result.put("questionId", questionId);
        return ApiResult.success(result);
    }


    @GetMapping("/identify-QR")
    @ApiOperation(value = "识别图片中二维码", notes = "识别图片中二维码返回二维码内容", response = ApiResult.class)
    @ApiImplicitParam(name = "objectKey", value = "阿里云OSS objectKey", required = true, paramType = "query", dataType = "String")
    public ApiResult<String> identifyQRcode(@RequestParam String objectKey) {
        try {
            String result = aliOssUtils.recognizeQrCode(objectKey);
            return ApiResult.success(result);
        } catch (Exception e) {
            throw new BaseException(e.getMessage());
        }
    }

    @DeleteMapping("/exam-school-relation/{examId}")
    public ApiResult<Map<String, Object>> deleteExamSchoolRelation(
            @PathVariable("examId") UUID examId) {
        mathSchoolExamService.deleteExamSchoolRelation(examId);
        return ApiResult.success();
    }

    @PostMapping("/exam-school-relation/{examId}")
    public ApiResult<Map<String, Object>> updateExamSchoolRelation(
            @PathVariable("examId") UUID examId, @RequestBody List<Long> schoolIds) {
        mathSchoolExamService.updateExamSchoolRelation(examId, schoolIds);
        return ApiResult.success();
    }

    @GetMapping("/exam-school-relation/{examId}")
    public ApiResult<List<SchoolInfoPO>> listExamSchoolRelation(@PathVariable("examId") UUID examId) {
        List<MathSchoolExam> list = mathSchoolExamService.listExamSchoolRelationByExamId(examId);
        if (CollUtil.isEmpty(list)) {
            return ApiResult.success(List.of());
        }
        List<SchoolInfoPO> schoolInfoPOS = basicBusinessService.listSchoolByIds(list.stream().map(MathSchoolExam::getSchoolId).collect(Collectors.toList()));
        return ApiResult.success(schoolInfoPOS);
    }

    @GetMapping("/exam-school-relation")
    public ApiResult<List<MathSchoolExamVo>> listExamSchoolRelation(@RequestParam(value = "schoolId", required = false) Long schoolId,
                                                                    @RequestParam(value = "examIds", required = false) List<UUID> examIds) {
        if (null == schoolId && CollUtil.isEmpty(examIds)) {
            throw new BaseException("schoolId和examIds不能同时为空");
        }
        List<MathSchoolExamVo> list = mathSchoolExamService.listExamSchoolRelation(schoolId, examIds);
        if (CollUtil.isEmpty(list)) {
            return ApiResult.success(List.of());
        }
        return ApiResult.success(list);
    }


}
