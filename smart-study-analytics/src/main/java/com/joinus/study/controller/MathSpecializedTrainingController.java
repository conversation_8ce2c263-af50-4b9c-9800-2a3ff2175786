package com.joinus.study.controller;

import com.joinus.common.model.response.ApiResult;
import com.joinus.common.model.response.CommonResponse;
import com.joinus.study.annotation.RequiresMathMembership;
import com.joinus.study.model.dto.ExamBookQuestionDetailDto;
import com.joinus.study.model.dto.ExamChapterQuestionDetailDto;
import com.joinus.study.model.dto.QuestionAnalyzeResultDto;
import com.joinus.study.model.dto.SpecializedTrainingUpdateExamDto;
import com.joinus.study.model.enums.PublisherEnum;
import com.joinus.study.model.param.*;
import com.joinus.study.model.vo.*;
import com.joinus.study.service.SpecializedTrainingService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;
import java.util.UUID;

/**
 * @Description: 专项训练
 * @Author: anpy
 * @date: 2025/4/28 17:33
 */
@RestController
@RequestMapping("/specialized-training")
@Api(tags = "数学专项训练")
public class MathSpecializedTrainingController {

    @Resource
    private SpecializedTrainingService specializedTrainingService;

    @PostMapping("/questions")
    @ApiOperation(value = "专项训练-根据知识点和题型生成题目", notes = "专项训练-根据知识点和题型生成题目", response = ApiResult.class)
    public ApiResult<SpecializedTrainingNewResultVo> createSpecializedTrainingQuestions(@RequestBody CreateQuestionByKnowledgeParam param) {
        return ApiResult.success(specializedTrainingService.createQuestionByKnowledgeAndQuestionType(param));
    }

    @PostMapping("/questions/v2")
    @ApiOperation(value = "专项训练-根据知识点和题型生成题目", notes = "专项训练-根据知识点和题型生成题目", response = ApiResult.class)
    public ApiResult<SpecializedTrainingNewResultVoV2> createSpecializedTrainingQuestionsV2(@RequestBody CreateQuestionByKnowledgeParamV2 param) {
        return ApiResult.success(specializedTrainingService.createQuestionByKnowledgeAndQuestionTypeV2(param));
    }

    @RequiresMathMembership
    @PostMapping("/questions/v3")
    @ApiOperation(value = "专项训练-根据知识点和题型生成题目_V3", notes = "专项训练-根据知识点和题型生成题目", response = ApiResult.class)
    public ApiResult<ExamChapterQuestionDetailDto> createSpecializedTrainingQuestionsV3(@RequestBody CreateQuestionByKnowledgeParamV2 param) {
        return ApiResult.success(specializedTrainingService.createQuestionByKnowledgeAndQuestionTypeV3(param));
    }
    @PostMapping("/holiday-training/questions")
    @ApiOperation(value = "暑假学习训练-单元/章节-生成题目", notes = "暑假学习训练-单元/章节-生成题目", response = ApiResult.class)
    public ApiResult<ExamChapterQuestionDetailDto> createHolidayTrainingSection(@RequestBody CreateQuestionByKnowledgeParamV2 param) {
        return ApiResult.success(specializedTrainingService.createHolidayTrainingQuestion(param));
    }
    @PostMapping("/holiday-training/textbook/questions")
    @ApiOperation(value = "暑假学习训练-综合训练-生成题目", notes = "暑假学习训练-综合训练-生成题目", response = ApiResult.class)
    public ApiResult<List<ExamBookQuestionDetailDto>> createHolidayTrainingTextbook(@RequestBody CreateQuestionByKnowledgeParamV2 param) {
        return ApiResult.success(specializedTrainingService.createHolidayTrainingBookQuestion(param));
    }
    @GetMapping("/questions/detail/{examId}")
    @ApiOperation(value = "专项训练-查询试卷详情", notes = "专项训练-查询试卷详情", response = ApiResult.class)
    public ApiResult<ExamChapterQuestionDetailDto> selectSpecializedTrainingExamDetail(@PathVariable UUID examId,
                                                                                       @RequestParam(value = "personalExamId", required = false) Long personalExamId) {
        return ApiResult.success(specializedTrainingService.selectSpecializedTrainingExamDetail(examId, personalExamId));
    }
    @GetMapping("/questions/textbook/detail/{examId}")
    @ApiOperation(value = "专项训练-查询综合试卷详情", notes = "专项训练-查询综合试卷详情", response = ApiResult.class)
    public ApiResult<List<ExamBookQuestionDetailDto>> selectSpecializedTextBookTrainingExamDetail(@PathVariable UUID examId,
                                                                                                  @RequestParam(value = "personalExamId", required = false) Long personalExamId) {
        return ApiResult.success(specializedTrainingService.selectSpecializedTextBookTrainingExamDetail(examId, personalExamId));
    }
    @GetMapping("/questions/analyze-result")
    @ApiOperation(value = "专项训练-根据题目解析结果", response = ApiResult.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "questionId", value = "题目ID", required = true, dataType = "UUID", paramType = "path"),
            @ApiImplicitParam(name = "publisher", value = "出版社", required = false, dataType = "String", paramType = "query")
    })
    public ApiResult<QuestionAnalyzeResultDto> questionAnalyzeResult(HttpServletRequest request,@RequestParam UUID questionId) {
        String publisher = request.getParameter("publisher");
        if(publisher == null){
            return ApiResult.success(specializedTrainingService.questionAnalyzeResult(questionId,null));
        }
        return ApiResult.success(specializedTrainingService.questionAnalyzeResult(questionId,PublisherEnum.valueOf(publisher)));
    }

    @GetMapping("/exam/exist")
    @ApiOperation(value = "专项训练-判断试卷是否存在", response = ApiResult.class)
    public CheckExamExsitenceVo examExist(@RequestParam String qrStr) {
        return specializedTrainingService.examExist(qrStr);
    }

    @GetMapping("/exam/exist/{personalExamId}")
    @ApiOperation(value = "专项训练-通过personalExamId判断试卷是否存在", response = ApiResult.class)
    public CheckExamExsitenceVo examExist(@PathVariable Long personalExamId) {
        return specializedTrainingService.examExistByPersonalExamId(personalExamId);
    }

    /**
     * 专项练习生成试卷
     * @param param
     * @return
     */
    @PostMapping("/exam/create")
    @ApiOperation(value = "试卷入库", notes = "试卷入库", response = ApiResult.class)
    public ApiResult<String> saveExam(@Valid @RequestBody SpecializedTrainingCreateExamParam param) {
        try {
            String examId = specializedTrainingService.saveExam(param);
            return ApiResult.success(examId);
        } catch (Exception e) {
            return ApiResult.failed(e.getMessage());
        }
    }

    /**
     * 专项练习更新试卷
     * @param param
     * @return
     */
    @PostMapping("/exam/update")
    @ApiOperation(value = "更新试卷", notes = "更新试卷", response = ApiResult.class)
    public SpecializedTrainingUpdateExamDto updateExam(@RequestBody SpecializedTrainingUpdateExamParam param) {
        CommonResponse.ERROR.assertNotNull(param.getExamSource(), "examSource不能为空");
        CommonResponse.ERROR.assertNotNull(param.getStudentId(), "studentId不能为空");
        return specializedTrainingService.updateExam(param);
    }

    @GetMapping("/exam/textbook/catalogue")
    @ApiOperation(value = "教材列表", notes = "教材列表", response = ApiResult.class)
    public ApiResult<TextbookCatalogLabelTree> textbookCatalogue(Long analyzeReportId,String examId) {
        return ApiResult.success(specializedTrainingService.textbookCatalogue(analyzeReportId,UUID.fromString(examId)));
    }

    @PostMapping("/exam/scope/update")
    @ApiOperation(value = "更新考察范围", notes = "更新考察范围", response = ApiResult.class)
    public ApiResult<String> updateExamScope(@RequestBody ExamScopeParam param) {
        specializedTrainingService.updateExamScope(param);
        return ApiResult.success("更新成功");
    }

    @GetMapping("/exam/scope")
    @ApiOperation(value = "获取考察范围", notes = "获取考察范围", response = ApiResult.class)
    public ApiResult<TextbookCatalogLabelTree> getExamScope(Long analyzeReportId) {
        CommonResponse.ERROR.assertNotNull(analyzeReportId, "analyzeReportId不能为空");
        return ApiResult.success(specializedTrainingService.getExamScope(analyzeReportId));
    }

    @GetMapping("/exam/scope/name")
    @ApiOperation(value = "获取已选择的考察范围", notes = "获取已选择的考察范围", response = ApiResult.class)
    public ApiResult<List<String>> getManualExamScopeName(Long analyzeReportId) {
        CommonResponse.ERROR.assertNotNull(analyzeReportId, "analyzeReportId不能为空");
        return ApiResult.success(specializedTrainingService.getManualExamScopeName(analyzeReportId));
    }

    @GetMapping("/exam/scope/knowledge-point")
    @ApiOperation(value = "获取考察知识点", notes = "获取考察知识点", response = ApiResult.class)
    public ApiResult<KnowledgePointBySelectVo> getKnowledgePointBySection(Long analyzeReportId) {
        CommonResponse.ERROR.assertNotNull(analyzeReportId, "analyzeReportId不能为空");
        return ApiResult.success(specializedTrainingService.getKnowledgePointBySection(analyzeReportId));
    }

    //查询题目详情
    @GetMapping("/question/{questionId}")
    @ApiOperation(value = "专项训练-查询题目详情", notes = "专项训练-查询题目详情", response = ApiResult.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "questionId", value = "题目ID", required = true, dataType = "UUID", paramType = "path"),
            @ApiImplicitParam(name = "publisher", value = "出版社", required = false, dataType = "String", paramType = "query")
    })
    public ApiResult<SpecializedTrainingQuestionVo> questionDetails(HttpServletRequest request, @PathVariable  UUID questionId) {
        String publisher = request.getParameter("publisher");
        if(publisher == null){
            SpecializedTrainingQuestionVo questionDetail = specializedTrainingService.getMathQuestionDetailById(questionId,null);
            return ApiResult.success(questionDetail);
        }
        SpecializedTrainingQuestionVo questionDetail = specializedTrainingService.getMathQuestionDetailById(questionId,PublisherEnum.valueOf(publisher));
        return ApiResult.success(questionDetail);
    }
}
