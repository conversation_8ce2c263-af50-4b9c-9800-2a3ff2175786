package com.joinus.study.model.vo;

import com.joinus.study.model.enums.PersonalExamQuestionResultEnum;
import io.swagger.annotations.ApiModel;
import lombok.*;

import java.util.UUID;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("专项训练题目")
@Data
@EqualsAndHashCode(of = {"questionId", "knowledgePointId"})
public class SpecializedTrainingQuestionVo {

    private UUID questionId;
    private String questionContent;
    private UUID questionTypeId;
    private String questionType;
    private String ossUrl;
    private int sortNo;
    private String answer;
    private String analyzeContent;
    private Boolean isPastExamPaper;
    private String pastExamPaperYear;
    private String pastExamPaperRegion;
    private UUID knowledgePointId;
    private String knowledgePointName;
    private UUID sectionId;
    private PersonalExamQuestionResultEnum questionResult;


}
