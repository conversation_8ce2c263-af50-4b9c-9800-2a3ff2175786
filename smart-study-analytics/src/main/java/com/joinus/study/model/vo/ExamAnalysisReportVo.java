package com.joinus.study.model.vo;

import com.joinus.study.model.dto.CorrectRateDto;
import com.joinus.study.model.enums.OverallScoreEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 * @Description:
 * @date: 2025/3/18 9:25
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ExamAnalysisReportVo implements Serializable {

    @ApiModelProperty(value = "考试id")
    private UUID examId;
    @ApiModelProperty(value = "考试名称")
    private String name;
    @ApiModelProperty(value = "试题总数")
    private int totalQuestions;
    @ApiModelProperty(value = "考试成绩 EXCELLENT-优、GOOD-良、FAIR-差")
    private OverallScoreEnum overallScore;
    @ApiModelProperty(value = "正确率json")
    private String correctRate;
    @ApiModelProperty(value = "正确率对象")
    private CorrectRateDto correctRates;
    @ApiModelProperty(value = "百分比排名")
    private BigDecimal percentile;
    @ApiModelProperty(value = "总知识点数")
    private int totalKnowledgePoints;
    @ApiModelProperty(value = "掌握知识点数")
    private int masteredKnowledgePoints;
    @ApiModelProperty(value = "弱项知识点数")
    private int weakKnowledgePoints;
    @ApiModelProperty(value = "选择题数")
    private int multipleChoiceTotal;
    @ApiModelProperty(value = "填空题数")
    private int fillBlankTotal;
    @ApiModelProperty(value = "简答题数")
    private int freeResponseTotal;
    @ApiModelProperty(value = "判断题数")
    private int trueFalseTotal;
    @ApiModelProperty(value = "算数题数")
    private int calculationTotal;
    @ApiModelProperty(value = "应用题数")
    private  int applicationTotal;
    @ApiModelProperty(value = "证明题数")
    private int proofTotal;
    @ApiModelProperty(value = "其他题数")
    private int otherTotal;
    private List<KnowledgePointStatisticsVo>  knowledgePointStatisticsPos;
    @ApiModelProperty(value = "图片地址")
    private List<String>  ossUrls;
    @ApiModelProperty(value = "做试卷人数")
    private Integer  testPapersCount;
    @ApiModelProperty(value = "是否已经加入错题 0否 1是")
    private Integer isAddMistakesBook;
}
