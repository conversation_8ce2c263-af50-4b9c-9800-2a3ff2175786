package com.joinus.study.model.param;

import com.joinus.study.model.enums.ExamSourceType;
import com.joinus.study.model.enums.PublisherEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.UUID;

/**
 * @Description: 专项训练试卷入库参数
 * @Author:  anpy
 * @date:  2025/5/8 09:30
 */
@Builder
@Data
public class SpecializedTrainingCreateExamParam {

    @NotBlank(message = "试卷名称不能为空")
    @ApiModelProperty("试卷名称")
    private String name;

    @ApiModelProperty("题目id列表")
    private List<UUID> questionIds;

    @NotNull(message = "学生id不能为空")
    @ApiModelProperty("学生id")
    private Long studentId;

    private Integer grade;
    private Integer semester;

    @ApiModelProperty("出版商")
    @NotNull(message = "出版商不能为空")
    private PublisherEnum publisher;

    @ApiModelProperty("试卷类型")
    @NotNull(message = "试卷类型不能为空")
    private ExamSourceType examSource;

}
