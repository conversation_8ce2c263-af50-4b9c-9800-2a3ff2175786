package com.joinus.study.model.param;

import com.joinus.study.model.enums.OssEnum;
import com.joinus.study.model.enums.PublisherEnum;
import lombok.Builder;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Builder
@Data
public class CreateExamParam {

    private List<String> objectNames;
    private List<String> handwritingRemovedObjectNames;
    @NotNull(message = "学生id不能为空")
    private Long studentId;
    private OssEnum ossEnum;
    private Integer grade;
    private Integer semester;
    @NotNull(message = "教材版本不能为空")
    private PublisherEnum publisher;
}
