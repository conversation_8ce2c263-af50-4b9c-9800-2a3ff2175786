package com.joinus.study.model.vo;

import com.joinus.study.model.enums.ExamAnalyzeResultEnum;
import com.joinus.study.model.enums.ExamSourceType;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.UUID;

@Data
public class ExamAnalyzeResultVo implements Serializable {

    private Long examAnalyzeResultId;
    private String examName;
    private UUID examId;
    private String ossUrl;
    private Date createdAt;
    private Integer questionCount;
    private ExamAnalyzeResultEnum result;
    private ExamSourceType source;
    private String examType;
    private List<String> ossUrls;
    private Long personalExamId;

}
