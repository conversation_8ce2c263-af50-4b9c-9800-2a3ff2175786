package com.joinus.dao;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * t_school_images
 */
@Data
@EqualsAndHashCode(callSuper=false)
@TableName("t_school_images")
public class SchoolImagesEntity {
    @TableField("school_id")
    private Long schoolId;
    /**
     *学校照片
     */
    @TableField("image_url")
    private String imageUrl;

}
