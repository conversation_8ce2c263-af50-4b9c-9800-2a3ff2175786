package com.joinus.dao;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

@TableName("adapter.t_external_platform_link")
@KeySequence("adapter.seq_external_platform_link")
@Data
public class ExternalPlatformLinkEntity {

    @TableId(value = "ID",type = IdType.INPUT)
    private Long id;
    @TableField("external_id")
    private String externalId;
    @TableField("platform_type")
    private Integer platformType;
    @TableField("internal_id")
    private Long internalId;
    @TableField("external_id_type")
    private Integer externalIdType;
    @TableField("SCHOOL_ID")
    private Long schoolId;
    @TableField("MODEL_ID")
    private String modelId;

    @TableField("isactive")
    private Integer isActive;
}
