package com.joinus.dao;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@TableName("ijx.T_LIFE_SERVICE_AGREEMENT")
@Data
public class LifeServiceEntity {

    @TableId(value = "ID",type = IdType.INPUT)
    private Long id;
    @TableField("parent_id")
    private Long parentId;
    @TableField("student_id")
    private Long studentId;
    @TableField("agreement_state")
    private Integer agreementState;
    @TableField("isactive")
    private Integer isActive;
    @TableField("type")
    private Integer type;

}
