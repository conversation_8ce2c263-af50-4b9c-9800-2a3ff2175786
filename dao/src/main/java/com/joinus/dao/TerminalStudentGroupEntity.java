package com.joinus.dao;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.apache.ibatis.type.JdbcType;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.Date;


@Data
@TableName("ijx.T_TERMINAL_STUDENT_GROUP")
public class TerminalStudentGroupEntity {


    @TableField("terminal_id")
    private Long terminalId;

    @TableField("group_id")
    private String groupId;
}
