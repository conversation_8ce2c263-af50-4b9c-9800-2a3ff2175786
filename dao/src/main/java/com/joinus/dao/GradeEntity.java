package com.joinus.dao;

import com.baomidou.mybatisplus.annotation.*;
import com.github.xiaoymin.knife4j.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@KeySequence("SEQ_T_GRADE")
@TableName("t_grade")
public class GradeEntity {
    @TableId(value = "ID",type = IdType.INPUT)
    private Long id;
    @TableField("grade_name")
    private String gradeName;
    @TableField("isactive")
    private Integer isActive;
    @TableField("school_name")
    private String schoolName;
    @TableField("school_id")
    private Long schoolId;
    @TableField("gra_name")
    private String graName;
    @TableField("update_time")
    private Date updateTime;
    @TableField("grade_code")
    private String gradeCode;
    @TableField("LEARNING_PERIOD")
    private String learningPeriod;

    @TableField(exist = false)
    private Long teacherId;
    @TableField(exist = false)
    private String teacherName;
    @TableField(exist = false)
    private String telNum;
    @TableField(exist = false)
    private String theNumber;
    @TableField(exist = false)
    private Long appRoverId;
    @TableField(exist = false)
    private String apRoverName;
    @TableField(exist = false)
    private Integer defType;

    /**
     * 判断年级中是否包含 教师或者教职工
     * @param subStrings
     * @return
     */
    public boolean isTeacher(List<String> subStrings) {
        if (StrUtil.isBlank(this.gradeName)) {
            return false;
        }
        return subStrings.stream().anyMatch(gradeName::contains);
    }

}
