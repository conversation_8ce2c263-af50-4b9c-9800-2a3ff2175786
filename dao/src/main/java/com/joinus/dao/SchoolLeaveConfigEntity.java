package com.joinus.dao;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * 学校表
 */
@Data
@TableName("T_SCHOOL_LEAVE_CONFIG")
public class SchoolLeaveConfigEntity implements Serializable {

    @TableField("school_id")
    private Long schoolId;

    @TableField("leave_type")
    private Integer leaveType;

    @TableField("leave_notice_type")
    private Integer leaveNoticeType;

    @TableField("leave_access_limit")
    private Integer leaveAccessLimit;

    @TableField("leave_resume_interval")
    private Integer leaveResumeInterval;

    @TableField("leave_expired_voice_status")
    private Integer leaveExpiredVoiceStatus;

    @TableField("leave_during_access")
    private Integer leaveDuringAccess;

    @TableField("leave_expired_access")
    private Integer leaveExpiredAccess;

    @TableField("created_at")
    private Date createdAt;

    @TableField("updated_at")
    private Date updatedAt;


}
