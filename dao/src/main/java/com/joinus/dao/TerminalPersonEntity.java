package com.joinus.dao;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.Date;

@Data
@TableName("ijx.T_TERMINAL_PERSON")
@KeySequence(value = "SEQ_T_TERMINAL_PERSON", dbType = DbType.ORACLE)
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class TerminalPersonEntity extends BaseEntity {
	/**
	 * 
	 */
	private static final long serialVersionUID = -4547595691321843309L;
	/** 主键 */
	@TableId(type = IdType.INPUT)
	private Long id;
	/** 操作类型 */
	private Short operateType;
	/** 操作时间 */
	private Date operateTime;
	/** 用户ID集合 */
	private String userIds;
	/** 用户名称集合 */
	private String userNames;
	/** 设备ID */
	private Long terminalId;
	/** 设备类型名称 */
	private String terminalTypeName;
	/** 设备编号 */
	private String terminalNum;
	/** 设备SIM卡号 */
	private String terminalSimNum;
	/** 设备名称 */
	private String terminalName;
	/** 操作者IP */
	private String operateLastIp;
	/** 操作内容 */
	private String operateContent;
	
}
