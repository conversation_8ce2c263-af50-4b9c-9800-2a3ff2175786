package com.joinus.dao;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName(value = "T_CAMERA_NVR")
public class CameraNvrEntity {
    /** CameraId */
    @TableId(value = "ID",type = IdType.INPUT)
    private Long id;
    /** 学校id */
    @TableField(value = "SCHOOL_ID")
    private Long schoolId;
    /** terminal num */
    @TableField(value = "TERMINAL_NUM")
    private String terminalNum;

}
