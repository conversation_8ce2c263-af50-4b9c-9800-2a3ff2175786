package com.joinus.dao;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * <AUTHOR> anpy
 * @create 2023/8/2 11:19
 */
@TableName("ijx.T_LIFE_SERVICE_SCHOOL_CONFIG")
@Data
public class LifeServiceSchoolConfigEntity {

    /**
     * 0：未启用，1：启用
     */
    @TableField("enabled_flag")
    private Integer enableFlag;
    /**
     * 生活服务类型 0签约配置 1微信可视电话 2校园洗衣 3校园吹风
     */
    private Integer type;
    @TableField("school_id")
    private Long schoolId;
    @TableField("isactive")
    private Integer isActive;

}
