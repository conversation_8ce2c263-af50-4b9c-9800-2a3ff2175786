package com.joinus.dao;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * <AUTHOR> anpy
 * @create 2024/4/12 10:06
 */
@TableName("T_FLOOR_TEACHER")
@Data
public class FloorTeacherEntity {
    @TableId(value = "ID",type = IdType.INPUT)
    private Long id;
    @TableField("floor_id")
    private Long floorId;
    @TableField("teacher_id")
    private Long teacherId;

}
