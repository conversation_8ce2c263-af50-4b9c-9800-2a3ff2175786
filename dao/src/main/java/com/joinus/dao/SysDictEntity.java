package com.joinus.dao;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName("sys_dict")
public class SysDictEntity {

    @TableId(value = "ID",type = IdType.INPUT)
    private Long id;

    @TableField("dict_name")
    private String dictName;

    @TableField("isactive")
    private Integer isActive;
    @TableField("DICT_ID")
    private Long dictId;
}
