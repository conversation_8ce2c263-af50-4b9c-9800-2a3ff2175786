package com.joinus.dao;

import com.baomidou.mybatisplus.annotation.*;
import lombok.*;

import java.util.Date;

@Data
@EqualsAndHashCode(callSuper=false)
@TableName(value = "T_CARD_SIGNIN_RECORD6")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CallSigninRecordEntity {

    @TableId(value = "ID",type = IdType.INPUT)
    private Long id;

    private Date saveTime;

    private Date startTime;

    private Long studentId;

    private String termianlNum;

    private String phone;
}
