package com.joinus.dao;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@Data
@EqualsAndHashCode(callSuper=false)
@TableName("sys_log")
@KeySequence("SEQ_SYS_LOG")
public class SysLogEntity extends BaseEntity {
    @TableId(value = "ID",type = IdType.INPUT)
    private Long id;/**/
    private String content;/**/
    @TableField("log_desc")
    private String logDesc;/**/
    @TableField("isactive")
    private Integer isActive;/**/
    private String operator;/**/
    @TableField("log_time")
    private Date logTime;/**/
    @TableField("LOG_TYPE")
    private Integer logType;/* 操作类型:1:增加，2：修改，3.删除，4.登陆，5.登出 */
    @TableField("school_id")
    private Long schoolId;/* 所属学校id */
    @TableField("school_name")
    private String schoolName;/* 所属学校名称 */
    @TableField("ip_address")
    private String ipAddress;/* ip地址 */
    @TableField("operator_id")
    private Long operatorId;/* 操作者id */
    @TableField("operator_type")
    private Integer operatorType;/* 操作者类型：1:管理员; 2:教师; 3:学生; …… */
    @TableField("user_id")
    private Integer userId;/* 用户ID */
    @TableField("group_name")
    private String groupName;/* 用户组名 */
    @TableField("region_name")
    private String regionName;/* 区域名 */

}
