package com.joinus.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.joinus.dao.TerminalExtrasEntity;

import java.math.BigDecimal;

/**
* <AUTHOR>
* @description 针对表【T_CARD_CASHBACK(返现记录明细)】的数据库操作Mapper
* @createDate 2024-07-15 16:42:04
* @Entity generator.domain.CardCashback
*/
public interface TerminalExtrasMapper extends BaseMapper<TerminalExtrasEntity> {


    TerminalExtrasEntity getTerminalExtrasByTerminalId(Long terminalId);

    void updateTerminalExtras(Long terminalId, BigDecimal cardSellingPrice);

    void addTerminalExtras(Long terminalId, BigDecimal cardSellingPrice);
}
