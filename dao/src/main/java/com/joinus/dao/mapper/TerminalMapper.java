package com.joinus.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.joinus.dao.TerminalEntity;
import com.joinus.dao.model.po.TerminalInfoBySchoolAndIdPO;
import com.joinus.dao.po.TerminalInfoPO;
import com.joinus.dao.po.TerminalStudentGroupPo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface TerminalMapper extends BaseMapper<TerminalEntity> {

    void updateTerminal(@Param("id") Long terminalId);

    TerminalEntity getRootTerminalByChildId(@Param("childId") Long childId);

    void updateRootTerminalFaceDeviceNum(@Param("rootId") Long rootId);

    void unstallTerminal(@Param("id") Long terminalId);

    List<Map<String, String>> getTerminalOnlineStatus();

    IPage<TerminalInfoPO> getActiveTerminalInfo(IPage<TerminalInfoPO> page, @Param("sort") String sort);
    List<TerminalEntity> listUpperComputerChildren(String terminalNum);

    /**
     * 根据学校id和终端编号查询终端信息
     *
     * @param schoolId    学校id
     * @param terminalNum 终端编号
     * @return 终端信息
     */
    TerminalInfoBySchoolAndIdPO getTerminalInfoBySchoolIdAndTerminalNum(@Param("schoolId") Long schoolId, @Param("terminalNum") String terminalNum);
    //List<TerminalEntity> getTerminalByImei(String imei);

    List<TerminalStudentGroupPo> listGroupTerminalByTerminalId(Long terminalId);

    void batchUpdateTerminalTree(@Param("rootId") Long rootId, @Param("schoolId") Long schoolId);

    List<TerminalEntity>   getFaceTerminalNumByIds(@Param("terminalIdList") List<Long> terminalIdList);
}

