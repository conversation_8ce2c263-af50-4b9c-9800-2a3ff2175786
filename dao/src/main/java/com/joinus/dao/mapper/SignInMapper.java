package com.joinus.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joinus.dao.entity.SignIn;
import org.apache.ibatis.annotations.Param;

import java.util.Date;

public interface SignInMapper extends BaseMapper<SignIn> {


    Page<SignIn> listCardSignInRecordHistory(Page<SignIn> page, @Param("startTime") Date startTime,
                                             @Param("endTime") Date endTime);
}
