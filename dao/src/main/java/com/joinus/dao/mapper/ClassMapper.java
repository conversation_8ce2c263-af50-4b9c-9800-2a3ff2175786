package com.joinus.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.joinus.dao.ClassEntity;
import com.joinus.dao.model.dto.ClassInfoListDTO;
import io.github.classgraph.ClassInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ClassMapper extends BaseMapper<ClassEntity> {

    int getClassBySchoolId(@Param("schoolId") Long schoolId, @Param("classId") Long classId, @Param("gradeId")Long gradeId);

    List<Long> classListByTeacher(Long teacherId);

    List<ClassInfoListDTO> classInfoListByTeacher(Long teacherId);

    List<ClassInfoListDTO> getClassAndGradeBySchoolId(Long schoolId);
}


