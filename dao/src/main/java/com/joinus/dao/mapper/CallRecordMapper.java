package com.joinus.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joinus.dao.entity.CallRecord;
import org.apache.ibatis.annotations.Param;

import java.util.Date;

public interface CallRecordMapper extends BaseMapper<CallRecord> {


    Page<CallRecord> listCallRecordHistory(Page<CallRecord> recordPage, @Param("startTime") Date startTime,
                                           @Param("endTime") Date endTime);
}
