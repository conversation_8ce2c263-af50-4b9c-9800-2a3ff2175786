package com.joinus.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.joinus.dao.entity.BasicPermission;
import com.joinus.dao.entity.BasicResource;
import com.joinus.dao.po.PermissionPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface PermissionMapper extends BaseMapper<BasicPermission> {

    List<PermissionPO> listAllPermissionByParentId(@Param("parentId") Long parentId,
                                                   @Param("platform") Integer platform);

}
