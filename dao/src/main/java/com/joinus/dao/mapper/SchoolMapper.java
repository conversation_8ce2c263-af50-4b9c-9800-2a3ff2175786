package com.joinus.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.joinus.dao.SchoolEntity;
import com.joinus.dao.po.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【T_SCHOOL(学校表)】的数据库操作Mapper
* @createDate 2024-11-29 15:46:27
* @Entity com.joinus.dao.SchoolEntity
*/
@Mapper
public interface SchoolMapper extends BaseMapper<SchoolEntity> {
    Integer getTenQinPhoneByStudentId(Long studentId);


    List<LeaveEnterSchoolListResult> leaveEnterSchoolStatisticsList(@Param("classIdList") List<Long> classIdList);

    /**
     * 根据用户id查询可管理的学校
     * @param userId 用户id
     * @return 根据用户id查询可管理的学校
     */
    List<UserSchoolListPo> getUserSchoolList(Long userId);

    List<UserSchoolListPo> getSchoolListByRegionId(List<Long> districtIdList);

    /**
     * 根据学校id查询学校信息（短信及刷卡记录查询——刷卡——根据终端编号查询终端信息）
     * @param schoolId 学校id
     *
     * @return 学校信息
     */
    SchoolInfoPO getSchoolInfoBySchoolId(Long schoolId);

    /**
     * 根据学校id查询学校管理员
     * @param schoolId 学校id
     * @return 学校管理员
     */
    List<SchoolManagerUserPO> getManagerUserBySchoolId(Long schoolId);



    IPage<SimLogPO> getToDaySimLogs(@Param("parma") IPage<SimLogPO> page);
}
