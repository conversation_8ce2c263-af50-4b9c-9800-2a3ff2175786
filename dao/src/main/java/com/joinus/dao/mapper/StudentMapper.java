package com.joinus.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.joinus.dao.StudentEntity;
import com.joinus.dao.StudentInSchoolStatusEntity;
import com.joinus.dao.model.dto.StudentBasicInfoDTO;
import com.joinus.dao.po.AssessmentEmployeePO;
import com.joinus.dao.po.SwipingCardStudentInfoPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR> maweihua
 * @create 2024-11-29 09:30:11
 */
public interface StudentMapper extends BaseMapper<StudentEntity> {
    void updateCardStatus(Long studentId);


    /**
     * 根据学生id获取学生信息
     *
     * @param studentId 学生id
     * @return 学生信息
     */
    StudentEntity loadStudentById(Long studentId);

    int queryAllStudentByClassIdSize(@Param("classIdList") List<Long> classIdList,@Param("schoolId") Long schoolId);
    List<StudentEntity> queryAllStudentByClassId(@Param("classIdList") List<Long> classIdList,@Param("schoolId") Long schoolId);
    List<StudentInSchoolStatusEntity> queryInSchoolStudentByClassIdSql(@Param("classIdList") List<Long> classIdList);

    /**
     * []
     * @return java.util.List<com.joinus.admin.model.po.AssessmentEmployeePO>
     * @description 查询公司考核人员列表
     * <AUTHOR>
     * @date 2024/4/25 15:59
     */
    List<AssessmentEmployeePO> listAssessmentEmployees(@Param("classId") Long classId);

    /**
     * 查询刷卡学生信息
     */
    List<SwipingCardStudentInfoPO> querySwipingCardStudentAndBusinessInfo(String cardNum);


    List<Long> getBindFloorIdsByStudentId(@Param("studentId") Long studentId);

    List<Long> getBindGroupTerminalIdByStudentId(@Param("studentId") Long studentId);


    List<Long> getBindPatrolsTerminalIdByStudentId(@Param("studentId") Long studentId);

    List<Long> getBindPatrolsTerminalIdBySchoolId(@Param("schoolId") Long schoolId);

    List<StudentBasicInfoDTO> getStudentsByParentPhone(@Param("parentPhone")String parentPhone);



}
