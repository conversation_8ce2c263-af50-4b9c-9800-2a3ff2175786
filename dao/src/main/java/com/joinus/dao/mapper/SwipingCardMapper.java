package com.joinus.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.joinus.dao.CallSigninRecordEntity;
import com.joinus.dao.model.dto.SwipingCardDTO;
import com.joinus.dao.po.SwipingCardPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR> anpengyu
 * @create 2024/5/28 10:03
 */
public interface SwipingCardMapper extends BaseMapper<CallSigninRecordEntity> {

    IPage<SwipingCardPO> querySwipingRecordForHistory(IPage<SwipingCardPO> pageParameters,@Param("param") SwipingCardDTO param);

    IPage<SwipingCardPO> queryCardSigninRecordForToday(IPage<SwipingCardPO> pageParameters, @Param("param") SwipingCardDTO param);


}
