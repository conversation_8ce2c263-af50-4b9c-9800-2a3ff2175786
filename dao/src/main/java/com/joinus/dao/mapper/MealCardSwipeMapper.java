package com.joinus.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.joinus.dao.MealCardSwipeEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 配餐刷卡数据 Mapper
 */
public interface MealCardSwipeMapper extends BaseMapper<MealCardSwipeEntity> {
    
    /**
     * 获取当天配餐刷卡学生数据
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 刷卡数据列表
     */
    List<Map<String, Object>> getTodayMealCardSwipeData(@Param("startTime") String startTime, @Param("endTime") String endTime);
}
