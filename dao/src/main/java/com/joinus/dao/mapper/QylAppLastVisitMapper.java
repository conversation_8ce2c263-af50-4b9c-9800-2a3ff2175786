package com.joinus.dao.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.joinus.dao.QylAppLastVisitEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
@DS("clickhouse")
public interface QylAppLastVisitMapper extends BaseMapper<QylAppLastVisitEntity> {
    List<QylAppLastVisitEntity> getQylAppLastVisitByStudentList(@Param("studentIdList")List<Long> studentIdList);


    List<Map<Long, Integer>> getQylAppActiveStudentsLast30Days();

}
