package com.joinus.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.joinus.dao.entity.CardCashback;
import com.joinus.dao.po.CardCashbackPO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【T_CARD_CASHBACK(返现记录明细)】的数据库操作Mapper
* @createDate 2024-07-15 16:42:04
* @Entity generator.domain.CardCashback
*/
public interface CardCashbackMapper extends BaseMapper<CardCashback> {


    List<CardCashbackPO> queryByLogdateAndSchoolId(@Param("logdate") Date logdate,
                                             @Param("schoolIdList") List<Long> schoolIdList);
}
