package com.joinus.dao;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper=false)
@TableName("T_DORM")
@KeySequence("SEQ_T_DORM")
public class DormEntity  {
    @TableId(value = "ID", type = IdType.INPUT)
    private Long id;
    @TableField("isactive")
    private Integer isActive;
    @TableField("dorm_name")
    private String dormName;
    @TableField("DORM_TYPE")
    private Integer dormType;
    @TableField("Num")
    private Integer Num;
    @TableField("REMAIN_NUM")
    private Integer remianNum;
    @TableField("LAYER_ID")
    private Integer layerId;

}
