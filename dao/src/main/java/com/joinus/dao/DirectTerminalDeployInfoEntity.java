package com.joinus.dao;

import com.baomidou.mybatisplus.annotation.*;
import com.github.houbb.heaven.util.lang.ObjectUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.util.Map;

@Data
@EqualsAndHashCode(callSuper = false)
@TableName("T_DIRECT_TERMINAL_DEPLOY_INFO")
@KeySequence("SEQ_T_DIRECT_TER_DEPLOY_INFO")
@Slf4j
public class DirectTerminalDeployInfoEntity {

    @TableId(value = "id", type = IdType.INPUT) // 如果id手动生成或通过其他方式生成，可以更改IdType类型
    private Long id;

    @TableField("terminal_num")
    private String terminalNum;

    @TableField("school_id")
    private Long schoolId;

    @TableField("student_id")
    private Long studentId;

    @TableField("deploy_info")
    private String  deployInfo;

    /**
     *  0-未下发, 1-已下发, 2-待删除-3已删除 4-超过10次失败
     */
    @TableField("status")
    private Integer status;

    @TableField("created_at")
    private LocalDateTime createdAt;

    @TableField("updated_at")
    private LocalDateTime updatedAt;

    @TableField("remarks")
    private String remarks;

    @TableField("isactive")
    private Integer isactive;

    @TableField("manufacturer_id")
    private Long manufacturerId;

}
