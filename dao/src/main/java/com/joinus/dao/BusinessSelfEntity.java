package com.joinus.dao;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import java.util.Date;

/**
 * <p>
 * 自推广业务开通表
 * </p>
 */
@Data
@EqualsAndHashCode
@Accessors(chain = true)
@TableName("T_BUSINESS_SELF")
@KeySequence("SEQ_T_BUSINESS_SELF")
public class BusinessSelfEntity {

    private static final long serialVersionUID = 1L;

    @TableId("ID")
    private Long id;

    @TableField("BUSINESS_TYPE")
    private Integer businessType;

    @TableField("BUSINESS_ID")
    private Long businessId;

    @TableField("PRODUCT_ID")
    private Long productId;

    @TableField("SUB_PRODUCT_ID")
    private Long subProductId;

    @TableField("STUDENT_ID")
    private Long studentId;

    @TableField("SCHOOL_ID")
    private Long schoolId;

    @TableField("GRADE_ID")
    private Long gradeId;

    @TableField("CLASS_ID")
    private Long classId;

    @TableField("PHONE_NUM")
    private String phoneNum;

    @TableField("TEL_SEGMENT")
    private String telSegment;

    @TableField("START_TIME")
    private Date startTime;

    @TableField("END_TIME")
    private Date endTime;

    @TableField("CREATE_TIME")
    private Date createTime;

    @TableField("UPDATE_TIME")
    private Date updateTime;

    @TableField("AUTO_RENEW_STATE")
    private Integer autoRenewState;


}