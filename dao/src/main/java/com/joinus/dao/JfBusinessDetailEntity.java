package com.joinus.dao;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

@Data
@EqualsAndHashCode
@Accessors(chain = true)
@TableName("jf.T_JF_BUSINESS_DETAILS")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class JfBusinessDetailEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "ID", type = IdType.AUTO)
    private Long id;

    /**
     * 积分账户id
     */
    private Long accountId;

    /**
     * 系统平台编号
     */
    private String sysCode;

    /**
     * 流水号
     */
    private String lsh;

    /**
     * 源流水号(扣减返销，充值返销时必填)
     */
    private String srcLsh;

    /**
     * 积分
     */
    private BigDecimal fee;

    /**
     * 创建时间
     */
    private Date createDt;

    /**
     * 操作类型1扣减2充值11扣减返销21充值返销23app首次登录加积分24通知增加积分25作业增加积分30合并学校管理员积分
     */
    private Integer opType;

    /**
     * 操作时间
     */
    private Date opDt;

    /**
     * 最后一次修改时间
     */
    private Date lastMdfDt;

    /**
     * 状态:1正常2返销99删除
     */
    private Integer status;

    /**
     * 备注
     */
    private String notes;

    /**
     * 签名
     */
    private String sign;

    /**
     * 商品编号
     */
    private String productCode;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 1正常,0删除
     */
    private Integer isactive;

    /**
     * 教师id
     */
    private Long teacherId;

    /**
     * 人人商城v5member_id
     */
    private Long storeV5MemberId;
}
