package com.joinus.dao;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
@TableName("t_sync_student_error_list")
@KeySequence("SEQ_SYNC_STUDENT_ERROR_LIST")
public class SyncStudentErrorEntity {

    @TableId(value = "ID",type = IdType.INPUT)
    private Long id;
    @TableField("school_id")
    private Long schoolId;
    @TableField("student_name")
    private String studentName;
    @TableField("error_type")
    private Integer errorType;
    @TableField("error_msg")
    private String errorMsg;
    @TableField("student_id")
    private Long studentId;

}
