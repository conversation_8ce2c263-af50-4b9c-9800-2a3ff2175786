package com.joinus.dao;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.joinus.dao.model.StatisticalSchool;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 学校表
 */
@Data
@EqualsAndHashCode(callSuper=false)
@TableName("T_SCHOOL")
public class SchoolEntity  {
    private Long id;/* 主键id */
    @TableField("charge_time")
    private Date chargeTime;/* 业务变更时间 */
    @TableField("dorm_time")
    private String dormTime;/* 宿舍熄灯时间 */
    @TableField("groupid")
    private String groupId;/* 集团id */
    @TableField("isactive")
    private Integer isActive;/* 是否在线 */
    private String iskai;/* 业务开通状态 01 新增 02 终止 03 暂停 04 恢复 05 变更 */
    private Integer isunify;/* 全校是否统一考勤 1 是 0 否 */
    private Integer isverify;/* 是否校验 */
    private Integer iswhite;/* 白名单标志 0没有白名单 1全校白名单 2住宿生白名单 */
    @TableField("market_manager")
    private String marketManager;/* 市场负责人姓名 */
    @TableField("market_telnum")
    private String marketTelnum;/* 市场负责人手机号 */
    @TableField("menwei_msg_flag")
    private Integer menweiMsgFlag;/* 门卫信息标志 */
    private String msgport;/* 每个学校一个, 短信端口号 (二级集团客户的扩展码) */
    private String prodid;/* 业务代码 */
    @TableField("school_code")
    private String schoolCode;/* 学校编号 (集团id) */
    @TableField("school_desc")
    private String schoolDesc;/* 学校描述 */
    @TableField("school_name")
    private String schoolName;/* 学校名称 */
    @TableField("school_address")
    private String schoolAddress;/* 学校地址 */

    private Integer status;/* 收费/适用/未开通 */
    @TableField("summer_end")
    private String summerEnd;/* 暑假截至日期 */
    @TableField("summer_start")
    private String summerStart;/* 暑假起始日期 */
    @TableField("winter_end")
    private String winterEnd;/* 寒假截至日期 */
    @TableField("winter_start")
    private String winterStart;/* 寒假起始日期 */
    @TableField("school_type_id")
    private Integer schoolTypeId;/* 小学、初中、高中 */
    @TableField("parent_id")
    private Integer parentId;/**/
    @TableField("region_id")
    private Integer regionId;/* 区域id,对应sys_region_new表的other_id */
    @TableField("new_region_id")
    private String newRegionId;/* 区域id，对应sys_region_new表的region_id */
    @TableField("ismanager")
    private Integer isManager;/* 是否纳入卡库管理 默认为1：表示该学校纳入卡库管理 0：表示该学校不纳入卡库管理 */
    @TableField("add_time")
    private String addTime;/* 添加时间 */
    @TableField("business_flag")
    private String businessFlag;//业务标识
    @TableField("custom_level")
    private String customLevel;//用户等级  1:A , 2:B , 3:C ,4:D
    @TableField("card_hardtype")
    private String cardHardtype;//硬件类型
    @TableField("card_type")
    private String cardType;//卡品类型
    @TableField("wm_teacher_groupid")
    private Long wmTeacherGroupid;/* 微商城老师分组groupID */
    @TableField("wm_parent_groupid")
    private Long wmParentGroupid;/* 微商城家长分组groupID */
    @TableField("ten_qinphone")
    private Integer tenQinphone;//是否支持10个亲情号码
    @TableField("yxopen_buss")
    private Integer yxOpenBuss;//迎新开通业务方式 1回复短信"是"开通   0 验证码开通
    private Integer ismonitor;//APP中在校视频 0:不展示（默认）1:视频+音频 2:仅视频
    @TableField("one_cartoon_pay")
    private Integer oneCartoonPay;//是否开通一卡通支付  0 否   1 是 默认0
    @TableField("MESSAGE_FLAG")
    private Integer messageFlag;//是否发送营销短信
    @TableField("STUDENT_LOCATION")
    private Integer studentLocationFlag;//学生定位厂商(对应数据库字段:STUDENT_LOCATION)
    @TableField("LEAVE_TYPE")
    private Integer leaveType;//请假类型
    @TableField("SCHOOL_PAY")
    private Integer schoolPay;//校园缴费
    //是否开启视频电话
    @TableField("is_open_video_phone")
    private Integer isOpenVideoPhone;
    //打电话（试用、未开通）提醒短信  1开通  0关闭
    @TableField("call_remind_message")
    private Integer callRemindMessage;
    //补卡功能扩展
    @TableField("school_nature")
    private Integer schoolNature;// 枚举类SchoolNatureEnum 学校性质(1:校讯通转型学校; 2:一卡通学校; 4:人脸识别方式进出; 8:其他; 16:自推广学校; 32:一本书捐书学校; 64:一本书受捐学校)
    @TableField("INTERACTION_FLAG")
    private Integer interactionFlag;
    /**
     * 套餐配置方式{0:按学校配置; 1:按年级配置}
     */
    @TableField("BIZ_CONFIG_METHOD")
    private Integer bizConfigMethod;
    @TableField("INIT_FEATURE_TIME")
    private String initFeatureTime;
    @TableField("INIT_FEATURE_FLAG")
    private String initFeatureFlag;
    @TableField("is_show_open_biz")
    private Integer isShowOpenBiz;

    /**
     *
     * @return
     * @description 请假后进出校限制：0不限制 1仅进出一次(手动销假) 2仅进出一次(进校与出校间隔大于一定时间，自动销假)
     * <AUTHOR>
     * @date 2022/11/14 09:39
     */
    @TableField("leave_access_limit")
    private Integer leaveAccessLimit;

    @ApiModelProperty(value = "学校名称全拼")
    @TableField("SCHOOL_NAME_JP")
    private String schoolNameJp;
    @ApiModelProperty(value = "学校名称简拼")
    @TableField("SCHOOL_NAME_QP")
    private String schoolNameQp;
    /**
     *
     * @return
     * @description 请假后进校与出校间隔大于此时间，自动销假，单位分。
     * <AUTHOR>
     * @date 2022/11/14 09:39
     */
    @TableField("leave_resume_interval")
    private Integer leaveResumeInterval;

    @TableField(exist = false)
    private String cardHardTypeName;
    @TableField(exist = false)
    private String cardTypeName;
    /*
     * 是否显示APP积分商城　0:不显示　1:显示
     */
    @TableField("VIEW_CREDIT_MALL")
    private Integer viewCreditMall;

    /**
     * 经度
     */
    @TableField("longitude")
    private BigDecimal longitude;
    /**
     *纬度
     */
    @TableField("latitude")
    private BigDecimal latitude;

}
