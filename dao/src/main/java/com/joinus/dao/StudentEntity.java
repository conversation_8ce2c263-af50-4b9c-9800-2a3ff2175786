package com.joinus.dao;

import com.alibaba.fastjson2.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.joinus.dao.enums.*;
import lombok.*;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper=false)
@TableName(value = "T_STUDENT")
@KeySequence("SEQ_T_STUDENT")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class StudentEntity  {

    /* 学生ID */
    @TableId(value = "ID",type = IdType.INPUT)
    private Long id;
    /** 增加时间 */
    @DateTimeFormat
    @JSONField(format = "yyyy-MM-dd HH:mm")
    @TableField(value = "add_time")
    private Date addTime;
    /** 地址 */
    private String address;
    /* 生日 */
    @TableField("birth")
    private String birth;
    @TableField("cardcode")
    private String cardCode;/* 卡号 */
    @TableField("identity")
    private String identity;/* 身份证 */
    @TableField("in_year")
    private String inYear;/* 年龄 */
    @TableField(value = "isactive")
    private Integer isActive;/* 是否删除，0删除1存在 */
    @TableField(value = "isbus")
    private StudentIsBusEnum isBus;/* 是否坐校车，0 无效 1有效 */
    @TableField(value = "isdorm")
    private StudentDormEnum isDorm;/* 是否住宿生，0 无效 1有效 2待定*/
    @TableField("is_guashi")
    private StudentGuashiEnum isGuaShi;/* 是否挂失 1 是 0 否 */
    @TableField("is_kai")
    private Integer isKai;/* 是否开通业务: 1:是; 0:否 */
    @TableField("kai_num")
    private Integer kaiNum;/* 开通业务数量 */
    @TableField("order_num")
    private String orderNum;/* 订购数量 */
    @TableField("region_id")
    private Integer regionId;/* 区域id */
    @TableField("school_id")
    private Long schoolId;/* 学校id */
    @TableField("sex")
    private StudentSexEnum sex;/* 性别 */
    @TableField("student_code")
    private String studentCode;/* 学生代码（学生编号） */
    @TableField("student_img")
    private String studentImg;/* 学生肖像 */
    @TableField("student_name")
    private String studentName;/* 学生姓名 */
    @TableField("nation_id")
    private Integer nationId;/* 国籍id */
    @TableField("class_id")
    private Long classId;/* 班级id */
    @TableField("code_num")
    private String codeNum;/* 卡数 */
    @TableField("photo_name")
    private String photoName;/* 照片名字 */
    @TableField("istime1")
    private Integer isTime1;/* 时段1标志，0 无效 1有效 */
    @TableField("istime2")
    private Integer isTime2;/* 时段2标志，0 无效 1有效 */
    @TableField("istime3")
    private Integer isTime3;/* 时段3标志，0 无效 1有效 */
    @TableField("student_auth")
    private Integer studentAuth;/*学生认证状态 0未认证 1已认证*/
    @TableField("bulk_paste_img")
    private String bulkPasteImg;/* 学生大头贴 默认是 ''*/
    @TableField("SECOND_ID")
    private Integer secondId; //制卡关联ID
    @TableField("CARD_STATUS")
    private Integer cardStaus;//制卡使用状态  1:已登记（admin的未处理）2:制卡中（admin的加工中）3:配送中 4:正常使用（admin已领卡）这张表只使用 3(未启用)和4(已启用)
    @TableField("make_card")
    private String makeCard; //学生编号
    @TableField("SIGNTIME")
    private Date signTime; //二次制卡登记时间
    @TableField("SIGNER")
    private String signEr; //二次制卡登记人
    @TableField("COMMITER")
    private String commiTer;/*提交制卡人*/
    private String imei;
    @TableField("student_dock_code")
    private String studentDockCode;
    @TableField("PRAPE_CARD")
    private String prapeCard; //预制卡号
    //学生人脸识别特征码
    @TableField("face_feature")
    private String faceFeature;
    @TableField("tel_num")
    private String telNum;
    @TableField("bus_feature")
    private String busFaceFeature;
    private Double balance; //一卡通余额
    @DateTimeFormat
    @JSONField(format = "yyyy-MM-dd HH:mm")
    @TableField("audit_time")
    private Date auditTime; //照片添加时间
    @TableField("passport_name")
    private String passportName;//护照名称
    @TableField("passport_no")
    private String passportNum;//护照编号
    @DateTimeFormat
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @TableField("COMMITTIME")
    private Date commitTime;/* 二次制卡提交时间 */
    @TableField("rank_id")
    private Long rankId;
    @DateTimeFormat
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @TableField("update_time")
    private Date updateTime; //学生信息修改时间
    @TableField("info_source")
    private Integer infoSource; //学生信息来源(通过什么方式添加的)，1 ijx平台  2 admin平台  3迎新系统
    @TableField("audit_status")
    private Integer auditStatus; //学生图片审核状态  0:未审核 1:审核通过	2未通过（已通知家长）	3未通过（家长已修改）
    @TableField("demo_qinqing")
    private String demoQinqing;
    @TableField("YKT_CARD_STATUS")
    private Integer yktCardStatus;//一卡通卡状态，0：正常，1挂失
    @TableField("BIRTHDAY_TYPE")
    private Integer birthDayType;
    @TableField("DISABLE_LIFE_SVC")
    private Integer disableLifeSvc;//是否屏蔽生活服务(设备端禁用生活服务、不发放优惠券)0:否,1:是(云校柜)

    /**
     * 拓展字段
     *
     * @return
     */
    @TableField(exist = false)
    private String nationName;//民族名称
    @TableField(exist = false)
    private Boolean isQinQingSchool;//学生所在学生是否是支持10个亲情号码的学校
    @TableField(exist = false)
    private BigDecimal walletBalance; //青于蓝钱包余额
    @TableField(exist = false)
    private String className;
    @TableField(exist = false)
    private String gradeName;
    @TableField(exist = false)
    private String schoolName;
    @TableField(exist = false)
    private Integer state;/*请假状态*/
    @TableField(exist = false)
    private String status;//业务开通状态
    @TableField("SOCIAL_SECURITY_CARD")
    private String socialSecurityCard;
    @TableField("SOCIAL_SECURITY_NUM")
    private String socialSecurityNum;//学生社保卡号
    @TableField("ONEBOOK_AGREEMENT")
    private StudentOneBookAgreementEnum oneBookAgreement;//是否已签署一本书协议 0:未签署,1:已签署
    @TableField("CHINESE_BIRTHDAY")
    private String chineseBirthday;//农历生日
    @TableField("FEATURE_UPDATE_TIME")
    private String featureUpdateTime;//特征码更新时间
    @TableField("AUDIT_SUCCESS_NUMBER")
    private String auditSuccessNumber;//学生照片审核成功个数
    @TableField("AUDIT_ERROR_MSG")
    private String auditErrorMsg;//学生照片审核不通过原因
    @TableField("UP_FLAG")
    private String upFlag;//升级标示,0升级中 1 升级后
    @TableField("COMPANY")
    private String company;//配送公司
    @TableField("UUID")
    private String UUID;

    @TableField(exist = false)
    private Long gradeId;
    @TableField(exist = false)
    private boolean isUpdateImg;


}
