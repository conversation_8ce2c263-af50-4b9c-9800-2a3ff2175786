package com.joinus.dao;

import com.baomidou.mybatisplus.annotation.*;
import lombok.*;

import java.util.Date;

@Data
@EqualsAndHashCode(callSuper=false)
@TableName("T_CAMERA_LOCATION")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CameraLocationEntity {
    /** 区域详细位置id */
    @TableId(value = "ID")
    private Long id;
    /** 区域详细位置 */
    @TableField("LOCATION_NAME")
    private String locationName;
    /** 学校id */
    @TableField("SCHOOL_ID")
    private Long schoolId;
    /** 更新时间 */
    @TableField("UPDATEDATE")
    private Date updateDate;
    /** 区域id */
    @TableField("AREA_ID")
    private Long areaId;
}
