package com.joinus.dao;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;

@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("T_TERMINAL_IMGS")
@KeySequence("SEQ_TERMINAL_IMGS")
public class TerminalImgsEntity implements Serializable {

    @TableId(value = "ID", type = IdType.INPUT)
    private Long id;
    @TableField("imgurl1")
    private String imgurl1;
    @TableField("imgurl2")
    private String imgurl2;
    @TableField("imgurl3")
    private String imgurl3;
    @TableField("imgurl4")
    private String imgurl4;

}
