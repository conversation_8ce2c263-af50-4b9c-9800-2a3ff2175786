package com.joinus.dao;

/**
 * 外部平台对接订单表
 *
 * <AUTHOR> anpy
 * @create 2023/8/25 13:49
 */

import com.baomidou.mybatisplus.annotation.*;
import lombok.Builder;
import lombok.Data;

import java.util.Date;

@TableName("T_EXTERNAL_ORDER")
@KeySequence("SEQ_T_EXTERNAL_ORDER")
@Data
@Builder
public class ExternalOrderEntity {
    @TableId(value = "ID",type = IdType.INPUT)
    private Long id;
    @TableField("order_id")
    private String orderId;
    @TableField("out_order_id")
    private String outOrderId;
    @TableField("product_info")
    private String productInfo;
    @TableField("snapshot_url")
    private String snapShotUrl;
    @TableField("business_type")
    private Integer businessType;
    @TableField("operation_type")
    private Integer operationType;
    @TableField("related_order_id")
    private String relatedOrderId;
    @TableField("platform_type")
    private Integer platformType;
    @TableField("student_id")
    private Long studentId;
    @TableField("identity")
    private String identity;
    @TableField("school_id")
    private Long schoolId;
    @TableField("amount")
    private Double amount;

    @TableField("create_time")
    private Date createTime;

    @TableField("update_time")
    private Date updateTime;
}
