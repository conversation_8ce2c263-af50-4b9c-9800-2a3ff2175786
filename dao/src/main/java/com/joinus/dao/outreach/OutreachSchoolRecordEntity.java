package com.joinus.dao.outreach;

import com.alibaba.fastjson2.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import java.util.Date;


/**
 * 走访学校记录实体类
 * <AUTHOR> mawh
 * @create 2024-4-9 09:07:40
 */
@Data
@EqualsAndHashCode(callSuper=false)
@TableName("t_outreach_school_record")
@KeySequence("seq_t_outreach_school_record")
public class OutreachSchoolRecordEntity {


    @TableId(value = "id", type = IdType.INPUT)
    private Long id;/* 走访记录Id */

    /** 走访日期 */
    @DateTimeFormat
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @TableField("outreach_date")
    private Date outreachDate;

    @TableField("organization_id")
    private String organizationId;

    @TableField("organization_name")
    private String organizationName;

    /** 类型  1 admin 系统 2 走访自己添加 */
    @TableField("school_type")
    private String schoolType;

    /**  0否 1是 */
    @TableField("isactive")
    private Integer isactive;


    @TableField("has_access_termianl")
    private Integer hasAccessTermianl;

    @TableField("has_telephone_termianl")
    private Integer hasTelephoneTermianl;

    @TableField("has_other_termianl")
    private Integer hasOtherTermianl;

    @TableField("has_remove_terminal")
    private Integer hasRemoveTerminal;

    @TableField("has_add_install_terminal")
    private Integer hasAddInstallTerminal;


    @TableField("has_add_install_apply")
    private Integer hasAddInstallApply;

    @TableField("has_guard_issues")
    private Integer hasGuardIssues;

    @TableField("met_principal")
    private Integer metPrincipal;


    @TableField("visit_people")
    private String visitPeople;

    @TableField("school_demand")
    private String schoolDemand;

    @TableField("issues")
    private String issues;

    @TableField("growth_opportunities")
    private String growthOpportunities;


    @TableField("visit_user_id")
    private Long visitUserId;


    @TableField("visit_user_name")
    private String visitUserName;

    @TableField("visit_user_phone")
    private String visitUserPhone;


    @TableField("CREATED_AT")
    private Date createTime;

    @TableField("UPDATED_AT")
    private Date updateTime;

}
