package com.joinus.dao.outreach;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
* 走访单位实体类
* <AUTHOR> mawh
* @create 2024-4-9 09:07:40
*/
@Data
@EqualsAndHashCode(callSuper=false)
@TableName("T_OUTREACH_ORGANIZATION")
public class OutreachOrganizationEntity {
    /** 单位id */
    @TableId(value = "id")
    private String id;

    /** 单位名称名称 */
    @TableField("name")
    private String name;

    /** 类型  1移动 2学校 */
    @TableField("type")
    private String type;

    /**  0否 1是 */
    @TableField("isactive")
    private Integer isactive;

    @TableField("CREATED_AT")
    private Date createTime;

    @TableField("UPDATED_AT")
    private Date updateTime;

}
