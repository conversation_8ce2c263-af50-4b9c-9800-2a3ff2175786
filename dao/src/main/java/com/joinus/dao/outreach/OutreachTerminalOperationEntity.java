package com.joinus.dao.outreach;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper=false)
@TableName("t_outreach_terminal_operation")
@KeySequence("seq_outreach_ter_operation")
public class OutreachTerminalOperationEntity {

    @TableId(value = "id", type = IdType.INPUT)
    private Long id;/* Id */

    @TableField("outreach_id")
    private Long outreachId;

    @TableField("terminal_type")
    private Integer terminalType;

    @TableField("operation_type")
    private Integer operationType;

    @TableField("status")
    private Integer status;

    @TableField("remarks")
    private String remarks;

}
