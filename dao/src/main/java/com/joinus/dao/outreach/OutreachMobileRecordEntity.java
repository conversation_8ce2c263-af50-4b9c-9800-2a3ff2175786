package com.joinus.dao.outreach;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;


/**
 * 走访移动记录实体类
 * <AUTHOR> mawh
 * @create 2024-4-9 09:07:40
 */
@Data
@EqualsAndHashCode(callSuper=false)
@TableName("t_outreach_mobile_record")
@KeySequence("seq_t_outreach_mobile_record")
public class OutreachMobileRecordEntity {


    @TableId(value = "id", type = IdType.INPUT)
    private Long id;/* 走访记录Id */

    /** 走访日期 */
    @DateTimeFormat
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @TableField("outreach_date")
    private Date outreachDate;

    @TableField("organization_id")
    private String organizationId;

    @TableField("organization_name")
    private String organizationName;

    /**  0否 1是 */
    @TableField("isactive")
    private Integer isactive;


    @TableField("visit_people")
    private String visitPeople;

    @TableField("work_report")
    private String workReport;

    @TableField("customer_demand")
    private String customerDemand;

    @TableField("business_opportunities")
    private String businessOpportunities;

    @TableField("city_manager_feedback")
    private String cityManagerFeedback;

    @TableField("issues")
    private String issues;

    @TableField("growth_opportunities")
    private String growthOpportunities;


    @TableField("visit_user_id")
    private Long visitUserId;


    @TableField("visit_user_name")
    private String visitUserName;

    @TableField("visit_user_phone")
    private String visitUserPhone;


    @TableField("CREATED_AT")
    private Date createTime;

    @TableField("UPDATED_AT")
    private Date updateTime;

}
