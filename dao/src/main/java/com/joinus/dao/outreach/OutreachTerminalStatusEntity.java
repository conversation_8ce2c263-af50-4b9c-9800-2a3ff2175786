package com.joinus.dao.outreach;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper=false)
@TableName("t_outreach_terminal_status")
@KeySequence("seq_outreach_terminal_status")
public class OutreachTerminalStatusEntity {

    @TableId(value = "id", type = IdType.INPUT)
    private Long id;/* Id */

    @TableField("outreach_id")
    private Long outreachId;

    @TableField("terminal_type")
    private Integer terminalType;

    @TableField("terminal_name")
    private String terminalName;

    @TableField("other_function")
    private String otherFunction;

    @TableField("status")
    private Integer status;

    @TableField("remarks")
    private String remarks;



}
