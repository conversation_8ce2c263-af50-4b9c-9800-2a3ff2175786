package com.joinus.dao;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-11
 */
@Data
@Accessors(chain = true)
@TableName("T_BED")
@KeySequence("SEQ_T_BED")
public class BedEntity {

    private static final long serialVersionUID = 1L;

    @TableId(value = "ID", type = IdType.INPUT)
    private Long id;

    @TableField("ALLOT")
    private Integer allot;

    @TableField("isactive")
    private Integer isActive;

    @TableField("BED_NAME")
    private String bedName;

    @TableField("DORM_ID")
    private Long dormId;

    @TableField("STUDENT_ID")
    private Long studentId;


}
