
package com.joinus.dao;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;

@Data
@TableName(value = "t_terminal_sim_balance_history")
public class TerminalSmsBalanceHistoryEntity {
    @TableId(value = "ID",type = IdType.INPUT)
    private Long id;

    @TableField(value = "TERMINAL_ID")
    private Long terminalId;

    @TableField(value = "SIM_NUM")
    private String simNum;

    @TableField(value = "LATEST_SMS_ID")
    private Long lastestSmsId;

    @TableField(value = "CURRENT_MONTH_EXPENSE")
    private double currentMonthExpense;


    @TableField(value = "balance_date")
    private Date balanceDate;

}
