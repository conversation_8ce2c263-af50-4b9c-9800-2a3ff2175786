package com.joinus.dao;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@TableName(value ="sys_dict")
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class DictionaryEntity {
	@TableId(value = "ID",type = IdType.INPUT)
	private Long id;/**/
	@TableField("DICT_ID")
	private Integer dictId;/**/
	@TableField("DICT_NAME")
	private String dictName;/**/
	@TableField("DICT_DESC")
	private String dictDesc;/**/
	@TableField("EXTEND")
	private String extend;/**/
	@TableField("ISACTIVE")
	private Integer isactive;/**/
	@TableField("ORDER_BY")
	private Integer orderBy;/**/


}
