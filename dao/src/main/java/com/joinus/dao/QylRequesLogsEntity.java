package com.joinus.dao;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> maweihua
 * @create 2024-12-27 13:45:44
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("qyl_app_last_login")
public class QylRequesLogsEntity {

    @TableField("student_id")
    private Long studentId;

    @TableField("parent_id")
    private Long parentId;

    @TableField("last_login_time")
    private Long lastVisitTime;

}
