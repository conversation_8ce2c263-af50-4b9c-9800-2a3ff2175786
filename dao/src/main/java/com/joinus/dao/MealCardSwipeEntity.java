package com.joinus.dao;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 配餐刷卡数据实体
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_meal_card_swipe")
public class MealCardSwipeEntity extends BaseEntity {
    
    /**
     * 学生ID
     */
    private Long studentId;
    
    /**
     * 学生姓名
     */
    private String studentName;
    
    /**
     * 学生卡号
     */
    private String cardNo;
    
    /**
     * 刷卡时间
     */
    private LocalDateTime swipeTime;
    
    /**
     * 终端ID
     */
    private Long terminalId;
    
    /**
     * 终端编号
     */
    private String terminalNo;
    
    /**
     * 刷卡类型（1：早餐，2：午餐，3：晚餐）
     */
    private Integer mealType;
    
    /**
     * 刷卡状态（0：失败，1：成功）
     */
    private Integer status;
    
    /**
     * 备注
     */
    private String remark;
}
