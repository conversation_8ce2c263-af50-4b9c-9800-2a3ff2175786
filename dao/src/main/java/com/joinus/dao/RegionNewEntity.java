package com.joinus.dao;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR> anpy
 * @create 2024/5/13 15:10
 */

@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("sys_region_new")
public class RegionNewEntity {
    /* 区域id，主键 */
    @TableId
    private String regionId;
    /* 父节点区域id */
    private String superRegionId;
    /* 区域名称 */
    private String regionName;
    /* 父节点区域名称 */
    private String superRegionName;
    /* 区域全路径名称 */
    private String fullName;
    /* 是否删除，0 否 1 是 */
    private Integer isActive;
    /* 区域代码 */
    private String regionCode;
    /* 是否是叶子节点， 1 是 0 否 */
    private String isleaf;
    /* Sys_Region表id */
    private Integer otherid;
    /* 区域级别 1 省 2 市 3 县/区 */
    private Integer regionLevel;

}
