package com.joinus.dao;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.util.Date;

/**
 * 生活服务——留言板
 *
 * <AUTHOR> anpy
 * @create 2023/7/5 10:30
 */
@Data
@TableName("ijx.t_life_service_msg_board")
@KeySequence("SEQ_T_LIFE_SERVICE_MSG_BOARD")
public class LifeServiceMsgBoardEntity {

    @TableId(value = "ID", type = IdType.INPUT)
    private Long id;
    @TableField("parent_id")
    private Long parentId;
    @TableField("student_id")
    private Long studentId;
    @TableField("txt_message")
    private String txtMessage;
    @TableField("media_url")
    private String mediaUrl;
    @TableField("read_state")
    private Integer readState;
    @TableField("message_type")
    private Integer messageType;
    @TableField("school_id")
    private Long schoolId;
    @TableField("create_time")
    private Date createTime;
    @TableField("update_time")
    private Date updateTime;
    @TableField("isactive")
    private Integer isActive;
    @TableField("life_service_type")
    private Integer lifeServiceType;
    @TableField("sender_type")
    private Integer sendType;
    @TableField("MEDIA_DURATION")
    private Integer mediaDuration;
}
