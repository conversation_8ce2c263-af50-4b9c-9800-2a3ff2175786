package com.joinus.dao;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@TableName("T_QUICK")
@Data
public class QuickEntity {
    @TableField("ID")
    private Long id; // 主键(SEQ_T_QUICK.NEXTVAL)

    @TableField("QUICK_IMG")
    private String quickImg; // 导航图片

    @TableField("QUICK_NAME")
    private String quickName; // 导航条名称

    @TableField("QUICK_URL")
    private String quickUrl; // 导航条链接

    @TableField("RELATED_ID")
    private Long relatedId; // 二级菜单关联ID    0:代表一级菜单   其它表明是二级菜单 值是所属一级菜单的主键

    @TableField("QUICK_ORDER")
    private Integer quickOrder; // 菜单排序

    @TableField("IS_JF")
    private Integer isJf; // 是否是积分 0:不是   1:是

    @TableField("HIDE_IN_MENU")
    private Integer hideInMenu; // 菜单隐藏:0:不隐藏;1:隐藏
}
