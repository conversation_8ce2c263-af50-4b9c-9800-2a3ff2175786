package com.joinus.dao;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 教师表
 * @TableName T_TEACHER
 */
@TableName(value ="T_TEACHER")
@KeySequence(value = "SEQ_T_TEACHER", dbType = DbType.ORACLE)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TeacherEntity implements Serializable {
    /**
     * 
     */
    @TableId(value = "ID", type = IdType.INPUT)
    private Long id;

    /**
     * 住址
     */
    private String address;

    /**
     * 是否有补卡权限
     */
    @Builder.Default
    private Integer buka = 0;

    /**
     * 卡号
     */
    private String cardcode;

    /**
     * 身份证
     */
    private String identity;

    /**
     * 登录时IP
     */
    private String loginIp;

    /**
     * 登录时间
     */
    private Date loginTime;

    /**
     * 
     */
    private Integer num;

    /**
     * 密码
     */
    private String password;

    /**
     * 性别:0女1男
     */
    private Integer sex;

    /**
     * 
     */
    private String subjectids;

    /**
     * 
     */
    private String subjectnames;

    /**
     * 教师姓名
     */
    private String teacherName;

    /**
     * 手机号
     */
    private String telNum;

    /**
     * 学校ID
     */
    private Long schoolId;

    /**
     * 部门id
     */
    private Long departmentId;

    /**
     * 教师类型:跟sys_dict表中dict_id=2的id类型对应,
     */

    private Long typeId;

    /**
     * 民族，默认汉族
     */
    @Builder.Default
    private Long nationId = 1L;

    /**
     * 是否虚拟账号:0：不是，1：是
     */
    private Integer isVirtualAccount;

    /**
     * 科目ID 
     */
    private Long subjectId;

    /**
     * 创建时间 
     */
    private Date createTime;

    /**
     * 
     */
    private String cookie;

    /**
     * 
     */
    private String confirmcode;

    /**
     * 
     */
    private Date confirmtime;

    /**
     * 
     */
    private String teacherCode;

    /**
     * 角色类型  0:其它  1:学校主管理员  2:普通管理员  3:宿管员 4:年级长  5:班主任 6:普通教师
     */
    @TableField("ROLE_ID")
    private Long roleId;

    /**
     * 教师头像
     */
    private String teacherImg;

    /**
     * 电子邮箱
     */
    private String email;

    /**
     * 是否是环信注册用户:0：不是，1：是
     */
    private String isEmchat;

    /**
     * 是否是校长 0:不是 1:是
     */
    private String isPrincipal;

    /**
     * 教师简介
     */
    private String introduction;

    /**
     * 教师头像上传时间
     */
    private Date headUploadTime;

    /**
     * 教师创建者
     */
    private String creator;

    /**
     * 是否在教师风采里面展示 1展示 0:不展示
     */
    private String isDisplay;

    /**
     * OA系统老师ID
     */
    private String oaPersonId;

    /**
     * OA系统岗位ID
     */
    private String oaPostId;

    /**
     * 教师app登录是否需要短信验证，1需要；0不需要
     */
    private Integer appLoginSms;

    /**
     * 教师信息更新时间
     */
    private Date updateTime;

    /**
     * 微商城用户ID
     */
    private Long wUid;

    /**
     * 微信名
     */
    private String wxName;

    /**
     * 微信openId
     */
    private String openid;

    /**
     * 微信unionID
     */
    private String unionId;

    /**
     * QQ昵称
     */
    private String qqNickName;

    /**
     * QQopenId
     */
    private String qqOpenid;

    /**
     * 前引导语
     */
    private String thelead;

    /**
     * 后引导语
     */
    private String afterlead;

    /**
     * 手机IMEI
     */
    private String imei;

    /**
     * App端用户登录次数
     */
    private Long appTimes;

    /**
     * 是否显示APP积分商城　0:不显示　1:显示
     */
    private Integer viewCreditMall;

    /**
     * 企业号用户id
     */
    private String wechatUserId;

    /**
     * 生日（QYL）
     */
    private Date birthday;

    /**
     * 所在地(qyl)
     */
    private String region;

    /**
     * 访校公众号微信用户OpenID
     */
    private String wxVsOpenid;

    /**
     * 是否设为默认身份：0、否，1、是
     */
    private Integer reserveIsDefault;

    /**
     * 是否设为默认身份：0、否，1、是
     */
    private Integer leaveIsDefault;

    /**
     * 访校公众号登录密码
     */
    private String wxVsPassword;

    /**
     * J套餐是否开通 0未开 1已开
     */
    private Integer isJKai;

    /**
     * 用户设备唯一识别码
     */
    private String deviceUniqueId;

    /**
     * 生日类型 0.公历 1.农历
     */
    private Integer birthdayType;

    /**
     * 农历生日
     */
    private String chineseBirthday;

    /**
     * 教师头衔
     */
    private String teacherTitle;

    /*
     * 逻辑删除标识
     */
    private Integer isactive;

    /**  加密后的密码 bcrypt*/
    private String passwordBcrypt;
}