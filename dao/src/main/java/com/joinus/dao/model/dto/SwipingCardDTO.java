package com.joinus.dao.model.dto;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR> anpengyu
 * @create 2024/10/28 9:56
 */
@Data
public class SwipingCardDTO {

    private Long schoolId;
    private String terminalNum;
    private String cardNum;
    private String type;
    private Integer signType;
    private Integer direction;
    private Integer healthFlag;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "结束时间不能为空")
    private Date dateEndDate;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "开始时间不能为空")
    private Date dateBeginDate;
    private Long gradeId;
    private Long classId;
    private String studentName;
    private String tableName;
    private Boolean moveOutStranger;
}
