package com.joinus.dao.model.po;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> maweihua
 * @create 2024-9-18 13:49:55
 */
@Data
public class TerminalInfoBySchoolAndIdPO {
    @ApiModelProperty(value = "终端号")
    private String terminalNum;
    @ApiModelProperty(value = "终端名称")
    private String terminalName;
    @ApiModelProperty(value = "终端类型")
    private String terminalType;
    @ApiModelProperty(value = "安装状态")
    private String terminalStatus;
    @ApiModelProperty(value = "硬件版本号")
    private String hardVersion;
    @ApiModelProperty(value = "软件版本号")
    private String softVersion;
    @ApiModelProperty(value = "留言下载标志")
    private Integer msgFlag;
    @ApiModelProperty(value = "白名单更新标志位")
    private String whiteFlag;
    @ApiModelProperty(value = "信号强度")
    private Integer baoStrong;
    @ApiModelProperty(value = "响应时间")
    private Integer baoTime;
    @ApiModelProperty(value = "安装位置")
    private String installAddress;
    @ApiModelProperty(value = "安装时间")
    private String installTime;
    @ApiModelProperty(value = "终端连接状态")
    private String isLogin;
    private String lastTime;
    private String lastIp;
    @ApiModelProperty(value = "网关通信序号")
    private String serialNum;
    @ApiModelProperty(value = "sim卡号")
    private String simNum;
    @ApiModelProperty(value = "白名单更新计数")
    private Integer updCount;
    @ApiModelProperty(value = "最后操作时间")
    private String lastOperateTime;
    @ApiModelProperty(value = "最后登陆")
    private String lastLogin;
}
