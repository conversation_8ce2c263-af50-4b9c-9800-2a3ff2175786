package com.joinus.dao.model;

/**
 * <AUTHOR> anpengyu
 * @create 2024/11/18 14:50
 */

import com.baomidou.mybatisplus.core.toolkit.ClassUtils;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import org.modelmapper.ModelMapper;
import org.modelmapper.convention.MatchingStrategies;
import org.springframework.cglib.beans.BeanMap;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;


public class BeanConverter {
    private static final ModelMapper modelMapper = new ModelMapper();

    public BeanConverter() {
    }

    public static ModelMapper getModelMapper() {
        return modelMapper;
    }

    public static <T> Map<String, Object> beanToMap(T bean) {
        Map<String, Object> map = Collections.emptyMap();
        if (null != bean) {
            BeanMap beanMap = BeanMap.create(bean);
            map = new HashMap(beanMap.keySet().size());
            Iterator var3 = beanMap.keySet().iterator();

            while(var3.hasNext()) {
                Object key = var3.next();
                ((Map)map).put(String.valueOf(key), beanMap.get(key));
            }
        }

        return (Map)map;
    }


    public static <T> List<Map<String, Object>> beansToMap(List<T> objList) {
        List<Map<String, Object>> list = Collections.emptyList();
        if (CollectionUtils.isNotEmpty(objList)) {
            list = new ArrayList(objList.size());
            Iterator var4 = objList.iterator();

            while(var4.hasNext()) {
                T anObjList = (T) var4.next();
                Map<String, Object> map = beanToMap(anObjList);
                ((List)list).add(map);
            }
        }

        return (List)list;
    }

    public static <T> List<T> mapToBean(List<Map<String, Object>> mapList, Class<T> beanClass) {
        List<T> list = Collections.emptyList();
        if (CollectionUtils.isNotEmpty(mapList)) {
            list = new ArrayList(mapList.size());
            Iterator var5 = mapList.iterator();

            while(var5.hasNext()) {
                Map<String, Object> map1 = (Map)var5.next();
                T bean = mapToBean(map1, beanClass);
                ((List)list).add(bean);
            }
        }

        return (List)list;
    }

    public static <T> T stringMapToBean(Map<String, String> map, Class<T> beanClass) {
        T entity = ClassUtils.newInstance(beanClass);
        BeanMap beanMap = BeanMap.create(entity);
        beanMap.putAll(map);
        return entity;
    }

    public static <T> T mapToBean(Map<String, Object> map, Class<T> beanClass) {
        T entity = ClassUtils.newInstance(beanClass);
        BeanMap beanMap = BeanMap.create(entity);
        beanMap.putAll(map);
        return entity;
    }

    public static <T> List<T> convert(Class<T> clazz, List<?> list) {
        return CollectionUtils.isEmpty(list) ? Collections.emptyList() : (List)list.stream().map((e) -> {
            return convert(clazz, e);
        }).collect(Collectors.toList());
    }

    public static <T> T convert(Class<T> targetClass, Object source) {
        return getModelMapper().map(source, targetClass);
    }

    static {
        modelMapper.getConfiguration().setFullTypeMatchingRequired(true);
        modelMapper.getConfiguration().setMatchingStrategy(MatchingStrategies.STRICT);
    }
}
