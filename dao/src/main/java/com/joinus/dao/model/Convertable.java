package com.joinus.dao.model;

import java.io.Serializable;

/**
 * <AUTHOR> anpengyu
 * @create 2024/11/18 14:57
 */

public class Convertable implements Serializable {
    private static final long serialVersionUID = 6353564202968451404L;

    public <T> T convert(Class<T> clazz) {
        return BeanConverter.convert(clazz, this);
    }

    protected Convertable(final ConvertableBuilder<?, ?> b) {
    }

    public static ConvertableBuilder<?, ?> builder() {
        return new ConvertableBuilderImpl();
    }

    public Convertable() {
    }

    private static final class ConvertableBuilderImpl extends ConvertableBuilder<Convertable, ConvertableBuilderImpl> {
        private ConvertableBuilderImpl() {
        }

        protected ConvertableBuilderImpl self() {
            return this;
        }

        public Convertable build() {
            return new Convertable(this);
        }
    }

    public abstract static class ConvertableBuilder<C extends Convertable, B extends ConvertableBuilder<C, B>> {
        public ConvertableBuilder() {
        }

        protected abstract B self();

        public abstract C build();

        public String toString() {
            return "Convertable.ConvertableBuilder()";
        }
    }
}
