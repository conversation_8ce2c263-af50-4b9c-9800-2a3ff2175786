package com.joinus.dao.model;
/**
 * 该类是作为统计的一个类，和数据库无直接关系
 * <AUTHOR>
 *
 */
public class StatisticalSchool {
	private String schoolId; /*学校id*/
	private String gradeNumber;/*学校年级数量*/
    private String classNumber;/*学校班级数量*/
    private String studentNumber;/*学校学生数量*/
    private String residenceNumber;/*学校住宿数量*/
    private String dayNumber;/*学校走读数量*/
    private String businessNumber;/*学校正式开通数量*/
    private String useBusinessNumber;/* 学生试用开通数量 */
    private String notbusinessNumber;/*学校未开通数量*/
    
	public String getSchoolId() {
		return schoolId;
	}
	public void setSchoolId(String schoolId) {
		this.schoolId = schoolId;
	}
	public String getGradeNumber() {
		return gradeNumber;
	}
	public void setGradeNumber(String gradeNumber) {
		this.gradeNumber = gradeNumber;
	}
	public String getClassNumber() {
		return classNumber;
	}
	public void setClassNumber(String classNumber) {
		this.classNumber = classNumber;
	}
	public String getStudentNumber() {
		return studentNumber;
	}
	public void setStudentNumber(String studentNumber) {
		this.studentNumber = studentNumber;
	}
	public String getResidenceNumber() {
		return residenceNumber;
	}
	public void setResidenceNumber(String residenceNumber) {
		this.residenceNumber = residenceNumber;
	}
	public String getDayNumber() {
		return dayNumber;
	}
	public void setDayNumber(String dayNumber) {
		this.dayNumber = dayNumber;
	}
	public String getBusinessNumber() {
		return businessNumber;
	}
	public void setBusinessNumber(String businessNumber) {
		this.businessNumber = businessNumber;
	}
	public String getUseBusinessNumber() {
		return useBusinessNumber;
	}
	public void setUseBusinessNumber(String useBusinessNumber) {
		this.useBusinessNumber = useBusinessNumber;
	}
	public String getNotbusinessNumber() {
		return notbusinessNumber;
	}
	public void setNotbusinessNumber(String notbusinessNumber) {
		this.notbusinessNumber = notbusinessNumber;
	}
    
	
}
