package com.joinus.dao.entity;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

@TableName("T_CARD_SIGNIN_RECORD6")
@Data
@KeySequence("SEQ_CARD_SIGNIN_RECORD")
@JsonIgnoreProperties(ignoreUnknown = true)
public class SignIn {
	@TableField(exist = false)
	private final short MAX_VALID_DIRECTION = 9;

	private long id;
	private String terminalNum;
	private long schoolId;
	@TableField("TERMINAL_TYPE_ID")
	private long typeId;
	private String terminalType;
	private String cardNum;
	private float thermometer;
	private Date cardTime;
	private short direction;
	private String imageUrl;
	private Integer reasonCode;
	private String latitude;
	private String longitude;
	private String address;
	private Date saveTime;
	private Integer signType;
	private String subTerminalNum;
	private String healthInfo;		//学生及同住人疫情健康信息 json格式
	private Integer healthFlag = 0;		//学生及同住人健康标识 -1身份信息异常 0 无健康信息 1正常 2健康异常

}
