package com.joinus.dao.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 返现记录明细
 * @TableName T_CARD_CASHBACK
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName(value ="T_CARD_CASHBACK")
public class CardCashback implements Serializable {
    /**
     * 
     */
    private Date logdate;

    /**
     * 学校ID
     */
    private Long schoolId;

    /**
     * 月补卡数量
     */
    private Long replenishmentNum;

    /**
     * 支付金额
     */
    private BigDecimal payNum;

    /**
     * 返佣金额
     */
    private BigDecimal cashbackNum;

    /**
     * 返佣时间
     */
    private Date cashbackTime;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 入库时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 返佣账户类型: 1 支付宝 2 微信
     */
    private Integer accountType;

    /**
     * 
     */
    private Long id;

    /**
     * 
     */
    private String accountName;

    /**
     * 
     */
    private Long rebateMoney;

    /**
     * 
     */
    private BigDecimal fillCardFee;

    /**
     * 学校补卡老师姓名
     */
    private String fillTeacherName;

    /**
     * 学校补卡老师手机号
     */
    private String fillTeacherTel;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    /**
     * 
     */
    public Date getLogdate() {
        return logdate;
    }

    /**
     * 
     */
    public void setLogdate(Date logdate) {
        this.logdate = logdate;
    }

    /**
     * 学校ID
     */
    public Long getSchoolId() {
        return schoolId;
    }

    /**
     * 学校ID
     */
    public void setSchoolId(Long schoolId) {
        this.schoolId = schoolId;
    }

    /**
     * 月补卡数量
     */
    public Long getReplenishmentNum() {
        return replenishmentNum;
    }

    /**
     * 月补卡数量
     */
    public void setReplenishmentNum(Long replenishmentNum) {
        this.replenishmentNum = replenishmentNum;
    }

    /**
     * 支付金额
     */
    public BigDecimal getPayNum() {
        return payNum;
    }

    /**
     * 支付金额
     */
    public void setPayNum(BigDecimal payNum) {
        this.payNum = payNum;
    }

    /**
     * 返佣金额
     */
    public BigDecimal getCashbackNum() {
        return cashbackNum;
    }

    /**
     * 返佣金额
     */
    public void setCashbackNum(BigDecimal cashbackNum) {
        this.cashbackNum = cashbackNum;
    }

    /**
     * 返佣时间
     */
    public Date getCashbackTime() {
        return cashbackTime;
    }

    /**
     * 返佣时间
     */
    public void setCashbackTime(Date cashbackTime) {
        this.cashbackTime = cashbackTime;
    }

    /**
     * 状态
     */
    public Integer getStatus() {
        return status;
    }

    /**
     * 状态
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * 入库时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 入库时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 返佣账户类型: 1 支付宝 2 微信
     */
    public Integer getAccountType() {
        return accountType;
    }

    /**
     * 返佣账户类型: 1 支付宝 2 微信
     */
    public void setAccountType(Integer accountType) {
        this.accountType = accountType;
    }

    /**
     * 
     */
    public Long getId() {
        return id;
    }

    /**
     * 
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 
     */
    public String getAccountName() {
        return accountName;
    }

    /**
     * 
     */
    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    /**
     * 
     */
    public Long getRebateMoney() {
        return rebateMoney;
    }

    /**
     * 
     */
    public void setRebateMoney(Long rebateMoney) {
        this.rebateMoney = rebateMoney;
    }

    /**
     * 
     */
    public BigDecimal getFillCardFee() {
        return fillCardFee;
    }

    /**
     * 
     */
    public void setFillCardFee(BigDecimal fillCardFee) {
        this.fillCardFee = fillCardFee;
    }

    /**
     * 学校补卡老师姓名
     */
    public String getFillTeacherName() {
        return fillTeacherName;
    }

    /**
     * 学校补卡老师姓名
     */
    public void setFillTeacherName(String fillTeacherName) {
        this.fillTeacherName = fillTeacherName;
    }

    /**
     * 学校补卡老师手机号
     */
    public String getFillTeacherTel() {
        return fillTeacherTel;
    }

    /**
     * 学校补卡老师手机号
     */
    public void setFillTeacherTel(String fillTeacherTel) {
        this.fillTeacherTel = fillTeacherTel;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        CardCashback other = (CardCashback) that;
        return (this.getLogdate() == null ? other.getLogdate() == null : this.getLogdate().equals(other.getLogdate()))
            && (this.getSchoolId() == null ? other.getSchoolId() == null : this.getSchoolId().equals(other.getSchoolId()))
            && (this.getReplenishmentNum() == null ? other.getReplenishmentNum() == null : this.getReplenishmentNum().equals(other.getReplenishmentNum()))
            && (this.getPayNum() == null ? other.getPayNum() == null : this.getPayNum().equals(other.getPayNum()))
            && (this.getCashbackNum() == null ? other.getCashbackNum() == null : this.getCashbackNum().equals(other.getCashbackNum()))
            && (this.getCashbackTime() == null ? other.getCashbackTime() == null : this.getCashbackTime().equals(other.getCashbackTime()))
            && (this.getStatus() == null ? other.getStatus() == null : this.getStatus().equals(other.getStatus()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()))
            && (this.getAccountType() == null ? other.getAccountType() == null : this.getAccountType().equals(other.getAccountType()))
            && (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getAccountName() == null ? other.getAccountName() == null : this.getAccountName().equals(other.getAccountName()))
            && (this.getRebateMoney() == null ? other.getRebateMoney() == null : this.getRebateMoney().equals(other.getRebateMoney()))
            && (this.getFillCardFee() == null ? other.getFillCardFee() == null : this.getFillCardFee().equals(other.getFillCardFee()))
            && (this.getFillTeacherName() == null ? other.getFillTeacherName() == null : this.getFillTeacherName().equals(other.getFillTeacherName()))
            && (this.getFillTeacherTel() == null ? other.getFillTeacherTel() == null : this.getFillTeacherTel().equals(other.getFillTeacherTel()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getLogdate() == null) ? 0 : getLogdate().hashCode());
        result = prime * result + ((getSchoolId() == null) ? 0 : getSchoolId().hashCode());
        result = prime * result + ((getReplenishmentNum() == null) ? 0 : getReplenishmentNum().hashCode());
        result = prime * result + ((getPayNum() == null) ? 0 : getPayNum().hashCode());
        result = prime * result + ((getCashbackNum() == null) ? 0 : getCashbackNum().hashCode());
        result = prime * result + ((getCashbackTime() == null) ? 0 : getCashbackTime().hashCode());
        result = prime * result + ((getStatus() == null) ? 0 : getStatus().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getAccountType() == null) ? 0 : getAccountType().hashCode());
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getAccountName() == null) ? 0 : getAccountName().hashCode());
        result = prime * result + ((getRebateMoney() == null) ? 0 : getRebateMoney().hashCode());
        result = prime * result + ((getFillCardFee() == null) ? 0 : getFillCardFee().hashCode());
        result = prime * result + ((getFillTeacherName() == null) ? 0 : getFillTeacherName().hashCode());
        result = prime * result + ((getFillTeacherTel() == null) ? 0 : getFillTeacherTel().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", logdate=").append(logdate);
        sb.append(", schoolId=").append(schoolId);
        sb.append(", replenishmentNum=").append(replenishmentNum);
        sb.append(", payNum=").append(payNum);
        sb.append(", cashbackNum=").append(cashbackNum);
        sb.append(", cashbackTime=").append(cashbackTime);
        sb.append(", status=").append(status);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", accountType=").append(accountType);
        sb.append(", id=").append(id);
        sb.append(", accountName=").append(accountName);
        sb.append(", rebateMoney=").append(rebateMoney);
        sb.append(", fillCardFee=").append(fillCardFee);
        sb.append(", fillTeacherName=").append(fillTeacherName);
        sb.append(", fillTeacherTel=").append(fillTeacherTel);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}