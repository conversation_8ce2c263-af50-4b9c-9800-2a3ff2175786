package com.joinus.dao.entity;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("t_permission")
@KeySequence("seq_t_permission")
public class BasicPermission implements Serializable {

    private long id;
    private Long parentId;
    private String name;
    private Integer platform;
    private Double sort;
    private Integer hideInMenu;
    private Integer isactive;
    private Long resourceId;
    private String remark;
    private Date createdAt;
    private Date updatedAt;
    private String parentFullPath;

}
