package com.joinus.dao.entity;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 结算审核表
 * @TableName T_CARD_CLEARING_AUDIT
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName(value ="T_CARD_CLEARING_AUDIT")
@KeySequence("SEQ_T_CARD_CLEARING_AUDIT")
public class CardClearingAudit implements Serializable {
    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 入库数量
     */
    private Integer lnventoryQuantity;

    /**
     * 入库人id
     */
    private Long arehouseHolderId;

    /**
     * 入库人
     */
    private String arehouseHolder;

    /**
     * 入库时间
     */
    private Date applyDate;

    /**
     * 学校id
     */
    private Integer schoolId;

    /**
     * 售价
     */
    private BigDecimal price;

    /**
     * 未出库数量
     */
    private Integer notOutbound;

    /**
     * 已出库数量
     */
    private Integer deliveryNumber;

    /**
     * 出库人id
     */
    private Long deliveryId;

    /**
     * 出库人
     */
    private String delivery;

    /**
     * 出库时间
     */
    private Date outboundDate;

    /**
     * 卡品类型(1 首次制卡 2 二次制卡 3 周转卡 4卡贴)
     */
    private Integer cardType;

    /**
     * 领取人id
     */
    private Long getPersonId;

    /**
     * 进价
     */
    private BigDecimal puchasePice;

    /**
     * 领取人
     */
    private String getPerson;

    /**
     * 申请结算时间
     */
    private Date settlementDate;

    /**
     * 已结算数量
     */
    private Integer settlementNumber;

    /**
     * 未结算数量
     */
    private Integer notSettlement;

    /**
     * 当前审核状态(0,审核中  1审核成功 2 审核失败,3出库审核成功 )
     */
    private Integer state;

    /**
     * 备注
     */
    private String remark;

    /**
     * 流水号
     */
    private Integer storageId;

    /**
     * 结算形式(1 现金 2 积分扣减 3 对公转账 4银行转账 5退卡)
     */
    private Integer settlementMethod;

    /**
     * 结算审批人
     */
    private String clearingApprover;

    /**
     * 结算审批人_id
     */
    private Long clearingApproverId;

    /**
     * 结算申请人
     */
    private String settlementClearing;

    /**
     * 结算申请人的id
     */
    private Long settlementClearingId;

    /**
     * 结算金额
     */
    private BigDecimal settlementAmount;

    /**
     * 审批意见
     */
    private String reviewmsg;

    /**
     * 审批结算时间
     */
    private Date endDate;

    /**
     * 结算表id
     */
    private Long clearingId;

    /**
     * 
     */
    private String schoolName;

    /**
     * 学校区域id
     */
    private String regionId;

    /**
     * 审批开始时间
     */
    private Date startedTime;

    /**
     * 本次结算数量
     */
    private Integer clearingNum;

    /**
     * 硬件类型
     */
    private String cardHardtype;

    /**
     * 是否已经打印 0:未打印 1:已打印
     */
    private Integer isPrint;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}