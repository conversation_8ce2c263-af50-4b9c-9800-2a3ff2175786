package com.joinus.dao.entity;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 审批历史表
 * @TableName T_CARD_HISTORY
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName(value ="T_CARD_HISTORY")
@KeySequence("SEQ_T_CARD_HISTORY")
public class CardHistory implements Serializable {
    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 学校id
     */
    private Long schoolId;

    /**
     * 卡品类型1 首次制卡 2 二次制卡 3 周转卡 4卡贴
     */
    private Integer cardType;

    /**
     * 进价
     */
    private BigDecimal purchasePrice;

    /**
     * 入库数量
     */
    private Integer inventory;

    /**
     * 操作类型(1 入库操作  2出库操作 3结算操作
     */
    private Integer operation;

    /**
     * 入库时间
     */
    private Date applyDate;

    /**
     * 入库人
     */
    private String arehouseHolder;

    /**
     * 出库人
     */
    private String delivery;

    /**
     * 出库审批人
     */
    private String outboundApprover;

    /**
     * 入库审批人
     */
    private String approver;

    /**
     * 出库时间
     */
    private Date outboundDate;

    /**
     * 出库数量
     */
    private Integer deliveryNumber;

    /**
     * 未出库数量
     */
    private Integer notOutbound;

    /**
     * 售价
     */
    private BigDecimal price;

    /**
     * 结算时间
     */
    private Date settlementDate;

    /**
     * 领取人
     */
    private String getPerson;

    /**
     * 结算申请人
     */
    private String settlementClearing;

    /**
     * 结算金额
     */
    private BigDecimal settlementAmount;

    /**
     * 已结算数量
     */
    private Integer settlementNumber;

    /**
     * 未结算数量
     */
    private Integer notSettlement;

    /**
     * 结算审批人
     */
    private String clearingApprover;

    /**
     * 当前审核状态(0,入库审核中  1入库审核成功,3出库审核中,4出库审核成功,5结算审核中,6结算审核成功,7入库审核失败,8出库审核失败,9结算审核失败 )
     */
    private Integer state;

    /**
     * 备注
     */
    private String remark;

    /**
     * 流水号
     */
    private Integer storageId;

    /**
     * 审核表的id  (通过这个键和state来确定一条数据 )
     */
    private Long only;

    /**
     * 当前学校的区域id
     */
    private String regionId;

    /**
     * 学校名字
     */
    private String schoolName;

    /**
     * 本次结算数量
     */
    private Long clearingNum;

    /**
     * 制卡商
     */
    private String modecompany;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    public Long getId() {
        return id;
    }

    /**
     * 主键
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 学校id
     */
    public Long getSchoolId() {
        return schoolId;
    }

    /**
     * 学校id
     */
    public void setSchoolId(Long schoolId) {
        this.schoolId = schoolId;
    }

    /**
     * 卡品类型1 首次制卡 2 二次制卡 3 周转卡 4卡贴
     */
    public Integer getCardType() {
        return cardType;
    }

    /**
     * 卡品类型1 首次制卡 2 二次制卡 3 周转卡 4卡贴
     */
    public void setCardType(Integer cardType) {
        this.cardType = cardType;
    }

    /**
     * 进价
     */
    public BigDecimal getPurchasePrice() {
        return purchasePrice;
    }

    /**
     * 进价
     */
    public void setPurchasePrice(BigDecimal purchasePrice) {
        this.purchasePrice = purchasePrice;
    }

    /**
     * 入库数量
     */
    public Integer getInventory() {
        return inventory;
    }

    /**
     * 入库数量
     */
    public void setInventory(Integer inventory) {
        this.inventory = inventory;
    }

    /**
     * 操作类型(1 入库操作  2出库操作 3结算操作
     */
    public Integer getOperation() {
        return operation;
    }

    /**
     * 操作类型(1 入库操作  2出库操作 3结算操作
     */
    public void setOperation(Integer operation) {
        this.operation = operation;
    }

    /**
     * 入库时间
     */
    public Date getApplyDate() {
        return applyDate;
    }

    /**
     * 入库时间
     */
    public void setApplyDate(Date applyDate) {
        this.applyDate = applyDate;
    }

    /**
     * 入库人
     */
    public String getArehouseHolder() {
        return arehouseHolder;
    }

    /**
     * 入库人
     */
    public void setArehouseHolder(String arehouseHolder) {
        this.arehouseHolder = arehouseHolder;
    }

    /**
     * 出库人
     */
    public String getDelivery() {
        return delivery;
    }

    /**
     * 出库人
     */
    public void setDelivery(String delivery) {
        this.delivery = delivery;
    }

    /**
     * 出库审批人
     */
    public String getOutboundApprover() {
        return outboundApprover;
    }

    /**
     * 出库审批人
     */
    public void setOutboundApprover(String outboundApprover) {
        this.outboundApprover = outboundApprover;
    }

    /**
     * 入库审批人
     */
    public String getApprover() {
        return approver;
    }

    /**
     * 入库审批人
     */
    public void setApprover(String approver) {
        this.approver = approver;
    }

    /**
     * 出库时间
     */
    public Date getOutboundDate() {
        return outboundDate;
    }

    /**
     * 出库时间
     */
    public void setOutboundDate(Date outboundDate) {
        this.outboundDate = outboundDate;
    }

    /**
     * 出库数量
     */
    public Integer getDeliveryNumber() {
        return deliveryNumber;
    }

    /**
     * 出库数量
     */
    public void setDeliveryNumber(Integer deliveryNumber) {
        this.deliveryNumber = deliveryNumber;
    }

    /**
     * 未出库数量
     */
    public Integer getNotOutbound() {
        return notOutbound;
    }

    /**
     * 未出库数量
     */
    public void setNotOutbound(Integer notOutbound) {
        this.notOutbound = notOutbound;
    }

    /**
     * 售价
     */
    public BigDecimal getPrice() {
        return price;
    }

    /**
     * 售价
     */
    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    /**
     * 结算时间
     */
    public Date getSettlementDate() {
        return settlementDate;
    }

    /**
     * 结算时间
     */
    public void setSettlementDate(Date settlementDate) {
        this.settlementDate = settlementDate;
    }

    /**
     * 领取人
     */
    public String getGetPerson() {
        return getPerson;
    }

    /**
     * 领取人
     */
    public void setGetPerson(String getPerson) {
        this.getPerson = getPerson;
    }

    /**
     * 结算申请人
     */
    public String getSettlementClearing() {
        return settlementClearing;
    }

    /**
     * 结算申请人
     */
    public void setSettlementClearing(String settlementClearing) {
        this.settlementClearing = settlementClearing;
    }

    /**
     * 结算金额
     */
    public BigDecimal getSettlementAmount() {
        return settlementAmount;
    }

    /**
     * 结算金额
     */
    public void setSettlementAmount(BigDecimal settlementAmount) {
        this.settlementAmount = settlementAmount;
    }

    /**
     * 已结算数量
     */
    public Integer getSettlementNumber() {
        return settlementNumber;
    }

    /**
     * 已结算数量
     */
    public void setSettlementNumber(Integer settlementNumber) {
        this.settlementNumber = settlementNumber;
    }

    /**
     * 未结算数量
     */
    public Integer getNotSettlement() {
        return notSettlement;
    }

    /**
     * 未结算数量
     */
    public void setNotSettlement(Integer notSettlement) {
        this.notSettlement = notSettlement;
    }

    /**
     * 结算审批人
     */
    public String getClearingApprover() {
        return clearingApprover;
    }

    /**
     * 结算审批人
     */
    public void setClearingApprover(String clearingApprover) {
        this.clearingApprover = clearingApprover;
    }

    /**
     * 当前审核状态(0,入库审核中  1入库审核成功,3出库审核中,4出库审核成功,5结算审核中,6结算审核成功,7入库审核失败,8出库审核失败,9结算审核失败 )
     */
    public Integer getState() {
        return state;
    }

    /**
     * 当前审核状态(0,入库审核中  1入库审核成功,3出库审核中,4出库审核成功,5结算审核中,6结算审核成功,7入库审核失败,8出库审核失败,9结算审核失败 )
     */
    public void setState(Integer state) {
        this.state = state;
    }

    /**
     * 备注
     */
    public String getRemark() {
        return remark;
    }

    /**
     * 备注
     */
    public void setRemark(String remark) {
        this.remark = remark;
    }

    /**
     * 流水号
     */
    public Integer getStorageId() {
        return storageId;
    }

    /**
     * 流水号
     */
    public void setStorageId(Integer storageId) {
        this.storageId = storageId;
    }

    /**
     * 审核表的id  (通过这个键和state来确定一条数据 )
     */
    public Long getOnly() {
        return only;
    }

    /**
     * 审核表的id  (通过这个键和state来确定一条数据 )
     */
    public void setOnly(Long only) {
        this.only = only;
    }

    /**
     * 当前学校的区域id
     */
    public String getRegionId() {
        return regionId;
    }

    /**
     * 当前学校的区域id
     */
    public void setRegionId(String regionId) {
        this.regionId = regionId;
    }

    /**
     * 学校名字
     */
    public String getSchoolName() {
        return schoolName;
    }

    /**
     * 学校名字
     */
    public void setSchoolName(String schoolName) {
        this.schoolName = schoolName;
    }

    /**
     * 本次结算数量
     */
    public Long getClearingNum() {
        return clearingNum;
    }

    /**
     * 本次结算数量
     */
    public void setClearingNum(Long clearingNum) {
        this.clearingNum = clearingNum;
    }

    /**
     * 制卡商
     */
    public String getModecompany() {
        return modecompany;
    }

    /**
     * 制卡商
     */
    public void setModecompany(String modecompany) {
        this.modecompany = modecompany;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        CardHistory other = (CardHistory) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getSchoolId() == null ? other.getSchoolId() == null : this.getSchoolId().equals(other.getSchoolId()))
            && (this.getCardType() == null ? other.getCardType() == null : this.getCardType().equals(other.getCardType()))
            && (this.getPurchasePrice() == null ? other.getPurchasePrice() == null : this.getPurchasePrice().equals(other.getPurchasePrice()))
            && (this.getInventory() == null ? other.getInventory() == null : this.getInventory().equals(other.getInventory()))
            && (this.getOperation() == null ? other.getOperation() == null : this.getOperation().equals(other.getOperation()))
            && (this.getApplyDate() == null ? other.getApplyDate() == null : this.getApplyDate().equals(other.getApplyDate()))
            && (this.getArehouseHolder() == null ? other.getArehouseHolder() == null : this.getArehouseHolder().equals(other.getArehouseHolder()))
            && (this.getDelivery() == null ? other.getDelivery() == null : this.getDelivery().equals(other.getDelivery()))
            && (this.getOutboundApprover() == null ? other.getOutboundApprover() == null : this.getOutboundApprover().equals(other.getOutboundApprover()))
            && (this.getApprover() == null ? other.getApprover() == null : this.getApprover().equals(other.getApprover()))
            && (this.getOutboundDate() == null ? other.getOutboundDate() == null : this.getOutboundDate().equals(other.getOutboundDate()))
            && (this.getDeliveryNumber() == null ? other.getDeliveryNumber() == null : this.getDeliveryNumber().equals(other.getDeliveryNumber()))
            && (this.getNotOutbound() == null ? other.getNotOutbound() == null : this.getNotOutbound().equals(other.getNotOutbound()))
            && (this.getPrice() == null ? other.getPrice() == null : this.getPrice().equals(other.getPrice()))
            && (this.getSettlementDate() == null ? other.getSettlementDate() == null : this.getSettlementDate().equals(other.getSettlementDate()))
            && (this.getGetPerson() == null ? other.getGetPerson() == null : this.getGetPerson().equals(other.getGetPerson()))
            && (this.getSettlementClearing() == null ? other.getSettlementClearing() == null : this.getSettlementClearing().equals(other.getSettlementClearing()))
            && (this.getSettlementAmount() == null ? other.getSettlementAmount() == null : this.getSettlementAmount().equals(other.getSettlementAmount()))
            && (this.getSettlementNumber() == null ? other.getSettlementNumber() == null : this.getSettlementNumber().equals(other.getSettlementNumber()))
            && (this.getNotSettlement() == null ? other.getNotSettlement() == null : this.getNotSettlement().equals(other.getNotSettlement()))
            && (this.getClearingApprover() == null ? other.getClearingApprover() == null : this.getClearingApprover().equals(other.getClearingApprover()))
            && (this.getState() == null ? other.getState() == null : this.getState().equals(other.getState()))
            && (this.getRemark() == null ? other.getRemark() == null : this.getRemark().equals(other.getRemark()))
            && (this.getStorageId() == null ? other.getStorageId() == null : this.getStorageId().equals(other.getStorageId()))
            && (this.getOnly() == null ? other.getOnly() == null : this.getOnly().equals(other.getOnly()))
            && (this.getRegionId() == null ? other.getRegionId() == null : this.getRegionId().equals(other.getRegionId()))
            && (this.getSchoolName() == null ? other.getSchoolName() == null : this.getSchoolName().equals(other.getSchoolName()))
            && (this.getClearingNum() == null ? other.getClearingNum() == null : this.getClearingNum().equals(other.getClearingNum()))
            && (this.getModecompany() == null ? other.getModecompany() == null : this.getModecompany().equals(other.getModecompany()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getSchoolId() == null) ? 0 : getSchoolId().hashCode());
        result = prime * result + ((getCardType() == null) ? 0 : getCardType().hashCode());
        result = prime * result + ((getPurchasePrice() == null) ? 0 : getPurchasePrice().hashCode());
        result = prime * result + ((getInventory() == null) ? 0 : getInventory().hashCode());
        result = prime * result + ((getOperation() == null) ? 0 : getOperation().hashCode());
        result = prime * result + ((getApplyDate() == null) ? 0 : getApplyDate().hashCode());
        result = prime * result + ((getArehouseHolder() == null) ? 0 : getArehouseHolder().hashCode());
        result = prime * result + ((getDelivery() == null) ? 0 : getDelivery().hashCode());
        result = prime * result + ((getOutboundApprover() == null) ? 0 : getOutboundApprover().hashCode());
        result = prime * result + ((getApprover() == null) ? 0 : getApprover().hashCode());
        result = prime * result + ((getOutboundDate() == null) ? 0 : getOutboundDate().hashCode());
        result = prime * result + ((getDeliveryNumber() == null) ? 0 : getDeliveryNumber().hashCode());
        result = prime * result + ((getNotOutbound() == null) ? 0 : getNotOutbound().hashCode());
        result = prime * result + ((getPrice() == null) ? 0 : getPrice().hashCode());
        result = prime * result + ((getSettlementDate() == null) ? 0 : getSettlementDate().hashCode());
        result = prime * result + ((getGetPerson() == null) ? 0 : getGetPerson().hashCode());
        result = prime * result + ((getSettlementClearing() == null) ? 0 : getSettlementClearing().hashCode());
        result = prime * result + ((getSettlementAmount() == null) ? 0 : getSettlementAmount().hashCode());
        result = prime * result + ((getSettlementNumber() == null) ? 0 : getSettlementNumber().hashCode());
        result = prime * result + ((getNotSettlement() == null) ? 0 : getNotSettlement().hashCode());
        result = prime * result + ((getClearingApprover() == null) ? 0 : getClearingApprover().hashCode());
        result = prime * result + ((getState() == null) ? 0 : getState().hashCode());
        result = prime * result + ((getRemark() == null) ? 0 : getRemark().hashCode());
        result = prime * result + ((getStorageId() == null) ? 0 : getStorageId().hashCode());
        result = prime * result + ((getOnly() == null) ? 0 : getOnly().hashCode());
        result = prime * result + ((getRegionId() == null) ? 0 : getRegionId().hashCode());
        result = prime * result + ((getSchoolName() == null) ? 0 : getSchoolName().hashCode());
        result = prime * result + ((getClearingNum() == null) ? 0 : getClearingNum().hashCode());
        result = prime * result + ((getModecompany() == null) ? 0 : getModecompany().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", schoolId=").append(schoolId);
        sb.append(", cardType=").append(cardType);
        sb.append(", purchasePrice=").append(purchasePrice);
        sb.append(", inventory=").append(inventory);
        sb.append(", operation=").append(operation);
        sb.append(", applyDate=").append(applyDate);
        sb.append(", arehouseHolder=").append(arehouseHolder);
        sb.append(", delivery=").append(delivery);
        sb.append(", outboundApprover=").append(outboundApprover);
        sb.append(", approver=").append(approver);
        sb.append(", outboundDate=").append(outboundDate);
        sb.append(", deliveryNumber=").append(deliveryNumber);
        sb.append(", notOutbound=").append(notOutbound);
        sb.append(", price=").append(price);
        sb.append(", settlementDate=").append(settlementDate);
        sb.append(", getPerson=").append(getPerson);
        sb.append(", settlementClearing=").append(settlementClearing);
        sb.append(", settlementAmount=").append(settlementAmount);
        sb.append(", settlementNumber=").append(settlementNumber);
        sb.append(", notSettlement=").append(notSettlement);
        sb.append(", clearingApprover=").append(clearingApprover);
        sb.append(", state=").append(state);
        sb.append(", remark=").append(remark);
        sb.append(", storageId=").append(storageId);
        sb.append(", only=").append(only);
        sb.append(", regionId=").append(regionId);
        sb.append(", schoolName=").append(schoolName);
        sb.append(", clearingNum=").append(clearingNum);
        sb.append(", modecompany=").append(modecompany);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}