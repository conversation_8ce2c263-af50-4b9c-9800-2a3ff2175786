package com.joinus.dao.entity;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName(value ="T_CARD_CLEARING")
@KeySequence("SEQ_T_CARD_CLEARING")
public class CardClearing implements Serializable {
    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 入库数量
     */
    private Long lnventoryQuantity;

    /**
     * 入库人id
     */
    private Long arehouseHolderId;

    /**
     * 入库人
     */
    private String arehouseHolder;

    /**
     * 入库时间
     */
    private Date applyDate;

    /**
     * 学校id
     */
    private Integer schoolId;

    /**
     * 售价
     */
    private BigDecimal price;

    /**
     * 未出库数量
     */
    private Integer notOutbound;

    /**
     * 已出库数量
     */
    private Integer deliveryNumber;

    /**
     * 出库人id
     */
    private Long deliveryId;

    /**
     * 出库人
     */
    private String delivery;

    /**
     * 出库时间
     */
    private Date outboundDate;

    /**
     * 卡品类型(0 首次制卡 1 二次制卡 2周转卡 3卡贴)
     */
    private Integer cardType;

    /**
     * 领取人id
     */
    private Long getPersonId;

    /**
     * 进价
     */
    private BigDecimal puchasePice;

    /**
     * 领取人
     */
    private String getPerson;

    /**
     * 结算时间
     */
    private Date settlementDate;

    /**
     * 已结算数量
     */
    private Integer settlementNumber;

    /**
     * 未结算数量
     */
    private Integer notSettlement;

    /**
     * 当前审核状态(0,审核中  1审核成功 2 审核失败,3出库审核成功 )
     */
    private Integer state;

    /**
     * 备注
     */
    private String remark;

    /**
     * 流水号
     */
    private Integer storageId;

    /**
     * 结算形式(1 现金 2 积分扣减 3 对公转账 4银行转账 5退卡)
     */
    private Integer settlementMethod;

    /**
     * 结算审批人
     */
    private String clearingApprover;

    /**
     * 结算审批人_id
     */
    private Long clearingApproverId;

    /**
     * 结算申请人
     */
    private String settlementClearing;

    /**
     * 结算申请人的id
     */
    private Long settlementClearingId;

    /**
     * 结算金额
     */
    private BigDecimal settlementAmount;

    /**
     * 审批意见
     */
    private String reviewmsg;

    /**
     * 审核表的id
     */
    private Long auditId;

    /**
     * 
     */
    private String schoolName;

    /**
     * 学校区域id
     */
    private String regionId;

    /**
     * 出库审批人
     */
    private String outboundApprover;

    /**
     * 硬件类型
     */
    private String cardHardtype;

    /**
     * 审批中数量
     */
    private Integer approvalNumber;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @TableField(exist = false)
    private Integer tempSettlementNumber ;
}