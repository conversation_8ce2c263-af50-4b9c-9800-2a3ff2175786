package com.joinus.dao.entity;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@TableName("t_resource")
@KeySequence("seq_t_resource")
public class BasicResource implements Serializable {

    @TableId
    private long id;
    @TableField("type")
    private Integer type;
    private String name;
    private Integer platform;
    private String urlPath;
    private String urlPathRegex;
    private String method;
    private String icon;
    private String remark;
    private Integer isactive;
    private Date createdAt;
    private Date updatedAt;

}
