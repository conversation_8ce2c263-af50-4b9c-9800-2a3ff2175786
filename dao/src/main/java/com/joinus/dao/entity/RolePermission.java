package com.joinus.dao.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("T_ROLE_PERMISSION")
public class RolePermission implements Serializable {

    private Long roleId;
    private Long permissionId;
    private Integer platform;
    private Date createdAt;
    private Date updatedAt;

}
