package com.joinus.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 话单记录
 * <AUTHOR>
 *
 */

@TableName("T_QIN_PHONE_DETAIL")
@KeySequence("SEQ_T_QIN_PHONE_DETAIL")
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class CallRecord implements Serializable {
	@TableId(value = "id", type = IdType.INPUT)
	private Long id;

	private Long schoolId;
	private Long studentId;

	private String cardcode;// 卡号（学生代号）
	private String phone;//	被叫号码
	private int talkLength;// 呼叫时长
	private Date startTime;// 发起时间

	private String terminalNum;

	private Integer answerState = 0;
	private Integer chargeType = 4;
	private Integer callType = 1;
	private Integer phoneType = 0;

	private Date saveTime;

}
