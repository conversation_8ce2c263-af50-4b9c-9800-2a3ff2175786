package com.joinus.dao;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

/**
 * <AUTHOR> maweihua
 * @create 2024-10-24 16:39:23
 */
@TableName("T_FLOOR")
@KeySequence("SEQ_T_FLOOR")
@Data
public class FloorEntity {
    @TableId(value = "ID",type = IdType.INPUT)
    private Long id;
    @TableField("floor_name")
    private String floorName;
    @TableField("ISACTIVE")
    private Integer isActive;
    @TableField("MANAGER_ID")
    private Integer managerId;
    @TableField("school_id")
    private Long schoolId;

}
