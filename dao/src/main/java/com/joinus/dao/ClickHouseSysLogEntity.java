package com.joinus.dao;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@Data
@EqualsAndHashCode(callSuper=false)
@TableName("SYS_LOG")
@KeySequence("SEQ_SYS_LOG")
public class ClickHouseSysLogEntity {
    @TableId(value = "ID",type = IdType.INPUT)
    private Long id;/**/
    @TableField("CONTENT")
    private String content;/**/
    @TableField("LOG_DESC")
    private String logDesc;/**/
    @TableField("ISACTIVE")
    private Integer isActive;/**/
    @TableField("OPERATOR")
    private String operator;/**/
    @TableField("LOG_TIME")
    private Date logTime;/**/
    @TableField("LOG_TYPE")
    private Integer logType;/* 操作类型:1:增加，2：修改，3.删除，4.登陆，5.登出 */
    @TableField("SCHOOL_NAME")
    private String schoolName;/* 所属学校名称 */
    @TableField("SCHOOL_ID")
    private Long schoolId;/* 所属学校id */
    @TableField("IP_ADDRESS")
    private String ipAddress;/* ip地址 */
    @TableField("OPERATOR_ID")
    private Long operatorId;/* 操作者id */
    @TableField("OPERATOR_TYPE")
    private String operatorType;/* 操作者类型：1:管理员; 2:教师; 3:学生; …… */
    @TableField("USER_ID")
    private Integer userId;/* 用户ID */
    @TableField("GROUP_NAME")
    private String groupName;/* 用户组名 */
    @TableField("REGION_NAME")
    private String regionName;/* 区域名 */

}
