package com.joinus.dao;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Builder;
import lombok.Data;

import java.sql.Timestamp;
import java.util.Date;

/**
 * <AUTHOR> anpengyu
 * 配餐学生端消费记录
 * @create 2024/10/15 10:21
 */
@Data
@Builder
@TableName("ijx.T_MEAL_CARD_RECORD")
@KeySequence("SEQ_T_MEAL_CARD_RECORD")
public class MealCardRecordEntity {

    @TableId(value = "ID",type = IdType.INPUT)
    private Long id;
    @TableField("record_id")
    private Long recordId;
    @TableField("card_date")
    private Date cardDate;
    @TableField("card_time")
    private Date cardTime;
    @TableField("school_id")
    private Long schoolId;
    @TableField("student_id")
    private Long studentId;
    @TableField("school_config_time_period")
    private String schoolConfigTimePeriod;
    @TableField("created_at")
    private Date createdAt;
}
