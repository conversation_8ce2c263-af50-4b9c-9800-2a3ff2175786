package com.joinus.dao;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.joinus.dao.enums.IjxRoleEnum;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.util.Objects;

@Data
@ToString
@Builder
@TableName("t_role")
@KeySequence("SEQ_T_ROLE")
public class RoleEntity {
    @TableId("id")
    private Long id;

    @TableField("role_name")
    private String roleName;

    @TableField("role_type")
    private IjxRoleEnum roleType;

    @TableField("school_id")
    private Long schoolId;

    @TableField("role_desc")
    private String roleDesc;

    /**
     * 是否是管理员，包含主管理员和普通管理员
     */
    public boolean isAdministrator() {
        return Objects.equals(this.roleType, IjxRoleEnum.ADMINISTRATOR) || Objects.equals(this.roleType, IjxRoleEnum.COMMON_ADMIN);
    }

    /**
     * 是否是宿管
     */
    public boolean isFloorManager() {
        return Objects.equals(this.roleType, IjxRoleEnum.FLOOR_MANAGER);
    }
}
