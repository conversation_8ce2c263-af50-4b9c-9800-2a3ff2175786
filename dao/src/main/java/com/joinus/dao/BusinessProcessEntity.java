package com.joinus.dao;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <AUTHOR> maweihua
 * @create 2025-1-23 11:47:24
 */
@Data
@EqualsAndHashCode
@Accessors(chain = true)
@TableName("t_business_process")
@KeySequence("SEQ_BUSINESS_PROCESS")
public class BusinessProcessEntity {
    @TableId("ID")
    private Long id;

    @TableField("telnum")
    private String telNum;

    @TableField("FLAG")
    private Integer flag;

    @TableField("STUDENT_ID")
    private Long studentId;

    @TableField("CLASS_ID")
    private Long classId;

    @TableField("SCHOOL_ID")
    private Long schoolId;


    @TableField("PRODUCT_ID")
    private Long productId;

    @TableField("PRODUCT_CODE")
    private String productCode;


    @TableField("OPEN_TIME")
    private Date openTime;


    @TableField("OPEN_TIME")
    private Date closeTime;


    @TableField("GROUPID")
    private String groupId;

    @TableField("PARENT_ID")
    private Long parentId;


    @TableField("REGION")
    private String region;


    @TableField("INSERT_TIME")
    private Date insertTime;

    @TableField("UPDATE_TIME")
    private Date updateTime;

    @TableField("BUS_BEGINTIME")
    private Date busBeginTime;

    @TableField("BUS_ENDTIME")
    private Date busEndTime;


}
