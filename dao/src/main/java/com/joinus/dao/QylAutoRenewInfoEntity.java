package com.joinus.dao;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <p>
 * 自推广业务开通表
 * </p>
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode
@Accessors(chain = true)
@TableName("t_qyl_auto_renew_info")
public class QylAutoRenewInfoEntity {

    private static final long serialVersionUID = 1L;

    @TableId("ID")
    private Long id;

    /** 签约平台类型 1:拉卡拉,2:一卡通(易科士),3:钱包 */
    @TableField("SIGN_PLATFORM_TYPE")
    private Integer signPlatformType;

    @TableField("STUDENT_ID")
    private Long studentId;

    @TableField("parent_id")
    private Long parentId;

    @TableField("sign_state")
    private Long signState;

}