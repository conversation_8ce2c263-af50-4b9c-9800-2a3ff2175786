package com.joinus.dao;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 学生在校状态
 *
 * <AUTHOR> anpy
 * @create 2024/4/9 10:37
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("T_STUDENT_IN_SCHOOL_STATUS")
public class StudentInSchoolStatusEntity {
    @TableField("record_id")
    private long recordId;
    @TableField("STUDENT_ID")
    private Long studentId;
    @TableField("school_id")
    private long schoolId;
    @TableField("status")
    private int status;
    @TableField(exist = false)
    private String terminalType;

//    @DateTimeFormat
//    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @TableField("created_at")
    private Date createTime;
//    @DateTimeFormat
//    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @TableField("updated_at")
    private Date updateTime;
}
