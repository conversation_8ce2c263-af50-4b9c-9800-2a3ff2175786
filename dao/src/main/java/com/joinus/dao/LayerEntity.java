package com.joinus.dao;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

/**
 * <AUTHOR> maweihua
 * @create 2024-10-24 16:39:20
 */
@TableName("t_layer")
@KeySequence("SEQ_T_LAYER")
@Data
public class LayerEntity {
    @TableId(value = "ID",type = IdType.INPUT)
    private Long id;
    @TableField("LAYER_NAME")
    private String layerName;
    @TableField("ISACTIVE")
    private Integer isActive;
    @TableField("DANYUAN_ID")
    private Long danYuanId;
    @TableField("MANAGER_ID")
    private Long managerId;
    @TableField("FLOOR_ID")
    private Long floorId;

}
