package com.joinus.dao;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.apache.ibatis.type.JdbcType;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.Calendar;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.util.Date;


@Data
@TableName("ijx.t_terminal")
public class TerminalEntity {

    @TableId(value = "ID")
    private Long id;

    @TableField("type_name")
    private String typeName;
    @TableField("terminal_num")
    private String terminalNum;
    @TableField("school_id")
    private Long schoolId;
    @TableField("CLASS_ID")
    private Long classId;
    @TableField("TERMINAL_TYPE")
    private Long terminalType;
    @TableField("isactive")
    private Integer isActive;
    @TableField("exist_group")
    private Long existGroup;
    @TableField("is_login")
    private Integer isLogin;
    @TableField("face_device_number")
    private Integer faceDeviceNumber;
    @TableField("floor_ids")
    private String floorIds;
    @TableField(value = "WX_VIDEOPHONE_GROUP_ID",jdbcType = JdbcType.INTEGER)
    private Long wxVideophoneGroupId;
    @TableField("sn")
    private String sn;
    @TableField("terminal_name")
    private String terminalName;
    @TableField("install_time")
    private Date installTime;
    @TableField("terminal_status")
    private Integer terminalStatus;
    @TableField("white_resert_flag")
    private Integer whiteResertFlag;
    @TableField("soft_flag")
    private Integer softFlag;
    @TableField("status_operate")
    private Integer statusOperate;
    @TableField("parent_terminal_id")
    private Long parentTerminalId;
    @TableField("TYPE_ID")
    private Long typeId;
    @TableField("RELOCATION_TIME")
    private Date relocationTime;
    @TableField("DEACTIVATED_AT")
    private Date deactivatedAt;
    /**
     * 白名单更新LastID, 记录ID
     */
    @TableField("WHITE_LAST_ID")
    private Long whiteLastId;
    @TableField("remark")
    private String remark;
    @TableField("INSTALL_TYPE")
    private String installType;
    @TableField("user_Ids")
    private String userIds;
    @TableField("user_names")
    private String userNames;

    @TableField("IMG_ID")
    private Long imgId;
    @TableField("direction")
    private Integer direction;
    @TableField("pwd")
    private String pwd;
    @TableField("MANUFACTURER_ID")
    private Long manufacturer;
    @TableField("IMGNUM")
    private Integer imgNum;
    @TableField("CHECK_HEALTH_CODE")
    private Integer checkHealthCode;
    @TableField("HEALTH_CODE_FAILED_HANDLE")
    private Integer healthCodeFailedHandle;
    @TableField("EXIST_CHILD")
    private Integer existChild;
    /**
     * 软件版本号
     */
    @TableField("SOFT_VERSION")
    private String softVersion;

    /**
     * 最终ip地址
     */
    @TableField("LAST_IP")
    private String lastIp;

    /**
     * 终端最后连接时间
     */
    @TableField("LAST_TIME")
    private LocalDateTime lastTime;

    @TableField("INSTALL_ADDRESS")
    private String installAddress;

    @TableField("SIM_NUM")
    private String simNum;
    @TableField("threshold")
    private Integer threshold;

    /**
     * 更新时间
     */
    @TableField("UPDATE_TIME")
    private LocalDateTime updateTime;


    /**
     * imei
     */
    @TableField("IMEI")
    private String imei;
    @TableField("BRANCH_SCHOOLIDS")
    private String branchSchoolIds;
    @TableField("LAST_OPERATE_TIME")
    private Date lastOperateTime = Calendar.getInstance().getTime();

    // 是宿舍终端
    public boolean isHostelTerminal() {
        return "d".equals(this.getTypeName()) || "4".equals(this.getTypeName()) || "j".equals(this.getTypeName());
    }

    // 是图书馆终端
    public boolean isLibraryTerminal() {
        return "8".equals(this.getTypeName());
    }

    // 是食堂终端
    public boolean isCanteenTerminal() {
        return "6".equals(this.getTypeName());
    }

    // 是校车终端
    public boolean isSchoolBusTerminal() {
        return "7".equals(this.getTypeName());
    }

    // 是否有人脸识别设备
    public boolean hasFaceDevices() {
        return faceDeviceNumber != null && faceDeviceNumber > 0;
    }

    public JSONObject toJSON() {
        JSONObject json = new JSONObject();
        json.put("@class", "com.joinus.edc.entity.TerminalCache");
        json.put("id", this.getId());
        json.put("softVersion", this.getSoftVersion());
        json.put("isLogin", this.getIsLogin());
        json.put("lastIp", this.getLastIp());
        JSONArray array = new JSONArray();
        array.add("java.util.Date");
        ZoneId zoneId = ZoneId.systemDefault();
        ZonedDateTime zonedDateTime = this.getLastTime().atZone(zoneId);
        array.add(zonedDateTime.toInstant().toEpochMilli());
        json.put("lastTime", array);
        return json;
    }


    public JSONObject toTerminalStudentIncrementJson() {
        JSONObject json = new JSONObject();
        json.put("id", this.getId());
        json.put("whiteLastId", this.getWhiteLastId());
        return json;
    }
}
