package com.joinus.dao;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR> anpengyu
 * @create 2024/10/25 14:30
 */
@TableName(value = "t_terminal_extras")
@Data
public class TerminalExtrasEntity {

    @TableField("TERMINAL_ID")
    private Long terminalId;
    @TableField("CARD_SELLING_PRICE")
    private BigDecimal cardSellingPrice;
    @TableField("updated_at")
    private Date updatedAt;
    @TableField("CREATED_AT")
    private Date createdAt;
    
}
