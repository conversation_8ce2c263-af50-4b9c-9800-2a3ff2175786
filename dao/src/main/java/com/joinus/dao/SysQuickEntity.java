package com.joinus.dao;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.time.LocalDateTime;

@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYS_QUICK")
public class SysQuickEntity {

    @TableId
    private Long id;
    private String quickImg;
    private String quickName;
    private String quickUrl;
    private Long relatedId;
    private Long quickOrder;
    private Integer isJf;

}
