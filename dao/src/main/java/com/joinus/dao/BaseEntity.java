package com.joinus.dao;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR> anpy
 * @create 2023/8/25 14:21
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BaseEntity {

    @TableField("creat_time")
    private Date createTime;

    @TableField("update_time")
    private Date updateTime;
}
