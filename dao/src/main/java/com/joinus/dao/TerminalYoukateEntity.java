package com.joinus.dao;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@TableName(value = "T_EXTERNAL_TERMINAL")
@KeySequence(value = "seq_T_EXTERNAL_TERMINAL", dbType = DbType.ORACLE)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TerminalYoukateEntity implements Serializable {

    @TableId(type = IdType.INPUT)
    private Long id;
    private Long schoolId;
    private Integer isactive;
    /*
     * 优卡特平台机号
     */
    private Integer machineNumber;
    /*
     * 优卡特平台设备号
     */
    private String deviceNumber;
    /*
     * 设备物理编号
     */
    private String devicePhysicalNumber;
    private Integer type;
    private Date createTime;
    private Date updateTime;

    /*
     * 设备供应商
     */
    private Integer deviceSupplier;
}
