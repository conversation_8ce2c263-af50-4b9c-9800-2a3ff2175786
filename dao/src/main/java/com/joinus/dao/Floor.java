package com.joinus.dao;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

/**
 * <AUTHOR> maweihua
 * @create 2024-8-28 16:53:08
 */
@TableName("T_FLOOR")
@KeySequence("SEQ_T_FLOOR")
@Data
public class Floor {
    @TableId(value = "ID",type = IdType.INPUT)
    private Long id;
    @TableField("floor_name")
    private String floorName;
    @TableField("ISACTIVE")
    private Integer isActive;
    @TableField("MANAGER_ID")
    private Integer managerId;
    @TableField("school_id")
    private Long schoolId;

}
