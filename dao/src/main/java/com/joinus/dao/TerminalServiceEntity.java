package com.joinus.dao;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import org.apache.ibatis.type.JdbcType;
import org.springframework.format.annotation.DateTimeFormat;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.Date;


@Data
@TableName("ijx.t_terminal_service")
@KeySequence("SEQ_TERMINAL_SERVICE")
public class TerminalServiceEntity {

    @TableId(value = "ID", type = IdType.INPUT)
    private Long id;

    @TableField("TERMINAL_ID")
    private Long terminalId;//终端id

    @TableField("FAULT_PHENOMENON")
    private String faultPhenomenon;//故障现象

    @TableField("TREATMENT_MEASURE")
    private String treatmentMeasure;//处理措施

    @TableField("SERVICEMAN")
    private String serviceman;//处理人

    @TableField("FAULT_PROGRAM")
    private String faultProgram;//故障终端程序

    @TableField("SCHOOL_ID")
    private Long schoolId;

    @TableField("REMARK")
    private String remark;

    @TableField("ORDER_TIME")
    private Date orderTime;

    @TableField("SERVICE_TIME")
    private Date serviceTime;

    @TableField("CREATE_TIME")
    private Date createTime;


}
