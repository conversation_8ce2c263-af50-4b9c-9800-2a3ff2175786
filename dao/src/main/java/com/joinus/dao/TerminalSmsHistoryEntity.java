
package com.joinus.dao;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName(value = "t_terminal_sms_history")
public class TerminalSmsHistoryEntity {
    @TableId(value = "ID",type = IdType.INPUT)
    private Long id;

    @TableField(value = "TERMINAL_ID")
    private Long terminalId;

    @TableField(value = "TERMINAL_NUM")
    private String terminalNum;

    @TableField(value = "smsc")
    private String smsc;

    @TableField(value = "sender")
    private String sender;


    @TableField(value = "content")
    private String content;

    @TableField(value = "RECEIVED_AT")
    private Date receivedAt;


}
