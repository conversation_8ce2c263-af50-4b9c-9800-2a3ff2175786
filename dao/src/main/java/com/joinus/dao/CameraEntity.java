package com.joinus.dao;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName(value = "T_CAMERA")
public class CameraEntity {
    /** CameraId */
    @TableId(value = "ID",type = IdType.INPUT)
    private Long id;
    /** 学校id */
    @TableField(value = "SCHOOL_ID")
    private Long schoolId;
    /** 班级id */
    @TableField(value = "CLASS_ID")
    private Long classId;
    /** 年级id */
    @TableField(value = "GRAD_ID")
    private Long gradId;
    /** 变更时间 */
    @TableField("UPDATEDATE")
    private Date updateDate;
    /** 类型，空代表没挂班级，0代表班级，1代表公共区域 */
    @TableField(value = "CTYPE")
    private String ctype;
    /** 具体位置id */
    @TableField(value = "LOCATION_ID")
    private Long locationId;
    /** 区域 */
    @TableField(value = "AREA_ID")
    private Long areaId;

    @TableField(value = "CHANNEL")
    private Integer channel;
    /**
     * 硬盘刻录机ID
     */
    @TableField(value = "NVR_ID")
    private Long nvrId;
}
