package com.joinus.dao;

import com.baomidou.mybatisplus.annotation.*;
import com.github.xiaoymin.knife4j.core.util.StrUtil;
import lombok.*;

import java.util.List;

/**
* 班级实体类
* <AUTHOR> anpy
* @create 2023/10/19 9:54
*/
@Data
@EqualsAndHashCode(callSuper=false)
@TableName("T_CLASS")
@KeySequence("SEQ_T_CLASS")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ClassEntity {
    /** 班级id */
    @TableId(value = "ID",type = IdType.INPUT)
    private Long id;
    /** 班级名称 */
    @TableField("class_name")
    private String className;
    /** 班长卡 */
    @TableField("monitor_code")
    private String monitorCode;
    /** 班长卡2 */
    @TableField("monitor_code2")
    private String monitorCode2;
    /** 学校ID */
    @TableField("school_id")
    private Long schoolId;
    /** 班主任教师ID */
    @TableField(value = "teacher_id", updateStrategy = FieldStrategy.IGNORED)
    private Integer teacherId;
    /** 年级ID */
    @TableField("grade_id")
    private Long gradeId;
    /** 班级名称（排序用） */
    @TableField("cla_name")
    private String claName;
    /** 年级名称（排序用） */
    @TableField("gra_name")
    private String graName;
    /** 学校名称 */
    @TableField("school_name")
    private String schoolName;
    @TableField("class_code")
    private String classCode;
    /** 班级序号、别名 */
    @TableField("class_number")
    private String classNumber;
    /** 环信群组ID */
    @TableField("mob_group_id")
    private Long mobGroupId;
    /** 报修学生ID */
    @TableField("REPAIR_ID")
    private String repairId;
    /** 班级二维码图片 */
    @TableField("class_img")
    private String classImg;
    /** 是否删除 */
    @TableField("isactive")
    private Integer isActive;
    /** 班主任电话 */
    @TableField(exist = false)
    private String telNum;
    /** 班主任姓名 */
    @TableField(exist = false)
    private String teacherName;

    /**
     * 判断班级中是否包含 教师或者教职工
     * @param subStrings
     * @return
     */
    public boolean isTeacher(List<String> subStrings) {
        if (StrUtil.isBlank(this.className)) {
            return false;
        }
        return subStrings.stream().anyMatch(className::contains);
    }
}
