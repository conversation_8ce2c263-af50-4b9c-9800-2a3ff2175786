package com.joinus.dao;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.time.LocalDateTime;

@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYS_USER")
public class SysUser implements Serializable {

    @TableId
    private Long id;
    private LocalDateTime addTime;
    private Integer isactive;
    private Integer isManager;
    private String loginIp;
    private LocalDateTime loginTime;
    private String userName;
    private Integer num;
    private String pass;
    private Long teacherId;
    private String telnum;
    private Integer masterManager;
    private Integer dormManagerLevel;
    private String email;
    @TableField("feishu_user_id")
    private String feiShuUserId;

    @TableField(exist = false)
    private String name;
}
