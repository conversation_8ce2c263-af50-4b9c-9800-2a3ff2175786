package com.joinus.dao;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <p>
 * 自推广业务开通表
 * </p>
 */
@Data
@EqualsAndHashCode
@Accessors(chain = true)
@TableName("T_CAMERA_CAPTURE_RECORD")
@KeySequence("SEQ_T_CAMERA_CAPTURE_RECORD")
public class CameraCaptureRecordEntity {

    private static final long serialVersionUID = 1L;

    @TableId
    private Long id;

    @TableField("CAPTURE_IMG_URL")
    private String captureImgUrl;

    @TableField("TERMINAL_NUM")
    private String terminalNum;

    @TableField("CHANNEL_NUMBER")
    private Integer channelNumber;

    @TableField("CAPTURE_BATCH")
    private String captureBatch;

    @TableField("CLASS_ID")
    private Long classId;

    @TableField("GRADE_ID")
    private Long gradeId;

    @TableField("SCHOOL_ID")
    private Long schoolId;

    @TableField("CAPTURED_AT")
    private Date capturedAt;

    @TableField("CREATED_AT")
    private Date createdAt;




}