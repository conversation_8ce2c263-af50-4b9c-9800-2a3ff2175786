<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.dao.mapper.UserMapper">

    <select id="getSysRegionIdsByUserId" resultType="String" >
        select u.new_region_id "regionId" from sys_region_new t
                                                   inner join sys_user_region u on t.region_id = u.new_region_id
        where u.user_id = #{userId}
	</select>

</mapper>