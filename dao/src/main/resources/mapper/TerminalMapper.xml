<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.dao.mapper.TerminalMapper">
    <resultMap id="TerminalResultMap" type="java.util.HashMap">
        <result column="terminalNum" property="terminalNum" />
        <result column="online" property="online" />
        <result column="lastOnlineTime" property="lastOnlineTime" />
    </resultMap>

    <select id="getTerminalOnlineStatus" resultMap="TerminalResultMap">
        SELECT t.terminal_num AS "terminalNum",
        DECODE(t.is_login, 0, 'false', 1, 'true', 'false') AS "online",
        t.last_time "lastOnlineTime"
        FROM t_terminal t
        INNER JOIN t_school ts ON t.school_id = ts.id
        WHERE ts.isactive = 1
    </select>

    <update id="updateTerminal">
        update T_TERMINAL set wx_videophone_group_id = null where id = #{id}
    </update>

    <update id="unstallTerminal">
        update t_terminal
        set school_id=null,
            class_id=null,
            install_address=null,
            install_time=null,
            terminal_status='0',
            white_resert_flag='0',
            soft_flag='0',
            is_login='0',
            status_operate='0',
            parent_terminal_id = -1
        where id =#{id}
    </update>

    <select id="getRootTerminalByChildId" resultType="com.joinus.dao.TerminalEntity">
        SELECT * FROM T_TERMINAL WHERE CONNECT_BY_ISLEAF = 1
            START WITH ID = #{childId}
        CONNECT BY PRIOR PARENT_TERMINAL_ID = ID
    </select>

    <update id="updateRootTerminalFaceDeviceNum">
        update T_TERMINAL
        set FACE_DEVICE_NUMBER =
                (select count(*)
                 from (select id, PARENT_TERMINAL_ID, TERMINAL_NAME, TERMINAL_TYPE, TERMINAL_STATUS, ISACTIVE
                       from T_TERMINAL start with id = #{rootId}
                       connect by PARENT_TERMINAL_ID = prior id) t
                 where TERMINAL_TYPE in (265, 281)
                   and TERMINAL_STATUS in (1, 3)
                   and ISACTIVE = 1)
        where id = #{rootId}
    </update>

    <select id="getActiveTerminalInfo"
            resultType="com.joinus.dao.po.TerminalInfoPO">
        select
        s.id "id",
        s.terminal_num "terminalNum",
        s.school_id "schoolId",
        tc.school_name "schoolName",
        s.terminal_name "terminalName",
        sd2.dict_desc "terminalTypeName",
        sd3.dict_desc "typeName",
        s.sim_num "simNum",
        sd.DICT_DESC "simStatus",
        til.price "price",
        til.oprcode "oprCode",
        s.LAST_CALL_TIME "lastCallTime",
        sd4.DICT_NAME "schoolType",
        tc.business_flag "businessFlag",
        s.install_address "installAddress",
        s.soft_version "softVersion",
        s.hard_version "hardVersion",
        s.last_ip "lastIp",
        s.terminal_status "terminalStatus",
        s.REMARK "remark",
        s.isactive "isActive",
        nvl(ttsi.total_costs, 0) "totalCosts",
        nvl(ttsi.current_month_expense, 0) "currentMonthExpense",
        nvl(ttsi.discount_offers, 0) "discountOffers",
        s.is_login "isLogin",
        s.LAST_TIME "lastTime"
        FROM t_terminal s
        LEFT JOIN T_SCHOOL tc ON tc.ID = s.SCHOOL_ID
        left join SYS_DICT sd ON sd.DICT_NAME = s.SIM_STATUS and sd.dict_id = 24
        left join sys_dict sd2 on s.terminal_type = sd2.id and sd2.dict_id = 26
        left join sys_dict sd3 on s.type_id = sd3.id and sd3.dict_id = 5
        left join sys_dict sd4 on tc.SCHOOL_TYPE_ID = sd4.id and sd4.dict_id = 1
        left join   (
                select
                childecid,
                max(price) AS price,
                max(oprcode) AS oprCode
                from
                t_interadc_laststus
                group by
                childecid
                ) til ON til.childecid = s.SIM_NUM
        left join t_terminal_sim_info ttsi on ttsi.terminal_id = s.id
        where s.parent_terminal_id=-1
        <if test="sort != null">
            order by ${sort}
        </if>
        <if test="sort == null">
            order by s.id
        </if>
    </select>

    <select id="getTerminalInfoBySchoolIdAndTerminalNum"
            resultType="com.joinus.dao.model.po.TerminalInfoBySchoolAndIdPO">
        SELECT te.terminal_num                                        "terminalNum",
               te.terminal_name                                       "terminalName",
               sd.id                                                  "terminalType",
               decode(te.terminal_status, '1', '已安装', '未安装')    "terminalStatus",
               te.hard_version                                        "hardVersion",
               te.soft_version                                        "softVersion",
               te.update_address                                      "updateAddress",
               te.white_address                                       "whiteAddress",
               te.msg_flag                                            "msgFlag",
               decode(te.white_flag, '1', '白名单表', '学生表')       "whiteFlag",
               te.bao_strong                                          "baoStrong",
               te.bao_time                                            "baoTime",
               te.install_address                                     "installAddress",
               to_char(te.install_time, 'YYYY-MM-DD HH24:MI:SS')      "installTime",
               decode(te.is_login, '0', '断开', '连接')               "isLogin",
               to_char(te.last_time, 'YYYY-MM-DD HH24:MI:SS')         "lastTime",
               te.last_ip                                             "lastIp",
               te.serial_num                                          "serialNum",
               te.sim_num                                             "simNum",
               te.white_updcount                                      "updCount",
               to_char(te.last_operate_time, 'YYYY-MM-DD HH24:MI:SS') "lastOperateTime"
        FROM T_TERMINAL te
                 left join SYS_DICT sd on te.type_id = sd.id
        where te.terminal_num = #{terminalNum}
          and te.school_id = #{schoolId}
    </select>

    <select id="listGroupTerminalByTerminalId" resultType="com.joinus.dao.po.TerminalStudentGroupPo">
        select t.TERMINAL_ID,
               t.GROUP_ID,
               tsg.GROUP_NAME
        from T_TERMINAL_STUDENT_GROUP t
                 inner join T_STUDENT_GROUP tsg on t.group_id = tsg.id
        where t.TERMINAL_ID = #{terminalId}
    </select>

    <update id="batchUpdateTerminalTree">
        UPDATE T_TERMINAL
        SET SCHOOL_ID = #{schoolId}, EXIST_GROUP = 0, FLOOR_IDS = null
        WHERE ID IN (
            SELECT CONNECT_BY_ROOT (ID) ID
            FROM T_TERMINAL T
            WHERE T.ID = #{rootId}
            CONNECT BY PRIOR T.PARENT_TERMINAL_ID = T.ID
        )
    </update>
    <select id="listUpperComputerChildren" resultType="com.joinus.dao.TerminalEntity">
        SELECT *
        FROM T_TERMINAL
        WHERE PARENT_TERMINAL_ID &lt;&gt; -1
            START WITH TERMINAL_NUM = #{terminalNum} and TERMINAL_TYPE=287
        CONNECT BY PRIOR ID = PARENT_TERMINAL_ID
    </select>

    <select id="getFaceTerminalNumByIds" resultType="com.joinus.dao.TerminalEntity">
        SELECT t.*
        FROM t_terminal t
        where (t.parent_terminal_id =-1 or t.terminal_type=281) and t.isactive=1
        START WITH id in
        <foreach collection="terminalIdList" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
        CONNECT BY PRIOR id = parent_terminal_id
    </select>

</mapper>