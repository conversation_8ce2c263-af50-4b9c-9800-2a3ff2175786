<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.dao.mapper.PermissionMapper">

    <!-- 根据父ID查询所有子权限 -->
    <select id="listAllPermissionByParentId" resultType="com.joinus.dao.po.PermissionPO">
        select t.*,
               tr.name as resourceName,
               tr.url_path as resourceUrlPath,
               tr.method as method,
               tr.type as type,
               tr.remark as resourceRemark
        from t_permission t
                 left join t_resource tr on t.resource_id = tr.id and tr.platform = #{platform}
        where t.isactive = 1 and t.platform = #{platform}
            start with t.parent_id = #{parentId}
        connect by prior t.id = t.parent_id
        order siblings by t.sort
    </select>

</mapper>