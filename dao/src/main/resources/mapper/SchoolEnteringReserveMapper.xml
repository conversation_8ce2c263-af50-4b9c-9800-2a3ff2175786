<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.dao.mapper.SchoolEnteringReserveMapper">

    <select id="listSchoolEnteringReserveByVerificationCode" resultType="com.joinus.dao.SchoolEnteringReserveEntity">
        SELECT e.id,
        e.current_identity,
        e.reason,
        e.enter_time,
        e.leave_time,
        e.cancel_reason,
        e.start_user_id,
        e.name,
        e.phone_number,
        e.verification_code,
        vi.actual_enter_time,
        vi.actual_leave_time
        FROM T_SCHOOL_ENTERING_RESERVE e
        INNER JOIN T_SCHOOL_RESERVE_VISITOR vi
        ON e.id = vi.RESERVE_ID
        WHERE vi.STATUS = 1
        AND e.STATUS != 3
        AND vi.school_ID = #{schoolId}
        AND e.leave_time > trunc(sysdate)
        AND e.VERIFICATION_CODE =#{verificationCode}

    </select>


    <select id="listSchoolEnteringReserveByVisitId"  resultType="com.joinus.dao.SchoolEnteringReserveEntity">
        SELECT e.id,
        e.current_identity,
        e.reason,
        e.enter_time,
        e.leave_time,
        e.cancel_reason,
        e.start_user_id,
        e.name,
        e.phone_number,
        e.verification_code,
        vi.actual_enter_time,
        vi.actual_leave_time
        FROM T_SCHOOL_ENTERING_RESERVE e
        INNER JOIN T_SCHOOL_RESERVE_VISITOR vi
        ON e.id = vi.RESERVE_ID
        WHERE vi.STATUS = 1
        AND e.STATUS != 3
        AND vi.school_ID = #{schoolId}
        AND e.leave_time > trunc(sysdate)
        AND vi.VISIT_ID =#{visitId}

    </select>
</mapper>