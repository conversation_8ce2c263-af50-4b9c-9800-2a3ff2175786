<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.dao.mapper.StudentMapper">

    <select id="findStudent" resultType="com.joinus.dao.StudentEntity">
        select
        t.*,
        nvl(twa.amount, 0) "walletBalance"
        from t_student t
        left join t_wallet_account twa on t.identity = twa.identity and t.school_id = twa.mch_id and twa.type = 's'
        where t.isactive=1
        <if test="id != null">
            and t.id = #{id}
        </if>
        <if test="schoolId != null">
            and t.school_id= #{schoolId}
        </if>
        <if test="classId != null">
            and t.class_id= #{classId}
        </if>
        <if test="studentCode != null and studentCode != ''">
            and t.make_card= #{makeCard}
        </if>
        <if test="cardCode != null and cardCode != ''">
            and t.cardcode= #{cardCode}
        </if>
        <if test="studentName != null and studentName != ''">
            and t.student_name= #{studentName}
        </if>
        order by student_name
    </select>

    <select id="getStudentsByParentPhone" resultType="com.joinus.dao.model.dto.StudentBasicInfoDTO">
        select  ts.id as studentId,
                ts.student_name as studentName,
                tc.id as classId,
                tc.class_name as className,
                tg.id as gradeId,
                tg.grade_name as gradeName,
                tsc.id as schoolId,
                tsc.school_name as schoolName
        from t_student ts
                 inner join t_student_parent tsp on ts.id = tsp.student_id
                 inner join t_parent tp on tsp.parent_id = tp.id and tp.isactive = 1
                 inner join t_class tc on tc.id = ts.class_id and tc.isactive = 1
                 inner join t_grade tg on tg.id = tc.grade_id and tg.isactive = 1
                 inner join t_school tsc on tsc.id = ts.school_id and tsc.isactive = 1
        where ts.isactive = 1
          and tp.tel_num =  #{parentPhone}
    </select>


    <select id="findStudentPassportNumById" resultType="long">
        select u.id from t_student u where u.isactive = 1
        <if test="id != null">
            <if test="id != 0">
                and <![CDATA[ u.id<>#{id} ]]>
            </if>
        </if>
        <if test="studentName != null and studentName != ''">
            and <![CDATA[ u.student_name<>#{studentName} ]]>
        </if>
        and u.passport_no = #{passportNum}
        and u.school_id = #{schoolId}
    </select>

    <!-- 添加一条学生信息 -->
    <insert id="addStudent">
        insert into t_student(
        id, add_time, address, birth, cardcode, demo_qinqing, identity, in_year,
        isactive, isbus, isdorm, is_guashi, is_kai, kai_num, order_num, region_id,
        school_id, sex, make_card, student_code, student_dock_code ,student_img, student_name, nation_id, class_id,
        code_num,
        photo_name, istime1, istime2, istime3, bulk_paste_img, rank_id,imei,
        face_feature, tel_num,
        passport_name,passport_no,info_source)
        values(
        SEQ_T_STUDENT.nextval, sysdate, #{address,jdbcType=VARCHAR}, #{birth,jdbcType=VARCHAR},
        #{cardCode,jdbcType=VARCHAR}, #{demoQinqing,jdbcType=VARCHAR}, #{identity,jdbcType=VARCHAR},
        #{inYear,jdbcType=VARCHAR},
        #{isactive,jdbcType=INTEGER}, #{isbus,jdbcType=INTEGER}, #{isdorm,jdbcType=INTEGER},
        #{isGuashi,jdbcType=INTEGER}, #{isKai,jdbcType=INTEGER}, #{kaiNum,jdbcType=INTEGER},
        #{orderNum,jdbcType=VARCHAR}, #{regionId,jdbcType=INTEGER},
        #{schoolId,jdbcType=BIGINT}, #{sex,jdbcType=INTEGER}, #{makeCard,jdbcType=VARCHAR},
        #{studentCode,jdbcType=VARCHAR}, #{studentDockCode,jdbcType=VARCHAR}, #{studentImg,jdbcType=VARCHAR},
        #{studentName,jdbcType=VARCHAR},
        #{nationId,jdbcType=INTEGER}, #{classId,jdbcType=BIGINT}, #{codeNum,jdbcType=VARCHAR},
        #{photoName,jdbcType=VARCHAR}, #{istime1,jdbcType=INTEGER}, #{istime2,jdbcType=INTEGER},
        #{istime3,jdbcType=INTEGER}, #{bulkPasteImg,jdbcType=VARCHAR}, #{rankId,jdbcType=INTEGER},
        #{imei,jdbcType=VARCHAR},
        <if test="infoSource != null">
            #{infoSource}
        </if>
        <if test="infoSource == null">
            2
        </if>
        )
    </insert>

    <!--将制卡状态置为4-->
    <update id="updateCardStatus">
        update t_student s
        set s.CARD_STATUS = 4,
            s.SIGNTIME    = NULL,
            s.SIGNER      = NULL,
            s.CASUAL_CARD = null,
            s.ITEMNUM     = null,
            s.COMPANY     = null,
            s.COMMITER    = null,
            s.COMMITTIME  = NULL
        where s.id = #{studentId}
    </update>

    <select id="findStudentsByParam" resultType="com.joinus.dao.StudentEntity">
        select t.id "id",
        t.STUDENT_NAME "studentName",
        t.STUDENT_IMG "studentImg",
        c.class_name "className",
        t.SEX "sex",
        g.GRADE_NAME "gradeName",
        t.identity "identity",
        t.FACE_FEATURE "faceFeature"
        from T_STUDENT t
        left join T_CLASS c on c.id = t.CLASS_ID
        left join T_GRADE g on g.id = c.GRADE_ID
        where t.school_id = #{param.schoolId}
        and t.isactive = 1
        and t.FACE_FEATURE is not null
        <if test="param.gradeId != null">
            and c.GRADE_ID = #{param.gradeId}
        </if>
        <if test="param.classId != null">
            and t.class_id = #{param.classId}
        </if>
        <if test="param.studentName != null">
            and t.student_name like '%'||#{param.studentName}||'%'
        </if>
        <if test="param.studentImg != null">
            and t.student_img = #{param.studentImg}
        </if>
    </select>

    <select id="loadStudentById" resultType="com.joinus.dao.StudentEntity">
        SELECT s.*, sc.SCHOOL_NAME
        FROM T_STUDENT s,
             T_SCHOOL sc
        WHERE s.id = #{studentId}
          AND s.SCHOOL_ID = sc.id
    </select>

    <select id="findStudentByPatrols" resultType="String">
        select tg.school_name "schoolName"
        from T_STUDENT ts,
             T_CLASS tc,
             T_GRADE tg,
             T_STUDENT_PARENT tsp,
             T_PARENT tp
        where ts.CLASS_ID = tc.ID
          and tc.GRADE_ID = tg.id
          and tsp.PARENT_ID = tp.ID
          and ts.id = tsp.STUDENT_ID
          and tp.TEL_NUM = #{param.phone}
          and tg.id = #{param.gradeId}
    </select>

    <select id="getBindFloorIdsByStudentId" resultType="long">
        select distinct f.id
        from T_BED b,
             T_DORM d,
             T_LAYER l,
             T_FLOOR f
        where b.DORM_ID = d.ID
          and l.ID = d.LAYER_ID
          and f.ID = l.FLOOR_ID
          and f.ISACTIVE = 1
          and l.ISACTIVE = 1
          and d.ISACTIVE = 1
          and b.ISACTIVE = 1
          and b.ALLOT = 1
          and b.DORM_ID IS NOT NULL
          and b.student_id = #{studentId}
    </select>

    <select id="getBindGroupTerminalIdByStudentId" resultType="long">
        select distinct tsg.terminal_id
        from T_TERMINAL_STUDENT_GROUP tsg,
             T_STUDENT_GROUP_DETAIL sgd
        where tsg.GROUP_ID = sgd.GROUP_ID
          and sgd.student_id = #{studentId}
    </select>

    <select id="getBindPatrolsTerminalIdByStudentId" resultType="long">
        SELECT distinct tprt.terminal_id
        FROM T_PATROLS_RANGE_PERSONNEL tprp
                 inner join T_PATROLS_RANGE_TERMINAL tprt
                            on tprp.range_id = tprt.range_id
        where tprp.is_del = 1
          and tprt.is_del = 1
          and tprp.personnel_id = #{studentId}
    </select>

    <select id="getBindPatrolsTerminalIdBySchoolId" resultType="long">
        SELECT distinct tprt.terminal_id
        FROM T_PATROLS_RANGE tpr
                 inner join T_PATROLS_RANGE_TERMINAL tprt
                            on tpr.id = tprt.range_id
        where tpr.is_del = 1
          and tprt.is_del = 1
          and tpr.school_id= #{schoolId}
    </select>



    <select id="queryAllStudentByClassIdSize" resultType="int">
        select count(1)
        from T_STUDENT ts,
        T_CLASS tc,
        T_GRADE tg
        where ts.CLASS_ID = tc.id
        and tc.GRADE_ID = tg.id
        and ts.ISACTIVE = 1
        and tc.ISACTIVE = 1
        and tg.ISACTIVE = 1
        and ts.SCHOOL_ID = #{schoolId}
        and tc.id in
        <foreach collection="classIdList" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>

    <select id="queryAllStudentByClassId" resultType="com.joinus.dao.StudentEntity">
        select ts.*
        from T_STUDENT ts,
        T_CLASS tc,
        T_GRADE tg
        where ts.CLASS_ID = tc.id
        and tc.GRADE_ID = tg.id
        and ts.ISACTIVE = 1
        and tc.ISACTIVE = 1
        and tg.ISACTIVE = 1
        and ts.SCHOOL_ID = #{schoolId}
        and tc.id in
        <foreach collection="classIdList" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>

    <select id="queryInSchoolStudentByClassIdSql" resultType="com.joinus.dao.StudentInSchoolStatusEntity">
        select tsiss.student_id "studentId" ,tsiss.status "status"
        from T_STUDENT_IN_SCHOOL_STATUS tsiss,
        T_STUDENT ts
        where ts.id = tsiss.STUDENT_ID
        and ts.class_id in
        <foreach collection="classIdList" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>


    <select id="listAssessmentEmployees" resultType="com.joinus.dao.po.AssessmentEmployeePO">
        select distinct ts.id as studentId,
                        tp.tel_num as mobile,
                        ts.cardcode as cardCode
        from t_student ts
                 inner join t_student_parent tsp on ts.id = tsp.student_id
                 inner join t_parent tp on tsp.parent_id = tp.id and tp.isactive = 1
        where ts.isactive = 1
          and ts.class_id =  #{classId}
    </select>

</mapper>