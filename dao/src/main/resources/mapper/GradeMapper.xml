<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.dao.mapper.GradeMapper">

    <select id="getUserGrade" resultType="com.joinus.dao.GradeEntity">
        <![CDATA[
        select
            t.id,				t.grade_name,				t.isactive,		t.school_name,
            t.school_id,		t.gra_name,				t.update_time,	t.grade_code,
            (SELECT LISTAGG (t.id, ',') WITHIN GROUP (ORDER BY t.id)
             FROM t_teacher t, t_grade_teacher gt
             WHERE gt.teacher_id = t.id AND t.isactive = 1 AND gt.grade_id = #{gradeId})
                teacherId,
            (SELECT LISTAGG (t.teacher_name, ',') WITHIN GROUP (ORDER BY t.id)
             FROM t_teacher t, t_grade_teacher gt
             WHERE gt.teacher_id = t.id AND t.isactive = 1 AND gt.grade_id = #{gradeId})
                teacherName,
            (SELECT LISTAGG (t.tel_num, ',') WITHIN GROUP (ORDER BY t.id)
             FROM t_teacher t, t_grade_teacher gt
             WHERE gt.teacher_id = t.id AND t.isactive = 1 AND gt.grade_id = #{gradeId})
                telNum,
            t.the_number theNumber,
            t.learning_period learningPeriod,
            t.def_type defType,
            ta.id appRoverId,
            ta.teacher_name apRoverName
        from t_grade t
                 left join t_teacher ta on ta.id = t.teacher_id and ta.isactive=1
        where t.isactive=1 and t.id=#{gradeId}
        ]]>
    </select>

</mapper>