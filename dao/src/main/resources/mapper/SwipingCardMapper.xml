<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.dao.mapper.SwipingCardMapper">

    <select id="querySwipingRecordForHistory" resultType="com.joinus.dao.po.SwipingCardPO">
        select a.id, f.dict_desc "terminalType",a.terminalnum "terminalNum",a.cardnum "cardNum",to_char(a.CARDTIME, 'yyyy-mm-dd hh24:mi:ss')
        "cardTime",a.schoolid "schoolId",t.TERMINAL_NAME "terminalName",
        a.direction,to_char(a.savetime, 'yyyy-mm-dd hh24:mi:ss')
        "saveTime",b.school_name "schoolName",c.student_name "studentName",c.isdorm "isDorm",b.iswhite "isWhite",e.class_name "className",a.THERMOMETER,
        a.IMAGE_URL "imageUrl",fr.REASON,a.ADDRESS,a.SIGN_TYPE "signType",a.SUB_TERMINAL_NUM "subTerminalNum", a.health_flag "healthFlag", a.health_info
        "healthInfo",t.id "terminalId",t.img_id "imgId"
        from ${param.tableName} a
        left join t_school b on b.id = a.schoolid
        left join t_student c on ((a.student_id is not null and a.student_id = c.id) or (c.CARDCODE = a.cardnum)) and
        c.isactive = 1
        left join t_class e on e.id = c.class_id
        left join sys_dict f on f.dict_name = a.terminaltype and dict_id = 5
        left join t_terminal t on a.terminalnum = t.terminal_num
        left join t_signin_forbidden_reason fr on a.REASON_CODE = fr.CODE
        where a.CARDTIME >= #{param.dateBeginDate}
        and  a.CARDTIME &lt; #{param.dateEndDate}
        <if test="param.schoolId != null">
            and a.SCHOOLID = #{param.schoolId}
        </if>
        <if test="param.gradeId != null">
            and e.GRADE_ID = #{param.gradeId}
        </if>
        <if test="param.classId != null">
            and c.CLASS_ID = #{param.classId}
        </if>
        <if test="param.studentName != null and param.studentName != ''">
            and c.STUDENT_NAME = #{param.studentName}
        </if>
        <if test="param.cardNum != null and param.cardNum != ''">
            and a.CARDNUM = #{param.cardNum}
        </if>
        <if test="param.terminalNum != null and param.terminalNum != ''">
            and a.terminalnum = #{param.terminalNum}
        </if>
         <if test="param.signType != null">
            and a.SIGN_TYPE = #{param.signType}
        </if>
        <if test="param.direction != null">
            and a.DIRECTION = #{param.direction}
        </if>
        <if test="param.healthFlag != null">
            and a.health_flag = #{param.healthFlag}
        </if>
        <if test="param.moveOutStranger != null and param.moveOutStranger">
            and a.CARDNUM != '**********'
        </if>
        order by a.CARDTIME desc
    </select>

    <select id="queryCardSigninRecordForToday" resultType="com.joinus.dao.po.SwipingCardPO">
        select a.id, f.dict_desc "terminalType",a.terminalnum "terminalNum",a.cardnum "cardNum",to_char(a.CARDTIME, 'yyyy-mm-dd hh24:mi:ss')
        "cardTime",a.schoolid "schoolId",a.THERMOMETER,t.TERMINAL_NAME "terminalName",
        a.direction,to_char(a.savetime, 'yyyy-mm-dd hh24:mi:ss')
        "saveTime",b.school_name "schoolName",c.student_name "studentName",c.isdorm "isDorm",b.iswhite "isWhite",e.class_name "className",a.IMAGE_URL "imageUrl",fr.REASON,
        a.ADDRESS,a.SIGN_TYPE "signType",a.SUB_TERMINAL_NUM "subTerminalNum", a.health_flag "healthFlag", a.health_info "healthInfo",t.id "terminalId",t.img_id "imgId"
        from (
        Select "ID","SCHOOL_ID" AS "SCHOOLID","TERMINAL_TYPE" AS "TERMINALTYPE","TERMINAL_NUM" AS
        "TERMINALNUM","CARD_NUM" AS "CARDNUM",
        "CARD_TIME" AS "CARDTIME","DIRECTION","SAVE_TIME" AS "SAVETIME",THERMOMETER AS "THERMOMETER", IMAGE_URL,
        REASON_CODE, ADDRESS, SIGN_TYPE, SUB_TERMINAL_NUM,
        health_flag, health_info
        From t_Card_Signin_Record6
        ) a
        left join t_school b on b.id = a.schoolid
        left join t_student c on c.CARDCODE = a.CARDNUM and c.isactive=1
        left join t_class e on e.id = c.class_id
        left join sys_dict f on f.dict_name = a.terminaltype and dict_id = 5
        left join t_terminal t on a.TERMINALNUM = t.TERMINAL_NUM
        left join t_signin_forbidden_reason fr on a.REASON_CODE = fr.CODE
        where cardTime >= TRUNC(SYSDATE)
        AND cardTime &lt; TRUNC(SYSDATE + 1)
        <if test="param.schoolId != null">
            and a.SCHOOLID = #{param.schoolId}
        </if>
        <if test="param.gradeId != null">
            and e.GRADE_ID = #{param.gradeId}
        </if>
        <if test="param.classId != null">
            and c.CLASS_ID = #{param.classId}
        </if>
        <if test="param.studentName != null and param.studentName != ''">
            and c.STUDENT_NAME = #{param.studentName}
        </if>
        <if test="param.cardNum != null and param.cardNum != ''">
            and a.CARDNUM = #{param.cardNum}
        </if>
        <if test="param.terminalNum != null and param.terminalNum != ''">
            and a.terminalnum = #{param.terminalNum}
        </if>
         <if test="param.signType != null">
            and a.SIGN_TYPE = #{param.signType}
        </if>
        <if test="param.direction != null">
            and a.DIRECTION = #{param.direction}
        </if>
        <if test="param.healthFlag != null">
            and a.health_flag = #{param.healthFlag}
        </if>
        <if test="param.moveOutStranger != null and param.moveOutStranger">
            and a.CARDNUM != '**********'
        </if>
        order by a.CARDTIME desc
    </select>

</mapper>