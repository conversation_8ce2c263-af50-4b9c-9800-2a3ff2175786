<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.dao.mapper.CardCashbackMapper">


    <select id="queryByLogdateAndSchoolId" resultType="com.joinus.dao.po.CardCashbackPO">
        select t.logdate as logdate,
               t.school_id as schoolId,
               t.fill_card_fee as makeupCardFee,
               t.replenishment_num as monthlyMakeupCardCount,
               t.pay_num as paymentAmount,
               ts.rebate_money as perCardCommission,
               t.cashback_num as makeupCardCommissionTotal,
               ts.rebate_account as makeupCardCommissionAccount,
               ts.rebate_account_name as makeupCardCommissionAccName,
               ts.rebate_money_admin as perCardSchoolCommission,
               tccs.cashback_num as schoolCommissionTotal,
               ts.rebate_account_admin as schoolCommissionAccount,
               ts.rebate_account_admin_name as schoolCommissionAccountName
        from t_card_cashback t
                 inner join t_school ts on t.school_id = ts.id
                 inner join t_card_cashback_school tccs on t.logdate = tccs.logdate and t.school_id = tccs.school_id
        where t.logdate = #{logdate}
            and t.school_id in
            <foreach collection="schoolIdList" item="schoolId" separator="," open="(" close=")">
               #{schoolId}
            </foreach>

    </select>
</mapper>