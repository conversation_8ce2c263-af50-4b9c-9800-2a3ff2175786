<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.dao.mapper.TerminalSmsHistoryMapper">

    <select id="listLast24HourTerminalSms" resultType="com.joinus.dao.TerminalSmsHistoryEntity">
        select a.id "id",
        a.terminal_id "terminalId",
        a.terminal_num "terminalNum",
        a.smsc "smsc",
        a.sender "sender",
        a.content "content",
        a.received_at "receivedAt",
        a.created_at "createdAt"
        from (select row_number() over ( partition by terminal_id order by received_at desc, id desc ) rn, sh.*
        from t_terminal_sms_history sh
        where sh.content like '【套餐资费介绍】%'
        and sh.received_at >= TRUNC(sysdate - 1)
        and sh.received_at &lt; TRUNC(sysdate)
        ) a
        where a.rn = 1
        union
        select a.id "id",
        a.terminal_id "terminalId",
        a.terminal_num "terminalNum",
        a.smsc "smsc",
        a.sender "sender",
        a.content "content",
        a.received_at "receivedAt",
        a.created_at "createdAt"
        from (select row_number() over ( partition by terminal_id order by received_at desc, id desc ) rn, sh.*
        from t_terminal_sms_history sh
        where sh.content like '您好，您已开通收费业务有%'
        and sh.received_at >= TRUNC(sysdate - 1)
        and sh.received_at &lt; TRUNC(sysdate)
        ) a
        where a.rn = 1
    </select>
</mapper>