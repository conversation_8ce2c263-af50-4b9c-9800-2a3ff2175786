<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.dao.mapper.SysRoleMapper">

    <!--  查询系统管理员用户身份  -->
    <select id="getRoleByUserId" resultType="com.joinus.dao.SysRoleEntity">
        select *
        from sys_role t
        where id = (select role_id
                    from sys_group
                    where id = (select group_id from sys_user_group where user_id = #{user_id}))
          and isactive = 1
    </select>

</mapper>