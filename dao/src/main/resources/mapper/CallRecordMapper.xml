<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.dao.mapper.CallRecordMapper">


    <select id="listCallRecordHistory" resultType="com.joinus.dao.entity.CallRecord">
        select * from t_qin_phone_dtl_his t
        where t.logdate >= #{startTime}
        and t.logdate &lt;= #{endTime}
        order by t.id asc
    </select>
</mapper>