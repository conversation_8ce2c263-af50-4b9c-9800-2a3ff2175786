<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.dao.mapper.SchoolMapper">

    <!--    根据id查询学校信息-->
    <select id="getSchoolById" resultType="com.joinus.dao.SchoolEntity">
        select a.STUDENT_LOCATION,
               to_char(a.init_feature_time, 'yyyy-MM-dd hh24:mi:ss')                                  "init_feature_time",
               decode(a.init_feature_flag, 0, '未初始化', 1, '初始化中', 2, '初始化完成', '未初始化') "init_feature_flag",
               a.LEAVE_TYPE,
               a.LEAVE_NOTICE_TYPE,
               a.leave_access_limit,
               a.leave_resume_interval,
               a.message_flag,
               a.id,
               a.charge_time,
               a.new_region_id,
               a.dorm_time,
               a.groupid,
               a.isactive,
               a.iskai,
               a.isunify,
               a.isverify,
               a.iswhite,
               a.ismonitor,
               a.menwei_msg_flag,
               a.msgport,
               a.prodid,
               a.school_code,
               a.school_desc,
               a.school_name,
               a.status,
               a.summer_end,
               nvl(a.interaction_flag, '1')                                                           "interaction_flag",
               a.summer_start,
               a.winter_end,
               a.winter_start,
               a.school_type_id,
               a.parent_id,
               a.region_id,
               a.ismanager                                                                              "isManager",
               a.is_open_video_phone,
               a.add_time,
               a.org_id,
               a.oa_domain_id,
               a.wm_teacher_groupid,
               a.wm_parent_groupid,
               decode(a.school_Card_Type, null, 1, a.school_Card_Type)                                schoolCardType,
               decode(a.school_Card_Num, null, 20, a.school_Card_Num)                                 schoolCardNum,
               a.ten_qinphone,
               a.view_credit_mall,
               a.m1kbcardquota,
               a.g24kbcardquota,
               a.school_nature,
               (select to_char(substr(wmsys.wm_concat(t.TEACHER_NAME), 1, 1000)) teacher_name
                from T_TEACHER t
                WHERE t.ISACTIVE = 1
                  AND t.SCHOOL_ID = #{schoolId}
                  and exists(select 1
                             from T_ROLE r
                             where r.ROLE_NAME = '补卡老师'
                               AND r.SCHOOL_ID = #{schoolId}
                               and r.id = t.role_id)) as                                              fill_card,
               (select to_char(substr(wmsys.wm_concat(t.TEL_NUM), 1, 1000)) telnum
                from T_TEACHER t
                WHERE t.ISACTIVE = 1
                  AND t.SCHOOL_ID = #{schoolId}
                  and exists(select 1
                             from T_ROLE r
                             where r.ROLE_NAME = '补卡老师'
                               AND r.SCHOOL_ID = #{schoolId}
                               and r.id = t.role_id)) as                                              fill_card_telnum,
               a.school_pay,
               a.school_address,
               a.business_flag,
               a.custom_level,
               a.card_hardtype,
               a.card_type,
               a.non_mobile_support,
               a.yxopen_buss,
               a.one_cartoon_pay,
               to_char(q.start_date, 'yyyy-MM-dd')                                                    start_date,
               to_char(q.end_date, 'yyyy-MM-dd')                                                      end_date,
               q.qrcode_url,
               a.m1kbcardquota,
               a.g24kbcardquota,
               a.fill_card,
               a.fill_card_telnum,
               a.school_address,
               a.business_flag,
               a.custom_level,
               a.card_hardtype,
               a.card_type,
               a.CALL_REMIND_MESSAGE,
               a.msg_sign,
               a.is_show_open_biz,
               (select to_char(substr(wmsys.wm_concat(user_name), 1, 1000))
                           user_name
                from sys_role e
                         inner join sys_group d on d.role_id = e.id
                         inner join sys_user_group c on c.group_id = d.id
                         inner join sys_user b on b.id = c.user_id
                         inner join sys_user_school f on f.user_id = b.id
                where f.school_id = a.id
                  and e.id in (6, 215))
                                                      as                                              market_manager,
               (select to_char(substr(wmsys.wm_concat(telnum), 1, 1000)) telnum
                from sys_role e
                         inner join sys_group d on d.role_id = e.id
                         inner join sys_user_group c on c.group_id = d.id
                         inner join sys_user b on b.id = c.user_id
                         inner join sys_user_school f on f.user_id = b.id
                where f.school_id = a.id
                  and e.id in (6, 215))
                                                      as                                              market_telnum,
               (select to_char(substr(wmsys.wm_concat(user_name || '(' || telnum || ')'), 1, 1000))
                           serviceManager
                from sys_role e
                         inner join sys_group d on d.role_id = e.id
                         inner join sys_user_group c on c.group_id = d.id
                         inner join sys_user b on b.id = c.user_id
                         inner join sys_user_school f on f.user_id = b.id
                where f.school_id = a.id
                  and e.id in (6, 215))
                                                      as                                              service_manager,
               (select to_char(substr(wmsys.wm_concat(user_name || '(' || telnum || ')'), 1, 1000))
                           serviceAttache
                from sys_role e
                         inner join sys_group d on d.role_id = e.id
                         inner join sys_user_group c on c.group_id = d.id
                         inner join sys_user b on b.id = c.user_id
                         inner join sys_user_school f on f.user_id = b.id
                where f.school_id = a.id
                  and e.id in (334, 454))
                                                      as                                              service_attache,
               (select to_char(substr(wmsys.wm_concat(aa.tel_num), 1, 1000)) telnum
                from t_teacher aa
                         inner join t_role bb on bb.id = aa.role_id and bb.role_type = 1
                where aa.school_id = #{schoolId}
                  and aa.isactive = 1)                                                                school_name_qp,
               (select to_char(substr(wmsys.wm_concat(aa.teacher_name), 1, 1000)) teacher_name
                from t_teacher aa
                         inner join t_role bb on bb.id = aa.role_id and bb.role_type = 1
                where aa.school_id = #{schoolId}
                  and aa.isactive = 1)                                                                school_name_jp,
               (select hc.cardtype_name
                from t_hardtype_cardtype hc
                where a.card_hardtype = hc.id)
                                                      as                                              cardhardtype_name,
               (select hc.cardtype_name
                from t_hardtype_cardtype hc
                where a.card_type = hc.id)
                                                      as                                              cardtype_name,
               a.BIZ_CONFIG_METHOD,
               a.LIBRARY_BOOK_LIMIT,
               a.iskai_zyk,
               a.msgport_zyk,
               a.longitude,
               a.latitude,
               nvl(a.contract_number, 0)                                                              contractNumber
        from t_school a
                 left join T_QRCODE_LINK q on q.school_id = a.id
        where a.id = #{schoolId}
    </select>

    <select id="getTenQinPhoneByStudentId" resultType="Integer">
        SELECT TEN_QINPHONE
        FROM T_SCHOOL ts,
             T_STUDENT ts2
        WHERE ts.id = ts2.SCHOOL_ID
          AND ts2.id = #{studentId}
    </select>


    <select id="leaveEnterSchoolStatisticsList" resultType="com.joinus.dao.po.LeaveEnterSchoolListResult">
        select * from (select ts.STUDENT_IMG  "imageUrl",
        ts.STUDENT_NAME "studentName",
        tg.GRADE_NAME   "gradeName",
        tc.CLASS_NAME   "className",
        tcsr.DIRECTION  "status",
        tcsr.CARD_TIME  "cardTime"
        from t_card_signin_record6 tcsr,
        T_STUDENT ts,
        T_GRADE tg,
        T_CLASS tc
        where ts.ISACTIVE = 1
        and ts.CARDCODE = tcsr.CARD_NUM
        and tg.ISACTIVE = 1
        and tc.ISACTIVE = 1
        and ts.CLASS_ID = tc.ID
        and tc.GRADE_ID = tg.id
        and tcsr.DIRECTION in (0, 1)
        and ts.CLASS_ID in
        <foreach collection="classIdList" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
        order by tcsr.CARD_TIME desc
        )
        <![CDATA[ where ROWNUM<= 2 ]]>
    </select>

    <!-- 查询用户可以管理的学校 -->
    <select id="getUserSchoolList" resultType="com.joinus.dao.po.UserSchoolListPo">
		<![CDATA[
        select s.*,
               (select hc.cardtype_name
                from t_hardtype_cardtype hc
                where s.card_hardtype = hc.id)
                   as cardHardTypeName,
               (select hc.cardtype_name
                from t_hardtype_cardtype hc
                where s.card_type =hc.id)
                   as cardTypeName
        from t_school s, sys_user_school u
        where s.id = u.school_id and u.user_id = #{userId}
          and s.isactive = 1
        ]]>
	</select>

    <!-- 根据叶子结点的regionId查询该区域下的所有学校-->
    <select id="getSchoolListByRegionId" resultType="com.joinus.dao.po.UserSchoolListPo">
        <![CDATA[
			select s.*,
				(select hc.cardtype_name
                 		  from t_hardtype_cardtype hc
                 		  where s.card_hardtype = hc.id)
                 		  as cardhardtype_name,
              		   (select hc.cardtype_name
                 		  from t_hardtype_cardtype hc
                 		  where s.card_type =hc.id)
                 		  as cardtype_name
  				from t_school s
  				where s.isactive = 1
		]]>
        <if test="districtIdList != null">
            AND EXISTS
            (SELECT 1
            FROM sys_region_new r
            WHERE s.new_region_id = r.region_id
            <foreach item="itemId" collection="districtIdList" open="AND (" separator="OR" close=")">
                r.region_id = #{itemId}
            </foreach>)
        </if>
    </select>

    <select id="getToDaySimLogs" resultType="com.joinus.dao.po.SimLogPO">
        select i.CHILDECID                                     "simNum",
               s.id                                            "schoolId",
               s.school_name                                   "schoolName",
               i.ECID                                          "groupId",
               i.PRICE                                         "productName",
               i.OPRCODE                                       "operate",
               to_char(i.FEETIME, 'yyyy-mm-dd hh24:mi:ss')     "feeDate",
               to_char(i.INSERT_TIME, 'yyyy-mm-dd hh24:mi:ss') "createDate"
        from T_INTERADC i,
             T_SCHOOL s
        where i.ecid = s.groupid
          and s.isactive = 1
          and i.INSERT_TIME >= (SELECT TRUNC(MAX(i.INSERT_TIME)) FROM T_INTERADC i)
        order by i.INSERT_TIME desc
    </select>

</mapper>