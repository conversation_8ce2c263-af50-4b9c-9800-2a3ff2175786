<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.dao.mapper.TeacherMapper">

    <select id="getTeacherInfoByAccountAndPassword" resultType="com.joinus.dao.TeacherEntity">
        select *
        from T_TEACHER tt
        where tt.tel_num = #{account}
          and tt.password = #{password}
          and tt.isactive = 1
    </select>

</mapper>
