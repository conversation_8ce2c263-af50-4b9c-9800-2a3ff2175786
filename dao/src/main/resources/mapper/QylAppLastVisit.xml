<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.dao.mapper.QylAppLastVisitMapper">

    <select id="getQylAppLastVisitByStudentList" resultType="com.joinus.dao.QylAppLastVisitEntity">
        SELECT
            student_id,
            parent_id,
            maxMerge(last_login_time) as last_visit_time
        FROM qyl_app_last_login
        where student_id in
        <foreach collection="studentIdList" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
        group by student_id, parent_id
    </select>

    <select id="getQylAppActiveStudentsLast30Days" resultType="map">
        SELECT school_id                    as "SCHOOL_ID",
               COUNT(DISTINCT t.student_id) as "COUNT"
        FROM default.qyl_request_logs t
        WHERE NOT empty(trim(student_id))
          AND NOT empty(trim(school_id))
          AND school_id !='null'
          AND t.role = '1'
          AND t.created_at > today() - 30
        GROUP BY school_id
    </select>


</mapper>