<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.dao.mapper.TerminalExtrasMapper">

    <select id="getTerminalExtrasByTerminalId" resultType="com.joinus.dao.TerminalExtrasEntity" >
        select  TERMINAL_ID "terminalId",  CARD_SELLING_PRICE  "cardSellingPrice"
        from t_terminal_extras where TERMINAL_ID = #{terminalId}
    </select>

    <insert id="addTerminalExtras">
        insert into t_terminal_extras(TERMINAL_ID, CARD_SELLING_PRICE, CREATED_AT,UPDATED_AT)
        values( #{terminalId}, #{cardSellingPrice},sysdate,sysdate)
    </insert>

    <update id="updateTerminalExtras">
        update t_terminal_extras
        set updated_at = sysdate,
            CARD_SELLING_PRICE= #{cardSellingPrice}
        where TERMINAL_ID = #{terminalId}
    </update>

</mapper>