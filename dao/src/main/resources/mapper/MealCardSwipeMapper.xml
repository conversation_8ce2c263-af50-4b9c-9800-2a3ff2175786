<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.dao.mapper.MealCardSwipeMapper">
    
    <!-- 获取当天配餐刷卡学生数据 -->
    <select id="getTodayMealCardSwipeData" resultType="java.util.Map">
        SELECT 
            m.student_id,
            m.student_name,
            m.card_no,
            m.swipe_time,
            m.terminal_id,
            m.terminal_no,
            m.meal_type,
            m.status,
            m.remark,
            t.terminal_num as terminal_num,
            t.name as terminal_name
        FROM t_meal_card_swipe m
        LEFT JOIN t_terminal t ON m.terminal_id = t.id
        WHERE m.swipe_time BETWEEN #{startTime} AND #{endTime}
        AND m.status = 1
        ORDER BY m.swipe_time DESC
    </select>
    
</mapper>
