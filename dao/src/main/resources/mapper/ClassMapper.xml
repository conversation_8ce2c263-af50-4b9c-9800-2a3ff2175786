<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.dao.mapper.ClassMapper">

    <select id="getClassById" resultType="com.joinus.dao.ClassEntity">
        <![CDATA[
        select
            id,				class_name,			monitor_code,
            grade_id,			cla_name,		class_number,
            update_time, 	class_code,			monitor_code2,   class_img
        from t_class a where  a.id=#{classId}  and isactive=1
        ]]>
    </select>
    <select id="getClassBySchoolId" resultType="int">
        <![CDATA[
        select count(1) from T_CLASS cl
        left join T_SCHOOL sc on sc.ID=cl.SCHOOL_ID
        left join T_GRADE gr on gr.ID=cl.GRADE_ID
        where
            cl.SCHOOL_ID=#{schoolId} and cl.ISACTIVE=1 and cl.ID=#{classId} and cl.GRADE_ID=#{gradeId} and
            sc.ISACTIVE=1 and
            gr.ISACTIVE=1 and
            cl.ISACTIVE=1
        ]]>
    </select>


    <select id="classListByTeacher" resultType="long">
        select distinct m.ID
        from (
                 -- 班主任
                 select tc.id
                 from T_CLASS tc
                 where tc.TEACHER_ID = #{teacherId}
                   and tc.ISACTIVE = 1
                 union all
                 -- 年级长
                 select tc.id
                 from T_GRADE_TEACHER tct,
                      T_GRADE tg,
                      T_CLASS tc
                 where tct.TEACHER_ID = #{teacherId}
                   and tct.GRADE_ID = tg.ID
                   and tc.GRADE_ID = tg.ID
                   and tg.ISACTIVE = 1
                   and tc.ISACTIVE = 1
                 union all
                 -- 任课老师
                 select tc.id
                 from T_TEACHER_CLASSTEACHER_SUBJECT ttcs,
                      T_CLASS tc
                 where ttcs.CLASS_ID = tc.id
                   and ttcs.TEACHER_ID = #{teacherId}
                   and tc.ISACTIVE = 1) m
    </select>

    <select id="classInfoListByTeacher" resultType="com.joinus.dao.model.dto.ClassInfoListDTO">
        SELECT DISTINCT
            tc.ID AS "classId",
            tc.CLASS_NAME AS "className",
            tg.ID AS "gradeId",
            tg.GRADE_NAME AS "gradeName"
        FROM
            T_CLASS tc
                JOIN
            T_GRADE tg ON tc.GRADE_ID = tg.ID
        WHERE
            tg.ISACTIVE = 1
          AND tc.ISACTIVE = 1
          AND (
            -- 班主任
            tc.TEACHER_ID = #{teacherId}
                OR
                -- 年级长
            EXISTS (
                SELECT 1
                FROM T_GRADE_TEACHER tgt
                WHERE tgt.TEACHER_ID = #{teacherId}
                  AND tgt.GRADE_ID = tg.ID
            )
                OR
                -- 任课老师
            EXISTS (
                SELECT 1
                FROM T_TEACHER_CLASSTEACHER_SUBJECT ttcs
                WHERE ttcs.CLASS_ID = tc.ID
                  AND ttcs.TEACHER_ID = #{teacherId}
            )
            )
        order by tg.GRADE_NAME,tc.CLASS_NAME
    </select>

    <select id="getClassAndGradeBySchoolId" resultType="com.joinus.dao.model.dto.ClassInfoListDTO">
        select tc.id         "classId",
               tc.CLASS_NAME "className",
               tg.id         "gradeId",
               tg.GRADE_NAME "gradeName"
        from T_CLASS tc,
             T_GRADE tg
        where tc.SCHOOL_ID = #{schoolId}
          and tc.GRADE_ID = tg.id
          and tg.ISACTIVE = 1
          and tc.ISACTIVE = 1
        order by tg.GRADE_NAME,tc.CLASS_NAME
    </select>
</mapper>