<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.dao.mapper.SignInMapper">


    <select id="listCardSignInRecordHistory" resultType="com.joinus.dao.entity.SignIn">
        select t.ID,
               t.SCHOOLID as schoolId,
               t.TERMINALTYPE as terminalType,
               t.TERMINALNUM as terminalNum,
               t.CARDNUM as cardNum,
               t.CARDTIME as cardTime,
               t.DIRECTION as direction,
               t.SAVETIME as saveTime,
               t.TERMINAL_TYPE_ID as terminalTypeId,
               t.THERMOMETER as thermometer,
               t.IMAGE_URL as imageUrl,
               t.REASON_CODE as reasonCode,
               t.LATITUDE as latitude,
               t.lONGITUDE as longitude,
               t.ADDRESS as address,
               t.SIGN_TYPE as signType,
               t.SUB_TERMINAL_NUM as subTerminalNum,
               t.HEALTH_INFO as healthInfo,
               t.HEALTH_FLAG as healthFlag,
               t.STUDENT_ID as studentId
        from T_CARD_SIGNIN_RECORD_HIS t
        where t.cardtime >= #{startTime}
            and t.cardtime &lt; #{endTime}
        order by t.CARDTIME ASC
    </select>
</mapper>