<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.dao.mapper.ResourceMapper">


    <select id="listResourceByRoleId" resultType="com.joinus.dao.entity.BasicResource">
        select distinct tr.*
        from T_ROLE_PERMISSION trp,
             T_RESOURCE tr,
             t_permission tp
        where trp.ROLE_ID = #{roleId}
          and trp.PERMISSION_ID = tp.id
          and tp.RESOURCE_ID = tr.id
          and trp.PLATFORM = #{platform}
          and tr.PLATFORM = #{platform}
          and tp.PLATFORM = #{platform}
          and tr.ISACTIVE = 1
          and tp.ISACTIVE = 1
    </select>
</mapper>