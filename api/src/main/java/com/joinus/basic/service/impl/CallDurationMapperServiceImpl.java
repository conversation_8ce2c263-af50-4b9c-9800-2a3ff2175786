package com.joinus.basic.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.joinus.basic.mapper.CallDurationMapper;
import com.joinus.basic.model.entity.CallDurationEntity;
import com.joinus.basic.model.params.UpdateRaiseCallDurationParam;
import com.joinus.basic.model.params.UpdateTotalCallDurationParam;
import com.joinus.basic.model.params.UpdateUsedVideoDurationParam;
import com.joinus.basic.model.params.UpdateUsedVoiceCallDurationParam;
import com.joinus.common.BaseServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Calendar;

/**
 * <AUTHOR> anpy
 * @create 2023/9/14 17:35
 */
@Slf4j
@Service
public class CallDurationMapperServiceImpl extends BaseServiceImpl<CallDurationMapper, CallDurationEntity> {

    public CallDurationEntity getCallDurationEntity(Long studentId) {
        return lambdaQuery()
                .eq(CallDurationEntity::getStudentId, studentId)
                .one();
    }

    public void saveTotalCallDuration(UpdateTotalCallDurationParam param) {
        CallDurationEntity callDurationEntityBuilder = CallDurationEntity.builder()
                .studentId(param.getStudentId())
                .schoolId(param.getSchoolId())
                .totalCallDuration(param.getTotalCallDuration())
                .freeUsedCallCount(param.getFreeUsedCallCount())
                .createTime(Calendar.getInstance().getTime())
                .updateTime(Calendar.getInstance().getTime())
                .usedVoiceDuration(param.getUsedVoiceDuration()).build();
        save(callDurationEntityBuilder);
    }

    public void saveVideoUsedCallDuration(UpdateUsedVideoDurationParam param, Integer usedVideoDuration, Integer callCount) {
        CallDurationEntity callDurationEntityBuilder = CallDurationEntity.builder()
                .studentId(param.getStudentId())
                .schoolId(param.getSchoolId())
                .usedVideoDuration(usedVideoDuration)
                .freeUsedCallCount(callCount)
                .createTime(Calendar.getInstance().getTime())
                .updateTime(Calendar.getInstance().getTime()).build();
        save(callDurationEntityBuilder);
    }

    public void saveVoiceUsedDuration(UpdateUsedVoiceCallDurationParam param, Integer usedVoiceDuration, Integer callCount) {
        CallDurationEntity callDurationEntityBuilder = CallDurationEntity.builder()
                .studentId(param.getStudentId())
                .schoolId(param.getSchoolId())
                .usedVoiceDuration(usedVoiceDuration)
                .freeUsedCallCount(callCount)
                .createTime(Calendar.getInstance().getTime())
                .updateTime(Calendar.getInstance().getTime()).build();
        save(callDurationEntityBuilder);
    }

    public void saveRaiseTotalCallDuration(UpdateRaiseCallDurationParam param, Integer totalCallDuration) {
        CallDurationEntity callDurationEntityBuilder = CallDurationEntity.builder()
                .studentId(param.getStudentId())
                .schoolId(param.getSchoolId())
                .totalCallDuration(totalCallDuration)
                .createTime(Calendar.getInstance().getTime())
                .updateTime(Calendar.getInstance().getTime()).build();
        save(callDurationEntityBuilder);
    }

    /**
     * 更新通话时长
     *
     * @param studentId             学生ID
     * @param totalCallDuration     总可使用时长
     * @param freeUsedCallCount    总已拨打电话次数
     * @param usedVoiceDuration     电话(4G)已使用时长
     * @param usedVideoCallDuration 视频已使用时长
     */
    public void updateCallDuration(Long studentId, Integer totalCallDuration, Integer freeUsedCallCount, Integer usedVoiceDuration, Integer usedVideoCallDuration) {
        update(null, Wrappers.<CallDurationEntity>lambdaUpdate()
                .set(totalCallDuration != null, CallDurationEntity::getTotalCallDuration, totalCallDuration)
                .set(freeUsedCallCount != null, CallDurationEntity::getFreeUsedCallCount, freeUsedCallCount)
                .set(usedVoiceDuration != null, CallDurationEntity::getUsedVoiceDuration, usedVoiceDuration)
                .set(usedVideoCallDuration != null, CallDurationEntity::getUsedVideoDuration, usedVideoCallDuration)
                .set(CallDurationEntity::getUpdateTime, Calendar.getInstance().getTime())
                .eq(studentId != null, CallDurationEntity::getStudentId, studentId));
    }
}
