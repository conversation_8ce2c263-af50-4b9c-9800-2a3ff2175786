package com.joinus.thirdparty.model.param;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

@Data
public class AddInstallTerminalParam {

    private String terminalNum;


    private Long installTime;


    private List<String> feiShuUserIds;

    private List<String> remarkList;

    private String installAddress;

    private List<String> imgUrlList;

    private String simNum;

    private String installType;

    @NotBlank(message = "recordId不允许为空")
    private String recordId;



}
