package com.joinus.thirdparty.model.result;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR> anpy
 * @create 2023/9/2 10:46
 */
@NoArgsConstructor
@Data
public class BaiduFeatureResult {

    private String code;
    private String msg;
    private List<DataDTO> data;
    private List<RowsDTO> rows;

    @NoArgsConstructor
    @Data
    public static class DataDTO {
        private String faceFeature;
        private String userId;
        private String imageUrl;
        private String groupId;
    }

    @NoArgsConstructor
    @Data
    public static class RowsDTO {
        private String faceFeature;
        private String userId;
        private String imageUrl;
        private String groupId;
    }
}
