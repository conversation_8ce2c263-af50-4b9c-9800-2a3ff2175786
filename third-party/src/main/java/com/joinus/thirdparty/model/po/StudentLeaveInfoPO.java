package com.joinus.thirdparty.model.po;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> mawh
 * @create 2024-4-26 15:27:55
 */
@Data
public class StudentLeaveInfoPO {

    private Long id;

    private Long studentId;

    private String studentName;

    private Long gradeId;

    private String gradeName;

    private String classId;

    private String className;

    // 请假人
    private String leaveName;

    private String startDate;

    private String startTime;

    private String endDate;

    private String endTime;

    // 理由
    private String reason;

    //审批记录
    private List<String> approvalRecords = new ArrayList<>();

    //审批状态
    private Integer approvalResult;

}
