package com.joinus.thirdparty.model.enums;


import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum TerminalOprCodeEnum {

    OPEN("04", "注销"),
    CANCLE("03", "开通");

    @EnumValue
    private final String code;

    private final String label;
    public static String getLabelByCode(String code) {
        for (TerminalOprCodeEnum value : TerminalOprCodeEnum.values()) {
            if (value.code.equals(code)) {
                return value.label;
            }
        }
        return null;
    }
}
