package com.joinus.thirdparty.model.param;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class FeishuCardSettlementParam implements Serializable {

    private Long schoolId;

    /*
     * 结算单价
     */
    private BigDecimal price;

    /*
     * 结算卡片数量
     */
    private Integer count;

    /*
     * 结算方式 SettlementMethodEnum
     */
    private String settlementMethod;

    /*
     * 卡品销账工单号
     */
    private String settlementNumFromFeishu;

}
