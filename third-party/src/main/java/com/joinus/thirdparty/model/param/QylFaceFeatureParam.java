package com.joinus.thirdparty.model.param;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 青于蓝人脸识别接口参数
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QylFaceFeatureParam implements Serializable {

    private String photoUrl;
    private List<String> sFaceFeatureList;

}
