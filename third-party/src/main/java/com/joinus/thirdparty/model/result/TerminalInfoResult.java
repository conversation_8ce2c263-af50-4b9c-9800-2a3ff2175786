package com.joinus.thirdparty.model.result;

import com.joinus.dao.po.TerminalInfoPO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR> mawh
 * @create 2024-9-18 13:50:05
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TerminalInfoResult {
    private List<TerminalInfoPO> records;
    private Long total;
    private Long size;
    private Long pages;
}
