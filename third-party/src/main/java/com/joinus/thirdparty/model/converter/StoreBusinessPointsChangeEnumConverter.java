package com.joinus.thirdparty.model.converter;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.converters.ReadConverterContext;
import com.alibaba.excel.converters.WriteConverterContext;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.joinus.thirdparty.model.enums.StoreBusinessPointsChangeEnum;

public class StoreBusinessPointsChangeEnumConverter implements Converter<StoreBusinessPointsChangeEnum> {


    @Override
    public Class<?> supportJavaTypeKey() {
        return StoreBusinessPointsChangeEnum.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    /**
     * 这里读的时候会调用
     *
     * @param context
     * @return
     */
    @Override
    public StoreBusinessPointsChangeEnum convertToJavaData(ReadConverterContext<?> context) {
        return StoreBusinessPointsChangeEnum.of(context.getReadCellData().getStringValue());
    }

    /**
     * 这里是写的时候会调用
     *
     * @return
     */
    @Override
    public WriteCellData<String> convertToExcelData(WriteConverterContext<StoreBusinessPointsChangeEnum> context) {
        return new WriteCellData<>(context.getValue().getDesc());
    }
}