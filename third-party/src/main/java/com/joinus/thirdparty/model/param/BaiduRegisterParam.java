package com.joinus.thirdparty.model.param;

import com.joinus.thirdparty.model.enums.BaiduRegisterImageTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * 百度人脸识别注册人脸参数
 *
 * <AUTHOR> anpy
 * @create 2023/8/21 13:44
 */

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class BaiduRegisterParam {
    private MultipartFile imageFile;
    private String imageUrl;
    @ApiModelProperty(value = "图片类型")
    private BaiduRegisterImageTypeEnum imageType;
    @NotNull(message = "用户id不能为空")
    @ApiModelProperty(value = "用户id")
    private Long userId;
    @NotEmpty(message = "用户组id不能为空")
    @ApiModelProperty(value = "用户组id(默认milk)")
    private String groupId;

}
