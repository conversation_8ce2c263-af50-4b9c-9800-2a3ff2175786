package com.joinus.thirdparty.model.po;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;

/**
 * <AUTHOR> anpy
 * @create 2023/8/23 15:12
 */
@Data
public class StudentCardInfoPO {

    @JSONField(name = "Barcode")
    private Long Barcode;
    @JSONField(name = "Name")
    private String Name;
    @JSONField(name = "Tag")
    private String Tag;
    private String gradeName;
    private String className;

    private Long studentId;

    private Long cardRecordId;

    private Integer direction;

    private String cardTime;

    private String cardNum;

}
