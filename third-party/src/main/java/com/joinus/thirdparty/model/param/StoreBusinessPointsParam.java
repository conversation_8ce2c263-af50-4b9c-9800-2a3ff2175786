package com.joinus.thirdparty.model.param;


import com.joinus.thirdparty.model.enums.StoreBusinessPointsChangeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class StoreBusinessPointsParam implements Serializable {

    @NotNull(message = "修改类型不能为空")
    private StoreBusinessPointsChangeEnum changeType;

    @NotNull(message = "发放数量不能为空")
    private Integer num;

    @NotEmpty(message = "备注说明不能为空")
    private String remark;
}
