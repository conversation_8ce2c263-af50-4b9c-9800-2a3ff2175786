package com.joinus.thirdparty.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * T_SCHOOL 表 BUSINESS_FLAG  业务标识 1：新增 2：正常（默认值） 3：待定 4：终止 5:虚假
 * <AUTHOR>
 */

@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum BusinessFlagEnum {

    ADD(1, "新增"),
    NORMAL(2,"正常"),
    PENDING(3,"待定"),
    STOP(4, "终止"),
    FLASE(5, "虚假"),
    OTHER(6,"体制外");

    private int code;
    private String desc;

    /**
     * 获取code的desc
     * @param code
     * @return
     */
    public static String getDescByCode (int code){

        for (BusinessFlagEnum businessFlagEnum : BusinessFlagEnum.values()){
            if (businessFlagEnum.getCode() == code){
                return businessFlagEnum.getDesc();
            }
        }
        return "未知枚举项";
    }
    /**
     * 根据desc获取code
     */
    public static int getCodeByDesc(String desc) {

        for (BusinessFlagEnum businessFlagEnum : BusinessFlagEnum.values()) {
            if (businessFlagEnum.getDesc().equals(desc)) {
                return businessFlagEnum.getCode();
            }
        }
        return -1;

    }
}
