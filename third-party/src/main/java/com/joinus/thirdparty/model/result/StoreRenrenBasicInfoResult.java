package com.joinus.thirdparty.model.result;


import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.joinus.thirdparty.model.param.StoreBusinessPointsParam;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.lang.reflect.Array;
import java.math.BigDecimal;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class StoreRenrenBasicInfoResult implements Serializable {

    private Long memberId;

    private Integer businessPoints;

    private BigDecimal balance;

    private String nickname;

    private String name;

    private String mobile;

    private Integer credit;

    private int isDeleted;

    private String isBindMobile;

    private int isBlack;

    private String isBlackName;

    private Array[] groups;
    private Array[] groupsMap;



    public static StoreRenrenBasicInfoResult of(JSONObject jsonObject) {
        if (null == jsonObject) {
            return new StoreRenrenBasicInfoResult();
        }
        StoreRenrenBasicInfoResult result = JSONUtil.toBean(jsonObject,
                StoreRenrenBasicInfoResult.class);
        result.setMemberId(jsonObject.getLong("id"));
        result.setBusinessPoints(jsonObject.getInt("credit"));
        result.setName(jsonObject.getStr("realname"));
        return result;
    }

}
