package com.joinus.thirdparty.model.result;

import com.joinus.thirdparty.model.po.StudentInfoPO;
import com.joinus.thirdparty.model.po.ZhongLiangStudentInfoPO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR> anpy
 * @create 2023/8/23 15:24
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SyncZhongLiangStudentInfoResult {

    private List<ZhongLiangStudentInfoPO> records;
    private Long total;
    private Long size;
    private Long current;
    private Long pages;
}
