package com.joinus.thirdparty.model.param;

import com.joinus.common.model.param.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value="测试", description="测试")
public class YiDuStudentListParam extends PageParam {

    @ApiModelProperty("学校id")
    private Long schoolId;
    private Long gradeId;
    private Long classId;
    private String studentName;

}
