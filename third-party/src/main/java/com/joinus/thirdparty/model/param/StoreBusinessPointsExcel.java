package com.joinus.thirdparty.model.param;


import com.alibaba.excel.annotation.ExcelProperty;
import com.joinus.thirdparty.model.converter.StoreBusinessPointsChangeEnumConverter;
import com.joinus.thirdparty.model.enums.StoreBusinessPointsChangeEnum;
import lombok.Data;

import java.io.Serializable;

@Data
public class StoreBusinessPointsExcel implements Serializable {

    @ExcelProperty(value = "手机号码")
    private String mobile;

    @ExcelProperty(value = "操作数量")
    private Integer num;

    @ExcelProperty(value = "操作类型", converter = StoreBusinessPointsChangeEnumConverter.class)
    private StoreBusinessPointsChangeEnum changeType;

    @ExcelProperty(value = "备注")
    private String remark;

    @ExcelProperty(value = "结果")
    private String result;

    @ExcelProperty(value = "错误原因")
    private String errorReason;
}
