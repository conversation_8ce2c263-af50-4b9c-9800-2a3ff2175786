package com.joinus.thirdparty.model.param;

import com.joinus.thirdparty.model.enums.BaiduRegisterImageTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 百度人脸识别注册人脸参数
 *
 * <AUTHOR> anpy
 * @create 2023/8/21 13:44
 */

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class AddBaiduFaceFeatureParam {
    @ApiModelProperty(value = "图片类型")
    private BaiduRegisterImageTypeEnum imageTypeEnum;
    @ApiModelProperty(value = "用户id")
    private Long userId;
    @ApiModelProperty(value = "用户组id(默认milk)")
    private String groupId;
    private String imageData;

}
