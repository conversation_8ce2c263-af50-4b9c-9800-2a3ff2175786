package com.joinus.thirdparty.model.enums;


import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum SimPriceEnum {

    H("H", "考勤机"),
    I("I", "电话机");

    @EnumValue
    private final String code;

    private final String label;
    // 静态方法通过code获取label
    public static String getLabelByCode(String code) {
        for (SimPriceEnum value : SimPriceEnum.values()) {
            if (value.code.equals(code)) {
                return value.label;
            }
        }
        return null;
    }
}
