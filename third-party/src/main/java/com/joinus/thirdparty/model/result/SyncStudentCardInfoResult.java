package com.joinus.thirdparty.model.result;

import com.joinus.thirdparty.model.po.StudentCardInfoPO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR> anpy
 * @create 2023/8/23 15:24
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SyncStudentCardInfoResult {

    private List<StudentCardInfoPO> records;
    private Long total;
    private Long size;
    private Long current;
    private Long pages;
}
