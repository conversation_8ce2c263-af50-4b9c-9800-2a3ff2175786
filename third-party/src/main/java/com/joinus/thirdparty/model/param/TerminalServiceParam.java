package com.joinus.thirdparty.model.param;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

@Data
public class TerminalServiceParam {

    private String terminalNum;

    private List<String> feiShuUserIds;

    private List<String> remarkList;

    private String installAddress;

    private List<String> imgUrlList;

    private String faultProgram;

    private List<String> faultPhenomenonList;

    private List<String> treatmentMeasureList;

    private String simNum;

    private Long orderTime;

    private Long serviceTime;

    @NotBlank(message = "recordId不允许为空")
    private String recordId;




}
