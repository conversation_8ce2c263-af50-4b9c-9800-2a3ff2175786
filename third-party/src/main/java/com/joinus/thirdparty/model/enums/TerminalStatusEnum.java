package com.joinus.thirdparty.model.enums;


import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum TerminalStatusEnum {

    NOT_IN_USER(0, "未使用"),
    IN_USER(1, "在使用"),
    LOST(2, "已丢失"),
    FOR_TEST(3, "测试用");

    @EnumValue
    private final int code;

    private final String label;
    public static String getLabelByCode(int code) {
        for (TerminalStatusEnum value : TerminalStatusEnum.values()) {
            if (value.code== code) {
                return value.label;
            }
        }
        return null;
    }
}
