package com.joinus.thirdparty.model.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.joinus.dao.enums.SwaggerDisplayEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 百度人脸识别图片类型
 *
 * <AUTHOR> anpy
 * @create 2023/8/21 16:04
 */
@Getter
@AllArgsConstructor
public enum BaiduRegisterImageTypeEnum implements SwaggerDisplayEnum {
    /**
     * 图片的base64值，base64编码后的图片数据，编码后的图片大小不超过2M
     */
    BASE64(0, "图片的base64值，base64编码后的图片数据，编码后的图片大小不超过2M"),
    /**
     * 图片的 URL地址( 可能由于网络等原因导致下载图片时间过长)
     */
    URL(1, "图片的 URL地址( 可能由于网络等原因导致下载图片时间过长)"),
    /**
     * 人脸图片的唯一标识，调用人脸检测接口时，会为每个人脸图片赋予一个唯一的FACE_TOKEN，同一张图片多次检测得到的FACE_TOKEN是同一个
     */
    FACE_TOKEN(2,"人脸图片的唯一标识，调用人脸检测接口时，会为每个人脸图片赋予一个唯一的FACE_TOKEN，同一张图片多次检测得到的FACE_TOKEN是同一个");
    @EnumValue
    private final int type;
    private final String typeName;

    @Override
    public String description() {
        return type + "-" + typeName + "-使用值:" + name();
    }
}
