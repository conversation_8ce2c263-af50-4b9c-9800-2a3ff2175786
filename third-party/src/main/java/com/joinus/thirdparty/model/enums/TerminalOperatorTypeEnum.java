package com.joinus.thirdparty.model.enums;
/**
 * TerminalOperatorType类型
 * <AUTHOR>
 *
 */
public enum TerminalOperatorTypeEnum {
	
	/** 安装1 */
	TYPE_INSTALL((short)1, "安装"),
	/** 卸载2 */
	TYPE_UNINSTALL((short)2, "卸载"),
	/** 有效3 */
	TYPE_EFFECTIVE((short)3, "有效"),
	/** 失效4 */
	TYPE_INVALID((short)4, "失效"),
	/** 初始化5 */
	TYPE_INIT((short)5, "初始化"),
	/** 升级6 */
	TYPE_UPGRADE((short)6, "升级"),
	/** 重启7 */
	TYPE_RESTART((short)7, "重启"),
	/** 编辑9 */
	TYPE_EDIT((short)9, "编辑"),
	/** 新增10 */
	TYPE_ADD((short)10, "新增"),

	/** 新增11 */
	TYPE_OA_OPEN((short)11, "开通OA"),

    TYPE_MOVE((short)12, "移机");
	
	/** code */
    private short code;

    /** 显示标签 */
    private String label;

    /**
     * 构造器，必须私有
     * @param code
     */
    TerminalOperatorTypeEnum(short code, String label){
        this.code = code;
        this.label = label;
    }

    /**
     * 获取code的文本
     * @param code
     * @return
     */
    public static String getLabelByCode (short code){

        for (TerminalOperatorTypeEnum enuma : TerminalOperatorTypeEnum.values()){
            if (enuma.getCode() == code){
                return enuma.getLabel();
            }
        }
        return "未知枚举项";
    }
    /**
     * 根据文本获取code
     */
    public static short getCodeByLabel(String label){

        for(TerminalOperatorTypeEnum enuma : TerminalOperatorTypeEnum.values()){
            if(enuma.getLabel().equals(label)){
                return enuma.getCode();
            }
        }
        return -1;

    }

    /**
     * 根据code获取枚举
     */
    public static TerminalOperatorTypeEnum getTerminalOperatorTypeByCode(short code){

        for(TerminalOperatorTypeEnum enuma : TerminalOperatorTypeEnum.values()){
            if(enuma.getCode() == code){
                return enuma;
            }
        }
        return null;

    }

    public String toString(){
        return label;
    }

    public String getLabel() {
        return label;
    }

    public short getShort(){
        return this.code;
    }

    public short getCode(){
        return this.code;
    }
}
