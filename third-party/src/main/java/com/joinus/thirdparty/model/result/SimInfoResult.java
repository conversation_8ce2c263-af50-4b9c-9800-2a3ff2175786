package com.joinus.thirdparty.model.result;

import com.joinus.dao.po.SimInfoPO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR> mawh
 * @create 2024-12-18 14:33:29
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SimInfoResult {
    private List<SimInfoPO> records;
    private Long total;
    private Long size;
    private Long pages;
}
