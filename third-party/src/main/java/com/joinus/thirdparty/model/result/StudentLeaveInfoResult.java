package com.joinus.thirdparty.model.result;

import com.joinus.thirdparty.model.po.StudentLeaveInfoPO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR> mawh
 * @create 2024-4-22 09:50:24
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class StudentLeaveInfoResult  {
    private List<StudentLeaveInfoPO> records;
    private Long total;
    private Long size;
    private Long pages;
}
