package com.joinus.thirdparty.model.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
public class SimParam {
    @ApiModelProperty(value = "每页数量", required = true)
    @NotNull(message = "每页数量不能为空")
    private Integer size;
    @ApiModelProperty(value = "当前页", required = true)
    @NotNull(message = "当前页不能为空")
    private Integer current;
    @ApiModelProperty(value = "排序", required = true)
    private String sort;

}
