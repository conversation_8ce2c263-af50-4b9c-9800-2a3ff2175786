package com.joinus.thirdparty.model.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * 自动售货机——人脸特征码全量参数
 *
 * <AUTHOR> anpy
 * @create 2023/8/21 9:40
 */
@Builder
@Data
public class FaceFeatureFullPageParam {

    @ApiModelProperty(value = "设备号")
    @NotEmpty(message = "设备编号不能为空！")
    private String deviceNum;

}
