package com.joinus.thirdparty.model.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR> anpy
 * @create 2023/8/21 9:59
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OrderParam {

    @ApiModelProperty(value = "设备编号",required = true)
    @NotEmpty(message = "设备编号不能为空！")
    private String deviceNum;
    @ApiModelProperty(value = "学生id",required = true)
    @NotNull(message = "用户id不能为空！")
    private Long studentId;
    @ApiModelProperty(value = "单位 分",required = true)
    @NotNull(message = "金额不能为空！")
    private Double amount;
    private String deviceType;
    private String outOrderId;
    @ApiModelProperty(value = "商品详情")
    private List<GoodsInfo> goodsInfo;

    @Data
    @Builder
    public static class GoodsInfo{
        @ApiModelProperty(value = "商品名称")
        private String goodsName;
        @ApiModelProperty(value = "商品价格，单位分")
        private Double price;
        @ApiModelProperty(value = "商品数量")
        private Integer count;
    }

}
