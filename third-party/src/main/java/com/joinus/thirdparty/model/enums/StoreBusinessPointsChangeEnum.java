package com.joinus.thirdparty.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 百度人脸识别图片类型
 *
 * <AUTHOR> anpy
 * @create 2023/8/21 16:04
 */
@Getter
@AllArgsConstructor
public enum StoreBusinessPointsChangeEnum {

    PLUS(1, "增加"),
    MINUS(2, "扣减");

    private final int type;
    private final String desc;


    /**
     * [desc]
     * @return com.joinus.thirdparty.model.enums.StoreBusinessPointsChangeEnum
     * @description 创建了String to enum 的静态方法后，hutool默认反序列化会调用此方法，所以兼容name和desc转换
     * <AUTHOR>
     * @date 2024/7/23 10:48
     */
    public static StoreBusinessPointsChangeEnum of(String string) {
        for (StoreBusinessPointsChangeEnum type : StoreBusinessPointsChangeEnum.values()) {
            if (type.getDesc().equals(string) || type.name().equals(string)) {
                return type;
            }
        }
        return null;
    }
}
