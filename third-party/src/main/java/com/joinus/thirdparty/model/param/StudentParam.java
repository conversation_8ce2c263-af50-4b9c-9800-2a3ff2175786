package com.joinus.thirdparty.model.param;

import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.joinus.thirdparty.model.po.ParentPO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Range;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StudentParam {

    @ApiModelProperty("学生id,更新学生时必传")
    private Long id;
    @ApiModelProperty(value = "学校id", required = true)
    private Long schoolId;
    @ApiModelProperty(value = "年级名称", required = true)
    private Long gradeId;
    @ApiModelProperty(value = "班级id", required = true)
    private Long classId;
    @ApiModelProperty("卡号")
    @JsonProperty("cardcode")
    private String cardCode;
    @ApiModelProperty("状元卡手机号")
    private String telNum;
    @ApiModelProperty("学生名字")
    private String studentName;//学生名字
    @ApiModelProperty("一卡通标识")
    private String studentCode;
    @ApiModelProperty("学生编号")
    private String makeCard;
    @ApiModelProperty("学生身份证号")
    private String identity;
    @ApiModelProperty("是否更新照片")
    private boolean isUpdateImg;
    @ApiModelProperty("性别:1、男 0、女")
    @Range(min = 0,max = 1,message = "sex参数错误")
    private Integer sex;
    @ApiModelProperty("民族")
    @Range(min = 0)
    private Long nationId;//民族id
    @ApiModelProperty("是否挂失：0、解挂 1、挂失")
    @Range(min = 0,max = 1,message = "isGuaShi参数错误")
    @JsonProperty("isGuashi")
    private Integer isGuaShi;
    @ApiModelProperty("是否坐校车：0、无效 1、有效")
    @Range(min = 0,max = 1,message = "isBus参数错误")
    @JsonProperty("isbus")
    private Integer isBus;
    @ApiModelProperty("是否住宿生：0、无效 1、有效 2、待定")
    @Range(min = 0,max = 2,message = "isBus参数错误")
    @JsonProperty("isdorm")
    private Integer isDorm;
    @ApiModelProperty("地址")
    private String address;
    @ApiModelProperty("学生照片")
    private String studentImg;
    @ApiModelProperty("中午允许离校:0、无效 1、有效")
    @Range(min = 0,max = 1,message = "isTime1参数错误")
    @JsonProperty("istime1")
    private Integer isTime1;
    @ApiModelProperty("下午允许离校:0、无效 1、有效")
    @Range(min = 0,max = 1,message = "isTime2参数错误")
    @JsonProperty("istime2")
    private Integer isTime2;
    @ApiModelProperty("晚上允许离校:0、无效 1、有效")
    @Range(min = 0,max = 1,message = "isTime3参数错误")
    @JsonProperty("istime3")
    private Integer isTime3;
    @ApiModelProperty("IMEI号")
    private String imei;
    @ApiModelProperty("对接同步学生代码")
    private String studentDockCode;
    @ApiModelProperty("人脸识别特殊码")
    private String faceFeature;
    @ApiModelProperty("校车人脸识别特殊码")
    private String busFaceFeature;
    @ApiModelProperty("生日")
    private String birth;
    @ApiModelProperty("是否删除，0删除1存在")
    private Integer isActive;
    @ApiModelProperty("学生认证状态 0未认证 1已认证")
    private Integer studentAuth;
    @ApiModelProperty("护照名称")
    private String passportName;
    @ApiModelProperty("护照编号")
    @JsonProperty("passportNo")
    private String passportNum;
    @DateTimeFormat
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime; //学生信息修改时间
    @ApiModelProperty("家长")
    private ParentPO[] parents;
    @ApiModelProperty("学生图片审核状态  0:未审核 1:审核通过 2未通过（已通知家长）3未通过（家长已修改）")
    private Integer auditStatus;
    @ApiModelProperty("是否屏蔽生活服务(设备端禁用生活服务、不发放优惠券)0:否,1:是(云校柜)")
    private Integer disableLifeSvc;
    @ApiModelProperty("社保局非接串号，后8位是16进制卡号。")
    private String socialSecurityCard;
    @ApiModelProperty("学生社保卡号")
    private String socialSecurityNum;
    @ApiModelProperty("是否已签署一本书协议 0:未签署,1:已签署")
    private Integer oneBookAgreement;
    @ApiModelProperty("农历生日")
    private String chineseBirthday;
    @ApiModelProperty("生日类型 0.公历 1.农历")
    private String birthdayType;
    @ApiModelProperty("0:不允许打电话,1:允许打电话")
    private String allowCall;
    @ApiModelProperty("特征码更新时间")
    private String featureUpdateTime;
    @ApiModelProperty("学生照片审核成功个数")
    private String auditSuccessNumber;
    @ApiModelProperty("学生照片审核不通过原因")
    private String auditErrorMsg;
    @ApiModelProperty("一卡通卡状态，0：正常，1挂失")
    private Integer yktCardStatus;
    @ApiModelProperty("一卡通余额")
    private Integer balance;
    @ApiModelProperty("升级标示,0升级中 1 升级后")
    private String upFlag;
    @ApiModelProperty("二次制卡提交时间")
    @DateTimeFormat
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date commitTime;
    @ApiModelProperty("二次制卡提交人")
    private String commiTer;
    @ApiModelProperty("配送公司")
    private String company;
    @ApiModelProperty("app客户端增加学生时的唯一标识")
    private String UUID;
    @ApiModelProperty("卡数")
    private String codeNum;
    @ApiModelProperty("是否配餐")
    private Integer mealState;//0是 1否
    @ApiModelProperty("配餐家长手机号")
    private String mealParentPhoneNum;

    @ApiModelProperty("年级名称-批量")
    private String gradeName;
    @ApiModelProperty("班级名称-批量")
    private String className;
    @ApiModelProperty("民族名称")
    @JsonProperty("nation")
    private String nationName;
    @ApiModelProperty("性别名称")
    @JsonProperty("sexName")
    private String sexName;
    @ApiModelProperty("学生类型名称")
    @JsonProperty("isDormName")
    private String isDormName;
    @ApiModelProperty("是否乘坐校车")
    @JsonProperty("isBusName")
    private String isBusName;

    @ApiModelProperty("信息合并-删除的学生id")
    private final Long[] delStudentIds = null;
    @ApiModelProperty("信息合并-删除的学生姓名")
    private final String[] oldStudentNames = null;
    @ApiModelProperty("信息合并-删除的学生卡号")
    private final String[] oldCards = null;
}
