package com.joinus.thirdparty.model.result;

import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> anpy
 * @create 2023/8/21 9:37
 */
@Builder
@Data
public class SchoolVendingFaceFeatureResult {
    private long current;
    private long pages;
    private List<Record> records;
    private long size;
    private long total;

    public class Record {
        private String faceFeature;
        private Long studentId;
        private String studentName;

    }

}
