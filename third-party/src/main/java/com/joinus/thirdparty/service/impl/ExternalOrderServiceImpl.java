package com.joinus.thirdparty.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.joinus.common.BaseServiceImpl;
import com.joinus.dao.ExternalOrderEntity;
import com.joinus.thirdparty.mapper.ExternalOrderMapper;
import com.joinus.thirdparty.service.ExternalOrderService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> anpy
 * @create 2023/8/25 14:10
 */
@Service
public class ExternalOrderServiceImpl extends BaseServiceImpl<ExternalOrderMapper, ExternalOrderEntity> implements ExternalOrderService {

    @Override
    public void saveOrderInfo(ExternalOrderEntity externalOrderEntity) {
        save(externalOrderEntity);
    }

    @Override
    public ExternalOrderEntity loadOrderByIjxOrderId(String ijxOrderId) {
        return lambdaQuery()
                .eq(ExternalOrderEntity::getOrderId, ijxOrderId)
                .one();
    }

    @Override
    public void updateOrderByIjxOrderId(String imgUrl, String ijxOrderId) {
        update(null, Wrappers.<ExternalOrderEntity>lambdaUpdate()
                .eq(ExternalOrderEntity::getOrderId, ijxOrderId)
                .set(ExternalOrderEntity::getSnapShotUrl, imgUrl));
    }
}
