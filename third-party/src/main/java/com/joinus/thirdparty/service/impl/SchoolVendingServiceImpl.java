package com.joinus.thirdparty.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.joinus.common.BaseServiceImpl;
import com.joinus.common.model.enmus.IPayDeviceTypeEnum;
import com.joinus.common.model.enmus.OrderBusinessTypeEnum;
import com.joinus.common.model.enmus.OrderOperationTypeEnum;
import com.joinus.common.model.enmus.OrderPlatformTypeEnum;
import com.joinus.common.model.enums.InterfaceResultEnum;
import com.joinus.common.model.param.OrderRefundParam;
import com.joinus.common.model.enums.StudentActiveTypeEnum;
import com.joinus.common.model.param.PageParam;
import com.joinus.common.model.response.CommonResponse;
import com.joinus.common.model.response.IPayResult;
import com.joinus.common.utils.CommonUtils;
import com.joinus.common.utils.HaSerial;
import com.joinus.common.utils.LocalHttpUtil;
import com.joinus.common.utils.QiNiuUtils;
import com.joinus.dao.ExternalOrderEntity;
import com.joinus.dao.ExternalTerminalEntity;
import com.joinus.dao.StudentEntity;
import com.joinus.thirdparty.mapper.ExternalTerminalMapper;
import com.joinus.thirdparty.mapper.StudentMapper;
import com.joinus.thirdparty.model.param.*;
import com.joinus.thirdparty.model.po.StudentInfoPO;
import com.joinus.thirdparty.model.po.ZhongLiangStudentInfoPO;
import com.joinus.thirdparty.model.result.SyncStudentInfoResult;
import com.joinus.thirdparty.model.result.SyncZhongLiangStudentInfoResult;
import com.joinus.thirdparty.service.ExternalOrderService;
import com.joinus.thirdparty.service.SchoolVendingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.UUID;

/**
 * <AUTHOR> anpy
 * @create 2023/8/23 14:18
 */
@Slf4j
@Service
public class SchoolVendingServiceImpl extends BaseServiceImpl<ExternalTerminalMapper, ExternalTerminalEntity> implements SchoolVendingService {

    @Value("${ipay_trade_pay_url}")
    private String iPayTradePayUrl;
    @Value("${ipay-recharge-url}")
    private String iPayRechargeUrl;
    @Value("${ipay_client_id}")
    private String iPayClientId;
    @Value("${ipay_secret}")
    private String iPaySecret;

    @Resource
    private ExternalTerminalMapper externalTerminalMapper;
    @Resource
    private StudentMapper studentMapper;
    @Resource
    private ExternalOrderService externalOrderService;

    /**
     * 全量同步学生百度特征码
     */
    @Override
    public SyncStudentInfoResult faceFeatureFullPage(PageParam param, FaceFeatureFullPageParam messageBoardDetailListParam) {
        IPage<StudentInfoPO> pageParameters = getStudentInfoIpage(param);
        IPage<StudentInfoPO> studentInfoResultIpage = externalTerminalMapper.loadStudentByDeviceNum(pageParameters, messageBoardDetailListParam.getDeviceNum());
        return SyncStudentInfoResult.builder()
                .records(studentInfoResultIpage.getRecords())
                .pages(studentInfoResultIpage.getPages())
                .current(studentInfoResultIpage.getCurrent())
                .total(studentInfoResultIpage.getTotal())
                .size(studentInfoResultIpage.getSize())
                .build();
    }

    @Override
    public SyncStudentInfoResult faceFeatureIncrementPage(PageParam param, FaceFeatureIncrementPageParam faceFeatureIncrementPageParam) {
        IPage<StudentInfoPO> pageParameters = getStudentInfoIpage(param);
        IPage<StudentInfoPO> studentInfoResultIpage = externalTerminalMapper.loadStudentByDeviceNumIncrement(pageParameters, faceFeatureIncrementPageParam.getDeviceNum(), faceFeatureIncrementPageParam.getUpdateTime());
        return SyncStudentInfoResult.builder()
                .records(studentInfoResultIpage.getRecords())
                .pages(studentInfoResultIpage.getPages())
                .current(studentInfoResultIpage.getCurrent())
                .total(studentInfoResultIpage.getTotal())
                .size(studentInfoResultIpage.getSize())
                .build();
    }

    @Override
    public SyncZhongLiangStudentInfoResult zhongLiangFaceFeatureFullPage(PageParam param, FaceFeatureFullPageParam messageBoardDetailListParam) {
        IPage<ZhongLiangStudentInfoPO> pageParameters = getZhongLiangStudentInfoIpage(param);
        IPage<ZhongLiangStudentInfoPO> studentInfoResultIpage = externalTerminalMapper.loadZhongLiangStudentByDeviceNum(pageParameters, messageBoardDetailListParam.getDeviceNum());
        return SyncZhongLiangStudentInfoResult.builder()
                .records(studentInfoResultIpage.getRecords())
                .pages(studentInfoResultIpage.getPages())
                .current(studentInfoResultIpage.getCurrent())
                .total(studentInfoResultIpage.getTotal())
                .size(studentInfoResultIpage.getSize())
                .build();
    }

    @Override
    public SyncZhongLiangStudentInfoResult zhongLiangFaceFeatureIncrementPage(PageParam param, FaceFeatureIncrementPageParam faceFeatureIncrementPageParam) {
        IPage<ZhongLiangStudentInfoPO> pageParameters = getZhongLiangStudentInfoIpage(param);
        IPage<ZhongLiangStudentInfoPO> studentInfoResultIpage = externalTerminalMapper.loadZhongLiangStudentByDeviceNumIncrement(pageParameters, faceFeatureIncrementPageParam.getDeviceNum(), faceFeatureIncrementPageParam.getUpdateTime());
        return SyncZhongLiangStudentInfoResult.builder()
                .records(studentInfoResultIpage.getRecords())
                .pages(studentInfoResultIpage.getPages())
                .current(studentInfoResultIpage.getCurrent())
                .total(studentInfoResultIpage.getTotal())
                .size(studentInfoResultIpage.getSize())
                .build();
    }

    /**
     * 分页参数
     */
    private IPage<StudentInfoPO> getStudentInfoIpage(PageParam param) {
        IPage<StudentInfoPO> pageParameters = new Page<>();
        pageParameters.setCurrent(param.getCurrent());
        pageParameters.setSize(param.getSize());
        return pageParameters;
    }

    private IPage<ZhongLiangStudentInfoPO> getZhongLiangStudentInfoIpage(PageParam param) {
        IPage<ZhongLiangStudentInfoPO> pageParameters = new Page<>();
        pageParameters.setCurrent(param.getCurrent());
        pageParameters.setSize(param.getSize());
        return pageParameters;
    }

    @Override
    public IPayResult order(OrderParam orderParam) {
        ExternalTerminalEntity terminalEntity = lambdaQuery()
                .eq(ExternalTerminalEntity::getDeviceNumber, orderParam.getDeviceNum())
                .eq(ExternalTerminalEntity::getIsactive, StudentActiveTypeEnum.NORMAL.getIsActive())
                .one();
        CommonResponse.ERROR.assertNotNull(terminalEntity, CommonResponse.TERMINAL_NO_EXIST);

        LambdaQueryWrapper<StudentEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(StudentEntity::getId, orderParam.getStudentId());
        StudentEntity studentEntity = studentMapper.selectOne(lambdaQueryWrapper);
        CommonResponse.ERROR.assertNotNull(studentEntity, CommonResponse.STUDENT_NO_EXIST);
        CommonResponse.ERROR.assertEquals(terminalEntity.getSchoolId(), studentEntity.getSchoolId(), CommonResponse.STUDENT_NOT_IN_SCHOOL);
        CommonResponse.ERROR.assertNotNull(studentEntity.getIdentity(), CommonResponse.IDENTITY_IS_NULL);

        String ijxOrderId = HaSerial.getPay();
        IPayResult iPayResult = iPayTradePay(
                terminalEntity.getSchoolId(),
                String.valueOf(orderParam.getAmount() / 100.0),
                studentEntity.getIdentity(),
                ijxOrderId,
                iPayTradePayUrl,
                orderParam.getDeviceType(), orderParam.getDeviceNum());
        CommonResponse.ERROR.assertIsNotNull(iPayResult, "支付失败..");
        if (iPayResult.getMsg().contains(CommonResponse.NOT_SUFFICIENT_FUNDS.getMsg())) {
            CommonResponse.assertError(CommonResponse.NOT_SUFFICIENT_FUNDS);
        }
        if (iPayResult.getMsg().contains(CommonResponse.ACCOUNT_EXCEPTION.getMsg())) {
            CommonResponse.assertError(CommonResponse.ACCOUNT_EXCEPTION);
        }
        CommonResponse.ERROR.assertEquals(iPayResult.getCode(), String.valueOf(InterfaceResultEnum.SUCCESS.getCode()), iPayResult.getMsg());
        //一卡通支付成功，返回ijxOrderId
        iPayResult.setIjxOrderId(ijxOrderId);

        //本次订单入库
        try {
            ExternalOrderEntity externalOrderEntity = ExternalOrderEntity.builder()
                    .orderId(ijxOrderId)
                    .businessType(OrderBusinessTypeEnum.SCHOOL_VENDING.getBusinessType())
                    .operationType(OrderOperationTypeEnum.BUY.getOperationType())
                    .productInfo(CommonUtils.objectToJson(orderParam.getGoodsInfo()))
                    .studentId(studentEntity.getId())
                    .identity(studentEntity.getIdentity())
                    .schoolId(studentEntity.getSchoolId())
                    .outOrderId(orderParam.getOutOrderId())
                    .platformType(OrderPlatformTypeEnum.XING_YUAN.getPlatformType())
                    .amount(orderParam.getAmount())
                    .createTime(Calendar.getInstance().getTime())
                    .updateTime(Calendar.getInstance().getTime())
                    .build();
            externalOrderService.saveOrderInfo(externalOrderEntity);
        } catch (JsonProcessingException e) {
            CommonResponse.assertError(e.getMessage());
        }

        return iPayResult;
    }

    /**
     * 退款操作
     *
     * @param ijxOrderId 下单订单号
     */
    @Override
    public void refundOrder(OrderRefundParam orderRefundParam) {
        ExternalOrderEntity externalOrderEntity = externalOrderService.loadOrderByIjxOrderId(orderRefundParam.getIjxOrderId());
        CommonResponse.ERROR.assertNotNull(externalOrderEntity, "未查询到该订单信息！");
        String refundOrderId = "TF" + externalOrderEntity.getOrderId();

        ExternalOrderEntity refundOrderEntity = externalOrderService.loadOrderByIjxOrderId(refundOrderId);
        CommonResponse.ERROR.assertIsNull(refundOrderEntity, "该订单已退款，请勿重复退款！");

        IPayResult iPayResult = iPayTradePay(
                externalOrderEntity.getSchoolId(),
                String.valueOf(externalOrderEntity.getAmount() / 100.0),
                externalOrderEntity.getIdentity(),
                refundOrderId,
                iPayRechargeUrl, IPayDeviceTypeEnum.SCHOOL_VENDING.getDeviceType().toString(), orderRefundParam.getDeviceNum());
        CommonResponse.ERROR.assertIsNotNull(iPayResult, "退款失败");
        CommonResponse.ERROR.assertEquals(iPayResult.getCode(), String.valueOf(InterfaceResultEnum.SUCCESS.getCode()), iPayResult.getMsg());

        externalOrderEntity.setRelatedOrderId(externalOrderEntity.getOrderId());
        externalOrderEntity.setOrderId(refundOrderId);
        externalOrderEntity.setOperationType(OrderOperationTypeEnum.REFUND.getOperationType());
        externalOrderEntity.setCreateTime(Calendar.getInstance().getTime());
        externalOrderEntity.setUpdateTime(Calendar.getInstance().getTime());

        externalOrderService.saveOrderInfo(externalOrderEntity);
    }

    /**
     * 调用iPay扣款接口
     */
    private IPayResult iPayTradePay(Long schoolId, String amount,
                                    String identity, String ijxOrderId, String url, String deviceType, String deviceNum) {
        IPayParam youKaTeIpayParam = IPayParam.builder()
                .clientId(iPayClientId)
                .timeStamp(DateUtil.formatDateTime(new Date()))
                .norceStr(UUID.randomUUID().toString())
                .schoolId(schoolId.toString())
                .identity(identity)
                .orderNo(ijxOrderId)
                .amount(amount)
                .deviceType(deviceType)
                .deviceNo(deviceNum)
                .build();
        HashMap<String, String> signMapParam = JSON.parseObject(JSON.toJSONString(youKaTeIpayParam), HashMap.class);
        signMapParam.put("clientSecret", iPaySecret);
        String iPaySign = SecureUtil.md5(CommonUtils.signContent(signMapParam)).toUpperCase();
        youKaTeIpayParam.setSign(iPaySign);
        log.info("一卡通参数：{} {}", JSONUtil.toJsonStr(youKaTeIpayParam), url);
        String response = LocalHttpUtil.post(url, youKaTeIpayParam);
        log.info("一卡通接口结果：{}", response);
        return CommonUtils.jsonToObject(response, IPayResult.class);
    }

    @Override
    public void uploadSnapshot(SnapshotParam snapshotParam) {
        ExternalOrderEntity externalOrderEntity = externalOrderService.loadOrderByIjxOrderId(snapshotParam.getIjxOrderId());
        CommonResponse.ERROR.assertNotNull(externalOrderEntity, "该订单不存在！");
        String imgUrl = QiNiuUtils.uploadFile(snapshotParam.getImageFile(), snapshotParam.getIjxOrderId());
        externalOrderService.updateOrderByIjxOrderId(imgUrl, snapshotParam.getIjxOrderId());
    }

    @Override
    public String loadSnapshot(String ijxOrderId) {
        ExternalOrderEntity externalOrderEntity = externalOrderService.loadOrderByIjxOrderId(ijxOrderId);
        CommonResponse.ERROR.assertNotNull(externalOrderEntity, "该订单不存在！");
        CommonResponse.ERROR.assertNotEmpty(externalOrderEntity.getSnapShotUrl(), "该订单未上传快照！");
        return QiNiuUtils.getPrivateImageByUrl(externalOrderEntity.getSnapShotUrl(), 50000);
    }

    @Override
    public void bindDevice(DeviceParam deviceParam) {
        ExternalTerminalEntity terminalEntity = lambdaQuery()
                .eq(ExternalTerminalEntity::getDeviceNumber, deviceParam.getDeviceNum())
                .eq(ExternalTerminalEntity::getIsactive, StudentActiveTypeEnum.NORMAL.getIsActive())
                .one();
        CommonResponse.ERROR.assertIsNull(terminalEntity, "该设备已绑定！");

        terminalEntity = new ExternalTerminalEntity();
        terminalEntity.setDeviceNumber(deviceParam.getDeviceNum());
        terminalEntity.setSchoolId(deviceParam.getSchoolId());
        terminalEntity.setType(deviceParam.getType());
        terminalEntity.setIsactive(StudentActiveTypeEnum.NORMAL.getIsActive());
        terminalEntity.setCreateTime(Calendar.getInstance().getTime());
        terminalEntity.setUpdateTime(Calendar.getInstance().getTime());
        save(terminalEntity);
    }

    @Override
    public void unBindDevice(String deviceNum) {
        ExternalTerminalEntity terminalEntity = lambdaQuery()
                .eq(ExternalTerminalEntity::getDeviceNumber, deviceNum)
                .eq(ExternalTerminalEntity::getIsactive, StudentActiveTypeEnum.NORMAL.getIsActive())
                .one();
        CommonResponse.ERROR.assertIsNotNull(terminalEntity, "该设备未绑定！");
        terminalEntity.setIsactive(StudentActiveTypeEnum.DELETE.getIsActive());
        terminalEntity.setUpdateTime(Calendar.getInstance().getTime());
        updateById(terminalEntity);
    }
}
