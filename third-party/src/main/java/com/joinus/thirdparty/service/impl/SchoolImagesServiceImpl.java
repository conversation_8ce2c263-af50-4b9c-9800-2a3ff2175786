package com.joinus.thirdparty.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.joinus.common.BaseServiceImpl;
import com.joinus.dao.SchoolEntity;
import com.joinus.dao.SchoolImagesEntity;
import com.joinus.dao.mapper.SchoolImagesMapper;
import com.joinus.dao.mapper.SchoolMapper;
import com.joinus.thirdparty.model.result.SchoolInfoResult;
import com.joinus.thirdparty.service.SchoolImagesService;
import com.joinus.thirdparty.service.SchoolService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
@Slf4j
public class SchoolImagesServiceImpl extends BaseServiceImpl<SchoolImagesMapper, SchoolImagesEntity> implements SchoolImagesService {

    @Override
    public List<SchoolImagesEntity> getSchoolImages(Long schoolId) {

        return this.baseMapper.selectList(
                new QueryWrapper<SchoolImagesEntity>()
                        .eq("school_id", schoolId));
    }
}
