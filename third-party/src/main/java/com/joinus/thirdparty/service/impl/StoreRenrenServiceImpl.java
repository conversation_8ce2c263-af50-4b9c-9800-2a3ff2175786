package com.joinus.thirdparty.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.joinus.common.exception.BaseException;
import com.joinus.thirdparty.model.param.StoreBusinessPointsExcel;
import com.joinus.thirdparty.model.param.StoreBusinessPointsParam;
import com.joinus.thirdparty.model.result.StoreRenrenBasicInfoResult;
import com.joinus.thirdparty.service.StoreRenrenService;
import com.joinus.thirdparty.util.ShopUtil;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import java.io.*;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

@Service
public class StoreRenrenServiceImpl implements StoreRenrenService {

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Override
    public Long getMemberIdByMobille(String mobile) throws Exception {
        Long memberId = ShopUtil.getMemberIdByMobile(mobile);
        if (null == memberId || memberId == 0) {
            throw new BaseException("未查询到memberId");
        }
        return memberId;
    }

    @Override
    public StoreRenrenBasicInfoResult getBasicInfoByMemberId(Long memberId) throws Exception {
        JSONObject basicInfo = ShopUtil.getBasicInfo(memberId);
        if (null == basicInfo) {
            throw new BaseException("未查询到用户信息");
        }
        return StoreRenrenBasicInfoResult.of(basicInfo);
    }

    @Override
    public StoreRenrenBasicInfoResult changeBusinessPoints(Long memberId, StoreBusinessPointsParam param) throws Exception {
        JSONObject paramObject = JSONUtil.parseObj(param);
        paramObject.set("member_id", memberId);
        paramObject.set("change_type", param.getChangeType().getType());
        JSONObject result = ShopUtil.sendCredit(paramObject);
        if (null == result) {
            throw new BaseException(param.getChangeType().getDesc() + "用户积分失败");
        }
        return this.getBasicInfoByMemberId(memberId);
    }

    @Override
    public String importBusinessPoints(MultipartFile file) throws IOException {
        InputStream inputStream = file.getInputStream();
        List<StoreBusinessPointsExcel> dataList = EasyExcel.read(inputStream)
                .head(StoreBusinessPointsExcel.class)
                .sheet()
                .doReadSync();

        // 添加列处理
        for (StoreBusinessPointsExcel data : dataList) {
            data.setResult("失败");
            try {
                Long memberId = this.getMemberIdByMobille(data.getMobile());
                if (null == memberId) {
                    data.setErrorReason("找不到用户信息");
                    continue;
                }
                StoreBusinessPointsParam businessPointsParam = BeanUtil.copyProperties(data,
                        StoreBusinessPointsParam.class);
                this.changeBusinessPoints(memberId, businessPointsParam);
                data.setResult("成功");
            } catch (Exception e) {
                e.printStackTrace();
                data.setResult("失败");
                data.setErrorReason("发放积分异常:" + e.getMessage());
            }
        }

        // 数据处理完成，写回文件
//        writeFile(dataList);
        String uuid = UUID.randomUUID().toString();
        stringRedisTemplate.opsForValue().set(StrUtil.format("thirt-party:renren-store:download:{}", uuid),
                JSONUtil.toJsonStr(dataList), 1, TimeUnit.DAYS);
        return uuid;
    }

    @Override
    public List<StoreBusinessPointsExcel> downloadExcel(String uuid) throws FileNotFoundException {
        String key = StrUtil.format("thirt-party:renren-store:download:{}", uuid);
        String value = stringRedisTemplate.opsForValue().get(key);
        if (StrUtil.isBlank(value)) {
            throw new BaseException("未查询到错误文件");
        }
        return JSONUtil.toList(value, StoreBusinessPointsExcel.class);
    }

    public void writeFile(List<StoreBusinessPointsExcel> dataList) throws FileNotFoundException {
        String fileName = UUID.randomUUID().toString();
        String outputPath = StrUtil.format("/tmp/{}.xlsx", fileName);
        OutputStream out = new FileOutputStream(outputPath);
        EasyExcel.write(out, StoreBusinessPointsExcel.class)
                .sheet("Sheet1")
                .doWrite(dataList);
    }
}
