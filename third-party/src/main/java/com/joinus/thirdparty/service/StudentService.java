package com.joinus.thirdparty.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.joinus.common.model.param.PageParam;
import com.joinus.common.model.param.StudentCardInfoParam;
import com.joinus.common.model.param.StudentLeaveParam;
import com.joinus.dao.StudentEntity;
import com.joinus.thirdparty.model.param.YiDuStudentListParam;
import com.joinus.thirdparty.model.po.StudentLeaveInfoPO;
import com.joinus.thirdparty.model.result.*;

import java.util.List;

/**
 * <AUTHOR> anpy
 * @create 2023/10/7 13:52
 */
public interface StudentService {

    /**
     * 郑州外国语学校全量学生同步
     *
     * @param param current 当前页
     *              size   每页条数
     * @return 学生分页数据
     */
    StudentInfoResult getStudentsBySchoolId(PageParam param,Long schoolId);


    /**
     * 按日全量同步学生刷卡信息
     *
     * @param param current 当前页
     *              size   每页条数
     *              date 刷卡记录入库日期
     * @return 按日全量同步学生刷卡信息分页数据
     */
    StudentCardInfoResult getStudentCardInfoBySchoolId(StudentCardInfoParam param,Long schoolId);


    StudentLeaveInfoResult  getStudentLeavesInfoBySchoolId(StudentLeaveParam param,Long schoolId);


    /**
     * 全量同步宿舍
     *
     * @return 按日全量同步学生刷卡信息分页数据
     */
    List<DormInfoResult> getDormsBySchoolId(Long schoolId);



    /**
     * 四十四中图书馆学生同步
     *
     * @param param current 当前页
     *              size   每页条数
     * @return 学生分页数据
     */
    SyncStudentCardInfoResult syncLibraryStudent(PageParam param);


    /**
     * 四十四中图书馆学生同步
     *
     * @param param      current 当前页
     *                   size   每页条数
     * @param updateTime 学生更新时间不为空时增量
     * @return 学生分页数据
     */
    SyncStudentCardInfoResult incrementSyncLibraryStudent(PageParam param, String updateTime);

    /**
     * 四十四中图书馆学生同步
     *
     * @param param      current 当前页
     *                   size   每页条数
     * @param updateTime 学生更新时间不为空时增量
     * @return 学生分页数据
     */
    StudentInfoResult getIncrementSyncStudent(PageParam param, String updateTime,Long schoolId);

    /**
     * 益读学生列表
     * @param yiDuStudentListParam
     *          schoolId: 学校id
     *          gradeId: 年级id
     *          classId: 班级id
     *          studentName: 学生姓名
     * @return 益读学生列表
     */
    IPage<YiDuStudentResult> loadYiDuStudentList(YiDuStudentListParam yiDuStudentListParam);

    /**
     * 根据photoUrl获取学生特征码
     * @param schoolId 学校id
     * @param photoUrl 图片url
     * @return 有特征码的学生列表
     */
    StudentEntity loadStudentByImg(Long schoolId, String photoUrl);

    List<StudentEntity> getStudentBySchoolId(Long schoolId);

}
