package com.joinus.thirdparty.service;

import com.joinus.dao.ExternalOrderEntity;

/**
 * <AUTHOR> anpy
 * @create 2023/8/25 14:09
 */
public interface ExternalOrderService {

    /**
     * 存储订单、退款信息
     */
    void saveOrderInfo(ExternalOrderEntity externalOrderEntity);

    /**
     * 根据爱家校订单获取订单信息
     * @param ijxOrderId 爱家校订单
     * @return 订单信息
     */
    ExternalOrderEntity loadOrderByIjxOrderId(String ijxOrderId);

    /**
     * 更新订单快照
     * @param imgUrl 照片地址
     * @param ijxOrderId ijx订单id
     */
    void updateOrderByIjxOrderId(String imgUrl, String ijxOrderId);
}
