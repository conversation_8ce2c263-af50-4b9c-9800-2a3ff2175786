package com.joinus.thirdparty.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.joinus.dao.TeacherEntity;
import com.joinus.thirdparty.model.result.TeacherBaseInfoResult;
import com.joinus.thirdparty.model.result.TeacherInfoResult;

import java.util.List;


/**
 * <AUTHOR> @description 针对表【t_teacher】的数据库操作Service
 * @createDate
 */
public interface TeacherService extends IService<TeacherEntity> {

     TeacherInfoResult checkRoleBytelNum(String telNum);

     TeacherEntity getTeacherByTelNum(String mobile);


     List<TeacherBaseInfoResult> getSpecificRoleTeachers(Long schoolId);

     List<TeacherEntity> getTeacherByRoleId(Long roleId);

}
