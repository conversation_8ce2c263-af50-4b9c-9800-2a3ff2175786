package com.joinus.thirdparty.service;

import com.joinus.common.model.param.OrderRefundParam;
import com.joinus.common.model.param.PageParam;
import com.joinus.common.model.response.IPayResult;
import com.joinus.thirdparty.model.param.*;
import com.joinus.thirdparty.model.result.SyncStudentInfoResult;
import com.joinus.thirdparty.model.result.SyncZhongLiangStudentInfoResult;

/**
 * 自动售货机
 * <AUTHOR> anpy
 * @create 2023/8/23 14:16
 */
public interface SchoolVendingService {

    /**
     * 全量同步学生百度特征码
     * @param param
     *          current:当前页
     *          size:每页条数
     * @param messageBoardDetailListParam
     *          deviceNum:设备编号
     * @return deviceNum关联的学校有百度人脸特征id的全量学生
     */
    SyncStudentInfoResult faceFeatureFullPage(PageParam param, FaceFeatureFullPageParam messageBoardDetailListParam);

    /**
     * 增量同步学生百度特征码
     * @param param
     *          current:当前页
     *          size:每页条数
     * @param faceFeatureIncrementPageParam
     *          deviceNum:设备编号
     *          updateTime：增量时间
     * @return deviceNum关联的学校有百度人脸特征id的BAIDU_FEATURE_UPDATE_TIME在updateTime之后的学生
     */
    SyncStudentInfoResult faceFeatureIncrementPage(PageParam param, FaceFeatureIncrementPageParam faceFeatureIncrementPageParam);

    /**
     * 全量同步学生百度特征码
     * @param param
     *          current:当前页
     *          size:每页条数
     * @param messageBoardDetailListParam
     *          deviceNum:设备编号
     * @return deviceNum关联的学校有百度人脸特征id的全量学生
     */
    SyncZhongLiangStudentInfoResult zhongLiangFaceFeatureFullPage(PageParam param, FaceFeatureFullPageParam messageBoardDetailListParam);

    /**
     * 增量同步学生百度特征码
     * @param param
     *          current:当前页
     *          size:每页条数
     * @param faceFeatureIncrementPageParam
     *          deviceNum:设备编号
     *          updateTime：增量时间
     * @return deviceNum关联的学校有百度人脸特征id的BAIDU_FEATURE_UPDATE_TIME在updateTime之后的学生
     */
    SyncZhongLiangStudentInfoResult zhongLiangFaceFeatureIncrementPage(PageParam param, FaceFeatureIncrementPageParam faceFeatureIncrementPageParam);
    /**
     * 下单
     * @param orderParam 下单信息
     */
    IPayResult order(OrderParam orderParam);

    /**
     * 退款
     * @param ijxOrderId 爱家校订单id
     */
    void refundOrder(OrderRefundParam orderRefundParam);

    /**
     * 上传快照
     * @param snapshotParam
     *      imageFile：照片
     *      ijxOrderId：订单id
     */
    void uploadSnapshot(SnapshotParam snapshotParam);

    /**
     * 根据订单号获取快照
     * @param ijxOrderId 订单号
     */
    String loadSnapshot(String ijxOrderId);

    void bindDevice(DeviceParam deviceParam);

    void unBindDevice(String deviceNum);
}
