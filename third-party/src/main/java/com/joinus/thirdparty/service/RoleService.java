package com.joinus.thirdparty.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.joinus.dao.RoleEntity;
import com.joinus.dao.TeacherEntity;
import com.joinus.dao.enums.IjxRoleEnum;


/**
 * <AUTHOR> @description 针对表【t_role】的数据库操作Service
 * @createDate
 */
public interface RoleService extends IService<RoleEntity> {

    RoleEntity getRoleBySchoolIdAndRoleName(Long schoolId, String roleName, IjxRoleEnum roleType);

    RoleEntity getRoleBySchoolIdAndRoleType(Long schoolId,Integer roleType);

    RoleEntity getRoleBySchoolIdAndRoleType(Long schoolId,Integer roleType);

}
