package com.joinus.thirdparty.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateBetween;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.dao.mapper.SysLogMapper;
import com.joinus.dao.mapper.TerminalMapper;
import com.joinus.dao.*;
import com.joinus.dao.po.TerminalInfoPO;
import com.joinus.thirdparty.model.enums.*;
import com.joinus.thirdparty.model.param.*;
import com.joinus.thirdparty.model.result.TerminalInfoResult;
import com.joinus.thirdparty.service.*;
import com.joinus.thirdparty.util.QiniuUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
public class TerminalServiceImpl extends ServiceImpl<TerminalMapper, TerminalEntity> implements TerminalService {

    @Resource
    private TerminalMapper terminalMapper;
    @Resource
    private TerminalStudentGroupService terminalStudentGroupService;

    @Resource
    private DictionaryService dictionaryService;

    @Resource
    private TerminalPersonService terminalPersonServicel;

    @Resource
    private SysUserService sysUserService;

    @Resource
    private TerminalImgsService terminalImgsService;

    @Resource
    private  TerminalRepairService terminalRepairService;

    @Resource
    private SysLogMapper  sysLogMapper;

    private List<String> remarks= Arrays.asList("SIM卡欠费","SIM卡销户","SIM卡APN异常","无SIM卡","2天线","3天线","4天线","定时器","固定支架","移动支架","地埋","线路无电","网络不稳定","设备丢失","设备停用","路由器");

    private List<String> faultPhenomenons= Arrays.asList("设备离线","刷卡无声音","不显示","通话无声音","按键失灵","摘机无反应","锁打不开","死机","不开门","不识别人脸","不开机","黑屏","白屏","花屏","程序版本低","刷卡无反应","刷卡距离近","参数错误");

    private List<String> teratmentMeasures= Arrays.asList("反馈至运营处理SIM卡","更换SIM卡","更换主板","更换电源","更换4G配件","通电测试","改造线路","更换GPRS模块","更换天线","更换天线帽","更换刷卡板","更换喇叭","更换功放板","更换语音芯片","更换显示屏","更换话筒","更换键盘","更换挂叉"
                                                          ,"更换锁芯","设置参数","重写程序","升级程序","重启设备","更换整机","更换交换机","更换电机","更换水晶头","更换网线","走部门协作","更换挡板","更换蜂鸣器");

    @Override
    public TerminalEntity getAllTerminalByTerminalNum(String terminalNum) {
        Assert.isTrue(ObjectUtil.isNotEmpty(terminalNum), "terminalNum不能为空");
        QueryWrapper<TerminalEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("terminal_num", terminalNum);
        return this.getBaseMapper().selectOne(queryWrapper);
    }

    @Override
    @Transactional
    public void uninstallTerminal(TerminalEntity terminal, UninstallTerminalParam uninstallTerminalParam) {
        terminalStudentGroupService.deleteByTerminalId(terminal.getId());
        this.getBaseMapper().unstallTerminal(terminal.getId());

        if (StrUtil.isNotBlank(uninstallTerminalParam.getSimNum())) {
            TerminalEntity updateTerminal = new TerminalEntity();
            updateTerminal.setId(terminal.getId());
            updateTerminal.setSimNum(uninstallTerminalParam.getSimNum());
            terminal.setSimNum(uninstallTerminalParam.getSimNum());
            this.updateById(updateTerminal);
        }
        List<SysUser> userByFeiShuUserIds = sysUserService.getUserByFeiShuUserIds(uninstallTerminalParam.getFeiShuUserIds());
        String userIds = userByFeiShuUserIds.stream()
                .map(user -> user.getId().toString())
                .collect(Collectors.joining(", "));
        String userNames = userByFeiShuUserIds.stream()
                .map(SysUser::getUserName)
                .collect(Collectors.joining(", "));

        String content = "实施人员[" + userNames + "]卸载了设备" + terminal.getTerminalName();

        DictionaryEntity dictionary = dictionaryService.getById(terminal.getTypeId());
        TerminalPersonEntity terminalPersonEntity = new TerminalPersonEntity();
        terminalPersonEntity.setOperateType(TerminalOperatorTypeEnum.TYPE_UNINSTALL.getCode());
        terminalPersonEntity.setUserIds(userIds);
        terminalPersonEntity.setUserNames(userNames);
        terminalPersonEntity.setTerminalId(terminal.getId());
        terminalPersonEntity.setTerminalName(terminal.getTerminalName());
        terminalPersonEntity.setTerminalTypeName(dictionary != null ? (dictionary.getDictName() + "[" + dictionary.getDictDesc() + "]") : "");
        terminalPersonEntity.setTerminalNum(terminal.getTerminalNum());
        terminalPersonEntity.setTerminalSimNum(terminal.getSimNum());
        terminalPersonEntity.setOperateContent(content);
        terminalPersonEntity.setOperateTime(new Date());
        terminalPersonServicel.save(terminalPersonEntity);
        TerminalEntity rootTerminal = this.baseMapper.getRootTerminalByChildId(terminal.getId());
        this.updateParentExistStatus(terminal.getParentTerminalId());
        //更新上位机人脸识别设备数量
        this.baseMapper.updateRootTerminalFaceDeviceNum(rootTerminal.getId());

    }

    @Override
    @Transactional
    public void moveTerminal(MoveTerminalParam moveTerminalParam, TerminalEntity terminalEntity) {
        TerminalEntity terminal = new TerminalEntity();
        terminal.setId(terminalEntity.getId());
        terminal.setInstallAddress(moveTerminalParam.getInstallAddress());
        if (StrUtil.isNotBlank(moveTerminalParam.getSimNum())) {
            terminal.setSimNum(moveTerminalParam.getSimNum());
        }
        // 格式化日期
        terminal.setRelocationTime(new Date(moveTerminalParam.getRelocationTime()));
        if (CollUtil.isNotEmpty(moveTerminalParam.getRemarkList())) {
            moveTerminalParam.setRemarkList(moveTerminalParam.getRemarkList().stream()
                    .map(String::toUpperCase) // 将每个元素转换为大写
                    .collect(Collectors.toList()));
            String remark = this.getRemark(moveTerminalParam.getRemarkList());
            terminal.setRemark(remark);
        }
        terminal.setInstallType("移机");
        List<SysUser> userByFeiShuUserIds = sysUserService.getUserByFeiShuUserIds(moveTerminalParam.getFeiShuUserIds());
        String userIds = userByFeiShuUserIds.stream()
                .map(user -> user.getId().toString())
                .collect(Collectors.joining(", "));
        String userNames = userByFeiShuUserIds.stream()
                .map(SysUser::getUserName)
                .collect(Collectors.joining(", "));
        terminal.setUserIds(userIds);
        terminal.setUserNames(userNames);
        List<String> imgList = new ArrayList<>();
        if (CollUtil.isNotEmpty(moveTerminalParam.getImgUrlList())) {
            if (null != terminalEntity.getImgId()) {
                TerminalImgsEntity oldTerminalImgs = terminalImgsService.getById(terminalEntity.getImgId());
                imgList = Arrays.asList(oldTerminalImgs.getImgurl1(), oldTerminalImgs.getImgurl2(), oldTerminalImgs.getImgurl3(), oldTerminalImgs.getImgurl4()).stream().filter(s -> StrUtil.isNotBlank(s))
                        .collect(Collectors.toList());
                TerminalImgsEntity terminalImgs = new TerminalImgsEntity();
                this.getTerminalImgsEntity(moveTerminalParam.getImgUrlList(), imgList, terminalImgs);
                terminalImgs.setId(terminalEntity.getImgId());
                terminalImgsService.updateById(terminalImgs);
            } else {
                TerminalImgsEntity terminalImgs = new TerminalImgsEntity();

                this.getTerminalImgsEntity(moveTerminalParam.getImgUrlList(), imgList, terminalImgs);
                terminalImgsService.save(terminalImgs);
                terminal.setImgId(terminalImgs.getId());
            }
        }

        String content = "实施人员[" + userNames + "]移机设备" + terminalEntity.getTerminalName();
        DictionaryEntity dictionary = dictionaryService.getById(terminalEntity.getTypeId());
        TerminalPersonEntity terminalPersonEntity = new TerminalPersonEntity();
        terminalPersonEntity.setOperateType(TerminalOperatorTypeEnum.TYPE_MOVE.getCode());
        terminalPersonEntity.setUserIds(userIds);
        terminalPersonEntity.setUserNames(userNames);
        terminalPersonEntity.setTerminalId(terminalEntity.getId());
        terminalPersonEntity.setTerminalName(terminalEntity.getTerminalName());
        terminalPersonEntity.setTerminalTypeName(dictionary != null ? (dictionary.getDictName() + "[" + dictionary.getDictDesc() + "]") : "");
        terminalPersonEntity.setTerminalNum(terminalEntity.getTerminalNum());
        terminalPersonEntity.setTerminalSimNum(terminalEntity.getSimNum());
        terminalPersonEntity.setOperateContent(content);
        terminalPersonEntity.setOperateTime(new Date());
        terminalPersonServicel.save(terminalPersonEntity);
        this.baseMapper.updateById(terminal);
    }

    @Override
    public void addInstallTerminal(AddInstallTerminalParam addInstallTerminalParam, TerminalEntity terminalEntity) {
        TerminalEntity terminal = new TerminalEntity();
        terminal.setId(terminalEntity.getId());
        if (StrUtil.isNotBlank(addInstallTerminalParam.getInstallAddress())) {
            terminal.setInstallAddress(addInstallTerminalParam.getInstallAddress());
        }
        terminal.setSimNum(StrUtil.isNotBlank(addInstallTerminalParam.getSimNum()) ? addInstallTerminalParam.getSimNum() : "11111111111");
        // 格式化日期
        terminal.setInstallTime(new Date(addInstallTerminalParam.getInstallTime()));
        terminal.setInstallType(addInstallTerminalParam.getInstallType());
        if (CollUtil.isNotEmpty(addInstallTerminalParam.getRemarkList())) {
            addInstallTerminalParam.setRemarkList(addInstallTerminalParam.getRemarkList().stream()
                    .map(String::toUpperCase) // 将每个元素转换为大写
                    .collect(Collectors.toList()));
            String remark = this.getRemark(addInstallTerminalParam.getRemarkList());
            terminal.setRemark(remark);
        }
        List<SysUser> userByFeiShuUserIds = sysUserService.getUserByFeiShuUserIds(addInstallTerminalParam.getFeiShuUserIds());
        String userIds = userByFeiShuUserIds.stream()
                .map(user -> user.getId().toString())
                .collect(Collectors.joining(", "));
        String userNames = userByFeiShuUserIds.stream()
                .map(SysUser::getUserName)
                .collect(Collectors.joining(", "));
        terminal.setUserIds(userIds);
        terminal.setUserNames(userNames);
        List<String> imgList = new ArrayList<>();
        if (CollUtil.isNotEmpty(addInstallTerminalParam.getImgUrlList())) {
            if (null != terminalEntity.getImgId()) {
                TerminalImgsEntity oldTerminalImgs = terminalImgsService.getById(terminalEntity.getImgId());
                imgList = Arrays.asList(oldTerminalImgs.getImgurl1(), oldTerminalImgs.getImgurl2(), oldTerminalImgs.getImgurl3(), oldTerminalImgs.getImgurl4()).stream().filter(s -> StrUtil.isNotBlank(s))
                        .collect(Collectors.toList());
                TerminalImgsEntity terminalImgs = new TerminalImgsEntity();
                this.getTerminalImgsEntity(addInstallTerminalParam.getImgUrlList(), imgList, terminalImgs);
                terminalImgs.setId(terminalEntity.getImgId());
                terminalImgsService.updateById(terminalImgs);
            } else {
                TerminalImgsEntity terminalImgs = new TerminalImgsEntity();

                this.getTerminalImgsEntity(addInstallTerminalParam.getImgUrlList(), imgList, terminalImgs);
                terminalImgsService.save(terminalImgs);
                terminal.setImgId(terminalImgs.getId());
            }
        }
        String content = "实施人员[" + userNames + "]"+addInstallTerminalParam.getInstallType()+"设备" + terminalEntity.getTerminalName();
        DictionaryEntity dictionary = dictionaryService.getById(terminalEntity.getTypeId());
        TerminalPersonEntity terminalPersonEntity = new TerminalPersonEntity();
        terminalPersonEntity.setOperateType(TerminalOperatorTypeEnum.TYPE_INSTALL.getCode());
        terminalPersonEntity.setUserIds(userIds);
        terminalPersonEntity.setUserNames(userNames);
        terminalPersonEntity.setTerminalId(terminalEntity.getId());
        terminalPersonEntity.setTerminalName(terminalEntity.getTerminalName());
        terminalPersonEntity.setTerminalTypeName(dictionary != null ? (dictionary.getDictName() + "[" + dictionary.getDictDesc() + "]") : "");
        terminalPersonEntity.setTerminalNum(terminalEntity.getTerminalNum());
        terminalPersonEntity.setTerminalSimNum(terminalEntity.getSimNum());
        terminalPersonEntity.setOperateContent(content);
        terminalPersonEntity.setOperateTime(new Date());
        terminalPersonServicel.save(terminalPersonEntity);
        this.baseMapper.updateById(terminal);
    }


    private void saveSysLog(String content, String operator){
        SysLogEntity sysLogEntity =new SysLogEntity();
        sysLogEntity.setOperator(operator);
        sysLogEntity.setContent(content);
        sysLogEntity.setLogType(1);
        sysLogEntity.setIsActive(1);
        sysLogEntity.setLogDesc(content);
        sysLogEntity.setLogTime(new Date());
        sysLogEntity.setOperatorType(1);
        sysLogMapper.insert(sysLogEntity);
    }

    private String getRemark(List<String> remarkList){
        List<String> containsList = remarkList.stream()
                .filter(remarks::contains)
                .collect(Collectors.toList());

        List<String> notContainsList = remarkList.stream()
                .filter(remark -> !remarks.contains(remark))
                .collect(Collectors.toList());
        // 构建拼接字符串
        String remark = "";
        if (!containsList.isEmpty()) {
            remark = String.join(",", containsList);
        }
        if (!notContainsList.isEmpty()) {
            String notContainsString = "自定义:" + String.join("，", notContainsList);
            if (StrUtil.isNotBlank(remark)) {
                remark += ",";  // 如果result已经有内容，添加逗号分隔
            }
            remark += notContainsString;
        }
        return  remark;
    }

    private String getFaultPhenomenon(List<String> faultPhenomenonList){
        List<String> containsList = faultPhenomenonList.stream()
                .filter(faultPhenomenons::contains)
                .collect(Collectors.toList());

        List<String> notContainsList = faultPhenomenonList.stream()
                .filter(remark -> !faultPhenomenons.contains(remark))
                .collect(Collectors.toList());
        // 构建拼接字符串
        String faultPhenomenon = "";

        if (!notContainsList.isEmpty()) {
            faultPhenomenon = "自定义:" + String.join("，", notContainsList);
        }

        if (!containsList.isEmpty()) {
            if (StrUtil.isNotBlank(faultPhenomenon)) {
                faultPhenomenon += ",";  // 如果result已经有内容，添加逗号分隔
            }
            faultPhenomenon += String.join(",", containsList);
        }
        return  faultPhenomenon;
    }

    private String getTeratmentMeasure(List<String> teratmentMeasureList){
        List<String> containsList = teratmentMeasureList.stream()
                .filter(teratmentMeasures::contains)
                .collect(Collectors.toList());

        List<String> notContainsList = teratmentMeasureList.stream()
                .filter(remark -> !teratmentMeasures.contains(remark))
                .collect(Collectors.toList());
        // 构建拼接字符串
        String teratmentMeasure = "";

        if (!notContainsList.isEmpty()) {
            teratmentMeasure = "自定义:" + String.join("，", notContainsList);

        }
        if (!containsList.isEmpty()) {
            if (StrUtil.isNotBlank(teratmentMeasure)) {
                teratmentMeasure += ",";  // 如果result已经有内容，添加逗号分隔
            }
            teratmentMeasure += String.join(",", containsList);
        }

        return  teratmentMeasure;
    }

    private void getTerminalImgsEntity(List<String> imgList, List<String> resultList, TerminalImgsEntity terminalImgs) {
        QiniuUtil qiniuUtil = new QiniuUtil();
        imgList.forEach(img -> {
            if (StrUtil.isNotBlank(img)) {
                String imgUrl = qiniuUtil.uploadImg(img);
                if (StrUtil.isNotBlank(imgUrl)) {
                    resultList.add(imgUrl);
                }
            }
        });
        List<String> lastFourImages = CollUtil.sub(resultList, Math.max(0, resultList.size() - 4), resultList.size());
        setTerminalImages(lastFourImages, terminalImgs);

    }

    private void setTerminalImages(List<String> images, TerminalImgsEntity terminalImgs) {
        try {
            for (int i = 1; i <= images.size(); i++) {
                Method method = TerminalImgsEntity.class.getMethod("setImgurl" + i, String.class);
                method.invoke(terminalImgs, images.get(i - 1));
            }
        } catch (NoSuchMethodException | IllegalAccessException | InvocationTargetException e) {
            // Consider logging the error or other error handling methods
            e.printStackTrace();
        }
    }

    @Override
    public void updateParentExistStatus(long parentTerminalId) {
        // 判断是否存在子记录
        boolean hasChild = terminalMapper.selectCount(
                new QueryWrapper<TerminalEntity>().eq("PARENT_TERMINAL_ID", parentTerminalId)
        ) > 0;

        // 更新父终端的 exist_child 字段
        UpdateWrapper<TerminalEntity> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("id", parentTerminalId)
                .set("exist_child", hasChild ? 1 : 0);
        terminalMapper.update(null, updateWrapper);
    }

    @Override
    public void addTerminalService(TerminalServiceParam terminalServiceParam, TerminalEntity terminalEntity) {
        TerminalEntity updateTerminal= new TerminalEntity();
        updateTerminal.setId(terminalEntity.getId());
        TerminalServiceEntity terminalServiceEntity =new TerminalServiceEntity();

        terminalServiceEntity.setTerminalId(terminalEntity.getId());
        if (CollUtil.isNotEmpty(terminalServiceParam.getRemarkList())) {
            terminalServiceParam.setRemarkList(terminalServiceParam.getRemarkList().stream()
                    .map(String::toUpperCase) // 将每个元素转换为大写
                    .collect(Collectors.toList()));
            String remark = this.getRemark(terminalServiceParam.getRemarkList());
            terminalServiceEntity.setRemark(remark);
            updateTerminal.setRemark(remark);
        }

        if (CollUtil.isNotEmpty(terminalServiceParam.getFaultPhenomenonList())) {
            terminalServiceParam.setFaultPhenomenonList(terminalServiceParam.getFaultPhenomenonList().stream()
                    .map(String::toUpperCase) // 将每个元素转换为大写
                    .collect(Collectors.toList()));
            String faultPhenomenon = this.getFaultPhenomenon(terminalServiceParam.getFaultPhenomenonList());
            terminalServiceEntity.setFaultPhenomenon(faultPhenomenon);
        }

        if (CollUtil.isNotEmpty(terminalServiceParam.getTreatmentMeasureList())) {
            terminalServiceParam.setTreatmentMeasureList(terminalServiceParam.getTreatmentMeasureList().stream()
                    .map(String::toUpperCase) // 将每个元素转换为大写
                    .collect(Collectors.toList()));
            String teratmentMeasure = this.getTeratmentMeasure(terminalServiceParam.getTreatmentMeasureList());
            terminalServiceEntity.setTreatmentMeasure(teratmentMeasure);
        }

        if (CollUtil.isNotEmpty(terminalServiceParam.getRemarkList())) {
            terminalServiceParam.setRemarkList(terminalServiceParam.getRemarkList().stream()
                    .map(String::toUpperCase) // 将每个元素转换为大写
                    .collect(Collectors.toList()));
            String remark = this.getRemark(terminalServiceParam.getRemarkList());
            terminalServiceEntity.setRemark(remark);
            updateTerminal.setRemark(remark);
        }

        List<SysUser> userByFeiShuUserIds = sysUserService.getUserByFeiShuUserIds(terminalServiceParam.getFeiShuUserIds());

        String userNames = userByFeiShuUserIds.stream()
                .map(SysUser::getUserName)
                .collect(Collectors.joining(", "));
        String userIds = userByFeiShuUserIds.stream()
                .map(user -> user.getId().toString())
                .collect(Collectors.joining(", "));
        terminalServiceEntity.setServiceman(userNames);
        if(StrUtil.isNotBlank(terminalServiceParam.getSimNum())){
            updateTerminal.setSimNum(terminalServiceParam.getSimNum());
        }

        if (StrUtil.isNotBlank(terminalServiceParam.getFaultProgram())) {
            terminalServiceEntity.setFaultProgram(terminalServiceParam.getFaultProgram());
        }

        if(StrUtil.isNotBlank(terminalServiceParam.getInstallAddress())){
            updateTerminal.setInstallAddress(terminalServiceParam.getInstallAddress());
        }

        List<String> imgList = new ArrayList<>();
        if (CollUtil.isNotEmpty(terminalServiceParam.getImgUrlList())) {
            if (null != terminalEntity.getImgId()) {
                TerminalImgsEntity oldTerminalImgs = terminalImgsService.getById(terminalEntity.getImgId());
                imgList = Arrays.asList(oldTerminalImgs.getImgurl1(), oldTerminalImgs.getImgurl2(), oldTerminalImgs.getImgurl3(), oldTerminalImgs.getImgurl4()).stream().filter(s -> StrUtil.isNotBlank(s))
                        .collect(Collectors.toList());
                TerminalImgsEntity terminalImgs = new TerminalImgsEntity();
                this.getTerminalImgsEntity(terminalServiceParam.getImgUrlList(), imgList, terminalImgs);
                terminalImgs.setId(terminalEntity.getImgId());
                terminalImgsService.updateById(terminalImgs);
            } else {
                TerminalImgsEntity terminalImgs = new TerminalImgsEntity();
                this.getTerminalImgsEntity(terminalServiceParam.getImgUrlList(), imgList, terminalImgs);
                terminalImgsService.save(terminalImgs);
                updateTerminal.setImgId(terminalImgs.getId());
            }
        }

        if (StrUtil.isNotBlank(updateTerminal.getSimNum()) || StrUtil.isNotBlank(updateTerminal.getRemark()) || StrUtil.isNotBlank(updateTerminal.getInstallAddress()) || null != updateTerminal.getImgId()) {
            this.updateById(updateTerminal);
        }

        if (null != terminalServiceParam.getOrderTime()) {
            terminalServiceEntity.setOrderTime(new Date(terminalServiceParam.getOrderTime()));
        }

        if (null != terminalServiceParam.getServiceTime()) {
            terminalServiceEntity.setServiceTime(new Date(terminalServiceParam.getServiceTime()));
        }

        terminalServiceEntity.setCreateTime( new Date());
        terminalRepairService.saveTerminalRepair(terminalServiceEntity);

        String content = String.format("实施人员[%s]新增设备编号为: %s的设备维修记录如下:" +
                        "故障终端程序: %s故障现象为: %s处理措施为: %s维修人员为: %s备注为: %s",
                userNames,
                terminalEntity.getTerminalName(),
                terminalServiceEntity.getFaultProgram(),
                terminalServiceEntity.getFaultPhenomenon(),
                terminalServiceEntity.getTreatmentMeasure(),
                userNames,
                terminalServiceEntity.getRemark());
        DictionaryEntity dictionary = dictionaryService.getById(terminalEntity.getTypeId());
        TerminalPersonEntity terminalPersonEntity = new TerminalPersonEntity();
        terminalPersonEntity.setOperateType(TerminalOperatorTypeEnum.TYPE_EDIT.getCode());
        terminalPersonEntity.setUserIds(userIds);
        terminalPersonEntity.setUserNames(userNames);
        terminalPersonEntity.setTerminalId(terminalEntity.getId());
        terminalPersonEntity.setTerminalName(terminalEntity.getTerminalName());
        terminalPersonEntity.setTerminalTypeName(dictionary != null ? (dictionary.getDictName() + "[" + dictionary.getDictDesc() + "]") : "");
        terminalPersonEntity.setTerminalNum(terminalEntity.getTerminalNum());
        terminalPersonEntity.setTerminalSimNum(terminalEntity.getSimNum());
        terminalPersonEntity.setOperateContent(content.toString());
        terminalPersonEntity.setOperateTime(new Date());
        terminalPersonServicel.save(terminalPersonEntity);
        this.saveSysLog(content, userNames);

    }

    @Override
    public TerminalInfoResult getActiveTerminalInfo(TerminalParam terminalParam) {
        Page<TerminalInfoPO> page = new Page<>(terminalParam.getCurrent(), terminalParam.getSize());
        IPage<TerminalInfoPO> activeTerminalPage = terminalMapper.getActiveTerminalInfo(page, terminalParam.getSort());
        List<TerminalInfoPO> records = activeTerminalPage.getRecords();
        List<TerminalInfoPO> resultList = records.stream().map(terminalInfo -> {
            if (StrUtil.isNotBlank(terminalInfo.getBusinessFlag())) {
                terminalInfo.setBusinessFlag(BusinessFlagEnum.getDescByCode(Convert.toInt(terminalInfo.getBusinessFlag())));
            }
            if (StrUtil.isNotBlank(terminalInfo.getPrice())) {
                terminalInfo.setPrice(SimPriceEnum.getLabelByCode(terminalInfo.getPrice()));
            }
            if (StrUtil.isNotBlank(terminalInfo.getOprCode())) {
                terminalInfo.setOprCode(TerminalOprCodeEnum.getLabelByCode(terminalInfo.getOprCode()));
            }
            if (StrUtil.isNotBlank(terminalInfo.getTerminalStatus())) {
                terminalInfo.setTerminalStatus(TerminalStatusEnum.getLabelByCode(Convert.toInt(terminalInfo.getTerminalStatus())));
            }

            if (StrUtil.isNotBlank(terminalInfo.getRemark())) {
                terminalInfo.setRemarkList(Arrays.stream(terminalInfo.getRemark().split(","))
                        .map(s -> s.replace("自定义:", "").trim())
                        .collect(Collectors.toList()));
            }
            terminalInfo.setSimCosts(terminalInfo.getTotalCosts() >(terminalInfo.getCurrentMonthExpense()+terminalInfo.getDiscountOffers()) ? terminalInfo.getTotalCosts() : terminalInfo.getCurrentMonthExpense()+terminalInfo.getDiscountOffers());
            terminalInfo.setIsActive(terminalInfo.getSchoolId() == null ? "2" : terminalInfo.getIsActive());
            return terminalInfo;
        }).collect(Collectors.toList());

        return TerminalInfoResult.builder().records(resultList)
                .pages(activeTerminalPage.getPages())
                .total(activeTerminalPage.getTotal())
                .size(activeTerminalPage.getSize())
                .build();
    }

    @Override
    public TerminalInfoResult getTerminalOnlineInfo(TerminalParam terminalParam) {
        Page<TerminalInfoPO> page = new Page<>(terminalParam.getCurrent(), terminalParam.getSize());
        IPage<TerminalInfoPO> terminalEntityPageList = terminalMapper.getActiveTerminalInfo(page, terminalParam.getSort());
        List<TerminalInfoPO> terminalInfoList = terminalEntityPageList.getRecords().stream()
                .map(terminalInfoPo -> {
                    TerminalInfoPO terminal = new TerminalInfoPO();
                    terminal.setId(terminalInfoPo.getId());
                    terminal.setTerminalNum(terminalInfoPo.getTerminalNum());
                    terminal.setOnline(null != terminalInfoPo.getIsLogin() && terminalInfoPo.getIsLogin() == TerminalLoginEnum.ON_LINE.getCode() ? TerminalLoginEnum.ON_LINE.getLabel() : TerminalLoginEnum.OUT_LINE.getLabel());
                    terminal.setLastTime(terminalInfoPo.getLastTime());
                    if(ObjectUtil.isNotEmpty(terminalInfoPo.getLastTime())){
                       terminal.setLastOnlineTime(DateUtil.toInstant(terminalInfoPo.getLastTime()).toEpochMilli());
                       DateBetween dateBetween = new DateBetween(terminalInfoPo.getLastTime(), DateUtil.date());
                       terminal.setTimeSinceDeviceLastOnline(String.format("%d 天 %d 小时 %d 分钟", dateBetween.between(DateUnit.DAY), dateBetween.between(DateUnit.HOUR) % 24, dateBetween.between(DateUnit.MINUTE) % 60));
                       terminal.setSecondSinceDeviceLastOnline(dateBetween.between(DateUnit.SECOND));
                   }else{
                       terminal.setLastOnlineTime(null);
                       terminal.setSecondSinceDeviceLastOnline(0L);
                       terminal.setTimeSinceDeviceLastOnline("从未在线");
                   }
                    return terminal;
                })
                .collect(Collectors.toList());
        return TerminalInfoResult.builder().records(terminalInfoList)
                .pages(terminalEntityPageList.getPages())
                .total(terminalEntityPageList.getTotal())
                .size(terminalEntityPageList.getSize())
                .build();
    }


}
