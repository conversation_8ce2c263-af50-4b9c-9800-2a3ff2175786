package com.joinus.thirdparty.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.joinus.dao.SchoolEntity;
import com.joinus.dao.SchoolImagesEntity;
import com.joinus.thirdparty.model.result.SchoolInfoResult;

import java.util.List;


/**
 * <AUTHOR> @description 针对表【t_school_images】的数据库操作Service
 * @createDate
 */
public interface SchoolImagesService extends IService<SchoolImagesEntity> {

    List<SchoolImagesEntity> getSchoolImages(Long schoolId);
}
