package com.joinus.thirdparty.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.joinus.common.BaseServiceImpl;
import com.joinus.common.mapper.SysUserMapper;
import com.joinus.dao.SysUser;
import com.joinus.thirdparty.service.SysUserService;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.List;


@Service
public class SysUserServiceImpl extends BaseServiceImpl<SysUserMapper, SysUser> implements SysUserService {

    @Override
    public List<SysUser> getUserByFeiShuUserIds(List<String> feiShuUserIds) {
        Assert.isTrue(ObjectUtil.isNotEmpty(feiShuUserIds), "feiShuUserIds不能为空");
        QueryWrapper<SysUser> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("feishu_user_id", feiShuUserIds);
        return this.baseMapper.selectList(queryWrapper);
    }
}
