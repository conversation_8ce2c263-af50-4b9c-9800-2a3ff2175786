package com.joinus.thirdparty.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.TypeReference;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joinus.common.BaseServiceImpl;
import com.joinus.common.CustomPage;
import com.joinus.common.model.param.PageParam;
import com.joinus.common.model.param.StudentCardInfoParam;
import com.joinus.common.model.param.StudentLeaveParam;
import com.joinus.common.model.response.CommonResponse;
import com.joinus.common.model.response.ResponseMessage;
import com.joinus.common.utils.LocalHttpUtil;
import com.joinus.dao.StudentEntity;
import com.joinus.thirdparty.mapper.StudentMapper;
import com.joinus.thirdparty.model.param.QylFaceFeatureParam;
import com.joinus.thirdparty.model.param.StudentParam;
import com.joinus.thirdparty.model.param.YiDuStudentListParam;
import com.joinus.thirdparty.model.po.StudentLeaveInfoPO;
import com.joinus.thirdparty.model.result.StudentCardInfo;
import com.joinus.thirdparty.model.po.StudentCardInfoPO;
import com.joinus.thirdparty.model.po.StudentInfoPO;
import com.joinus.thirdparty.model.result.*;
import com.joinus.thirdparty.service.StudentService;
import com.joinus.thirdparty.util.QiniuUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> anpy
 * @create 2023/10/7 13:59
 */

@Slf4j
@Service
public class StudentServiceImpl extends BaseServiceImpl<StudentMapper, StudentEntity> implements StudentService {

    @Value("${qyl.facefeature.url}")
    private String qylFeatureUrl;


    @Value("${visiting.school.domain.url:http://localhost:8087}")
    private String visitingSchoolDomainUrl;
    @Resource
    private StudentMapper studentMapper;

    @Override
    public StudentInfoResult getStudentsBySchoolId(PageParam param, Long schoolId) {
        IPage<StudentInfoPO> pageParameters = new CustomPage<StudentInfoPO>().getGenericIpage(param);
        log.info("查询学生信息,参数:{},学校id:{}", param, schoolId);
        IPage<StudentInfoPO> studentInfoResultIpage = studentMapper.getStudentsBySchoolId(pageParameters, schoolId);
        log.info("查询学生信息结束,参数:{},学校id:{}", param, schoolId);
        try{
            QiniuUtil qiniuUtil = new QiniuUtil();
            qiniuUtil.batchCopyImg(studentInfoResultIpage.getRecords());
        }catch (Exception e){
            e.printStackTrace();
            log.error("七牛云拷贝学生照片出错:"+e.getMessage());
        }
        log.info("七牛云拷贝学生照片结束,参数:{},学校id:{}", param, schoolId);
        return StudentInfoResult.builder()
                .records(BeanUtil.copyToList(studentInfoResultIpage.getRecords(),StudentInfo.class))
                .pages(studentInfoResultIpage.getPages())
                .total(studentInfoResultIpage.getTotal())
                .size(studentInfoResultIpage.getSize())
                .build();
    }

    @Override
    public StudentCardInfoResult getStudentCardInfoBySchoolId(StudentCardInfoParam param, Long schoolId) {
        IPage<StudentCardInfoPO> pageParameters = new CustomPage<StudentCardInfoPO>().getGenericIpage(param);
        String today = DateUtil.today();
        IPage<StudentCardInfoPO> studentCaardInfoResultIpage;
        param.setSchoolId(schoolId);
        if (today.equals(param.getQueryDate())) {
            studentCaardInfoResultIpage = studentMapper.getTodayStudentCardInfoBySchoolId(pageParameters, param);
        } else {
            studentCaardInfoResultIpage = studentMapper.getHisStudentCardInfoBySchoolId(pageParameters, param);
        }

        return StudentCardInfoResult.builder().records(BeanUtil.copyToList(studentCaardInfoResultIpage.getRecords(),StudentCardInfo.class))
                .pages(studentCaardInfoResultIpage.getPages())
                .total(studentCaardInfoResultIpage.getTotal())
                .size(studentCaardInfoResultIpage.getSize())
                .build();
    }

    @Override
    public StudentLeaveInfoResult getStudentLeavesInfoBySchoolId(StudentLeaveParam param, Long schoolId) {
        IPage<StudentLeaveInfoPO> pageParameters = new CustomPage<StudentLeaveInfoPO>().getGenericIpage(param);
        param.setSchoolId(schoolId);
        IPage<StudentLeaveInfoPO> studentLeaveInfoResultIpage = studentMapper.getStudentLeavesInfoBySchoolId(pageParameters, param);
        //获取请假ids
        List<Long> leaveIds = studentLeaveInfoResultIpage.getRecords().stream().map(StudentLeaveInfoPO::getId).collect(Collectors.toList());
        //调用访校获取审批记录
        try {
            HttpResponse response = HttpRequest.post(visitingSchoolDomainUrl + "/api/anon/leave/approval-records")
                    .body(JSONUtil.toJsonStr(leaveIds))
                    .timeout(30000)
                    .contentType("application/json;charset=UTF-8")
                    .execute();
            if (response.isOk()) {
                String body = response.body();
                ResponseMessage<Map<Long, List<String>>> responseMessage = JSONUtil.toBean(body,
                        new TypeReference<ResponseMessage<Map<Long, List<String>>>>() {
                        }, true);

                if (ObjectUtil.isNotEmpty(responseMessage.getCode()) && "200".equals(responseMessage.getCode())) {
                    Map<Long, List<String>> approvalRecords = responseMessage.getData();
                    studentLeaveInfoResultIpage.getRecords().forEach(e -> {
                        e.setApprovalRecords(approvalRecords.get(e.getId()));
                    });
                    log.info(approvalRecords.toString());
                }

            }
        } catch (Exception e) {
            log.warn("调用接口出错,查询日期:{},查询数据量:{},查询页数:{}", param.getQueryDate(), param.getSize(), param.getCurrent());
            log.warn(e.getMessage());
        }

        return StudentLeaveInfoResult.builder().records(studentLeaveInfoResultIpage.getRecords())
                .pages(studentLeaveInfoResultIpage.getPages())
                .total(studentLeaveInfoResultIpage.getTotal())
                .size(studentLeaveInfoResultIpage.getSize())
                .build();
    }

    @Override
    public List<DormInfoResult> getDormsBySchoolId(Long schoolId) {

        return this.baseMapper.getDormsBySchoolId(schoolId);
    }

    /**
     * 四十四中图书馆学生同步
     * schoolId在sql中写死
     *
     * @return 学生分页数据
     */
    @Override
    public SyncStudentCardInfoResult syncLibraryStudent(@Valid PageParam param) {
        IPage<StudentCardInfoPO> pageParameters = new CustomPage<StudentCardInfoPO>().getGenericIpage(param);
        IPage<StudentCardInfoPO> studentInfoResultIpage = studentMapper.syncLibraryStudent(pageParameters);
        return SyncStudentCardInfoResult.builder()
                .records(studentInfoResultIpage.getRecords())
                .pages(studentInfoResultIpage.getPages())
                .current(studentInfoResultIpage.getCurrent())
                .total(studentInfoResultIpage.getTotal())
                .size(studentInfoResultIpage.getSize())
                .build();
    }

    @Override
    public SyncStudentCardInfoResult incrementSyncLibraryStudent(PageParam param, String updateTime) {
        IPage<StudentCardInfoPO> pageParameters = new CustomPage<StudentCardInfoPO>().getGenericIpage(param);
        IPage<StudentCardInfoPO> studentInfoResultIpage = studentMapper.incrementSyncLibraryStudent(pageParameters, updateTime);
        return SyncStudentCardInfoResult.builder()
                .records(studentInfoResultIpage.getRecords())
                .pages(studentInfoResultIpage.getPages())
                .current(studentInfoResultIpage.getCurrent())
                .total(studentInfoResultIpage.getTotal())
                .size(studentInfoResultIpage.getSize())
                .build();
    }

    @Override
    public StudentInfoResult getIncrementSyncStudent(PageParam param, String updateTime, Long schoolId) {
        IPage<StudentInfoPO> pageParameters = new CustomPage<StudentInfoPO>().getGenericIpage(param);
        log.info("查询学生信息,参数:{},学校id:{}", param, schoolId);
        IPage<StudentInfoPO> studentInfoResultIpage = studentMapper.getIncrementSyncStudent(pageParameters, updateTime, schoolId);
        log.info("查询学生信息结束,参数:{},学校id:{}", param, schoolId);
        try{
            QiniuUtil qiniuUtil = new QiniuUtil();
            qiniuUtil.batchCopyImg(studentInfoResultIpage.getRecords());
        }catch (Exception e){
            e.printStackTrace();
            log.error("七牛云拷贝学生照片出错:"+e.getMessage());
        }
        log.info("七牛云拷贝学生照片结束,参数:{},学校id:{}", param, schoolId);
        return StudentInfoResult.builder()
                .records(BeanUtil.copyToList(studentInfoResultIpage.getRecords(),StudentInfo.class))
                .pages(studentInfoResultIpage.getPages())
                .total(studentInfoResultIpage.getTotal())
                .size(studentInfoResultIpage.getSize())
                .build();
    }

    @Override
    public IPage<YiDuStudentResult> loadYiDuStudentList(YiDuStudentListParam yiduStudentListParam) {
        StudentParam studentParam = StudentParam.builder()
                .schoolId(yiduStudentListParam.getSchoolId())
                .gradeId(yiduStudentListParam.getGradeId())
                .studentName(yiduStudentListParam.getStudentName())
                .classId(yiduStudentListParam.getClassId())
                .build();
        IPage<StudentEntity> pageParameters = new Page<>();
        pageParameters.setCurrent(yiduStudentListParam.getCurrent());
        pageParameters.setSize(yiduStudentListParam.getSize());
        IPage<StudentEntity> entityIpage = studentMapper.findStudentsByParam(pageParameters, studentParam);
        List<StudentEntity> studentList = entityIpage.getRecords();
        IPage<YiDuStudentResult> iPage = new Page<>();
        List<YiDuStudentResult> collect = studentList.stream().map(item -> {
            YiDuStudentResult yiDuStudentResult = new YiDuStudentResult();
            BeanUtil.copyProperties(item, yiDuStudentResult);
            return yiDuStudentResult;
        }).collect(Collectors.toList());
        BeanUtil.copyProperties(entityIpage, iPage);
        iPage.setRecords(collect);
        return iPage;
    }

    @Override
    public StudentEntity loadStudentByImg(Long schoolId, String photoUrl) {
        log.info("益读-loadStudentByImg schoolId:{},photoUrl:{}", schoolId, photoUrl);
        StudentParam studentParam = StudentParam.builder()
                .schoolId(schoolId)
                .build();
        //获取该学校所有学生数据，提取所有人脸特征码
        List<StudentEntity> studentEntityList = studentMapper.findStudentsByParam(studentParam);
        List<String> faceFeatureList = studentEntityList.stream().map(StudentEntity::getFaceFeature).collect(Collectors.toList());
        log.info("益读-loadStudentByImg,特征码长度:{}", faceFeatureList.size());
        QylFaceFeatureParam qylFaceFeatureParam = QylFaceFeatureParam.builder()
                .photoUrl(photoUrl)
                .sFaceFeatureList(faceFeatureList).build();
        //调用青于蓝接口获取人脸特征码
        String response = LocalHttpUtil.post(qylFeatureUrl, qylFaceFeatureParam);
        log.info("益读-loadStudentByImg response:{}", response);
        QylFaceFeatureResult qylFaceFeatureResult = JSONUtil.toBean(response, QylFaceFeatureResult.class);
        CommonResponse.ERROR.assertEquals(qylFaceFeatureResult.getCode(), "200", qylFaceFeatureResult.getMessage());
        log.info("益读-loadStudentByImg qylFaceFeatureResult:{}", qylFaceFeatureResult);
        if (CollUtil.isNotEmpty(qylFaceFeatureResult.getData())) {
            //匹配到多个特征码，取第一个，根据特征码匹配学生
            String faceFeature = qylFaceFeatureResult.getData().get(0);
            List<StudentEntity> studentByFaceFeature = studentEntityList.stream().filter(item -> StrUtil.isNotEmpty(item.getFaceFeature()) && item.getFaceFeature().equals(faceFeature)).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(studentByFaceFeature)) {
                return studentByFaceFeature.get(0);
            }
        }
        return null;
    }

    @Override
    public List<StudentEntity> getStudentBySchoolId(Long schoolId) {
        QueryWrapper<StudentEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.isNotNull("cardcode");
        queryWrapper.eq("school_id", schoolId);
        queryWrapper.eq("isactive", 1);
        return this.baseMapper.selectList(queryWrapper);
    }
}
