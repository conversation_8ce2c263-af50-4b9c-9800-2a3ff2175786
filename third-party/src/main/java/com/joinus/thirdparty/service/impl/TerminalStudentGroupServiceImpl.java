package com.joinus.thirdparty.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.common.mapper.TerminalStudentGroupMapper;
import com.joinus.dao.TerminalStudentGroupEntity;
import com.joinus.thirdparty.service.TerminalStudentGroupService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

@Slf4j
@Service
public class TerminalStudentGroupServiceImpl extends ServiceImpl<TerminalStudentGroupMapper, TerminalStudentGroupEntity> implements TerminalStudentGroupService {

    @Override
    public int deleteByTerminalId(Long terminalId) {
        Assert.isTrue(ObjectUtil.isNotEmpty(terminalId), "terminalId不能为空");
        QueryWrapper<TerminalStudentGroupEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("terminal_id", terminalId);
        return this.baseMapper.delete(queryWrapper);
    }
}
