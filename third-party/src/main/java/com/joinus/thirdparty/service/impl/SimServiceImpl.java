package com.joinus.thirdparty.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joinus.dao.TerminalSmsBalanceHistoryEntity;
import com.joinus.dao.TerminalSmsHistoryEntity;
import com.joinus.dao.mapper.SchoolMapper;
import com.joinus.dao.po.SimInfoPO;
import com.joinus.dao.po.SimLogPO;
import com.joinus.dao.po.TerminalInfoPO;
import com.joinus.thirdparty.model.enums.SimPriceEnum;
import com.joinus.thirdparty.model.enums.TerminalOprCodeEnum;
import com.joinus.thirdparty.model.param.*;
import com.joinus.thirdparty.model.result.SimInfoResult;
import com.joinus.thirdparty.model.result.SimLogResult;
import com.joinus.thirdparty.service.SimService;
import com.joinus.thirdparty.service.TerminalSmsBalanceHistoryService;
import com.joinus.thirdparty.service.TerminalSmsHistoryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class SimServiceImpl implements SimService {

    @Autowired
    private TerminalSmsBalanceHistoryService terminalSmsBalanceHistoryService;

    @Autowired
    private TerminalSmsHistoryService terminalSmsHistoryService;

    @Autowired
    private SchoolMapper schoolMapper;
    @Override
    public SimInfoResult getSimDetails(SimParam simParam) {
        Page<TerminalSmsBalanceHistoryEntity> page = terminalSmsBalanceHistoryService.getYesterdayTerminalSmsBalance(simParam.getCurrent(), simParam.getSize());
        List<SimInfoPO> resultList = new ArrayList<>();
        List<TerminalSmsBalanceHistoryEntity> simNumList = page.getRecords();
        List<Long> smsIdList = simNumList.stream().map(TerminalSmsBalanceHistoryEntity::getLastestSmsId).distinct().collect(Collectors.toList());
        List<TerminalSmsHistoryEntity> terminalSmsHistoryList = terminalSmsHistoryService.listLast24HourTerminalSms();
        Map<Long, TerminalSmsHistoryEntity> productSmsMap = terminalSmsHistoryList.stream().filter(sms -> sms.getContent().startsWith("【套餐资费介绍】")).collect(Collectors.toMap(TerminalSmsHistoryEntity::getTerminalId, sms -> sms));

        Map<Long, TerminalSmsHistoryEntity> businessSmsMap = terminalSmsHistoryList.stream().filter(sms -> sms.getContent().startsWith("您好，您已开通收费业务有")).collect(Collectors.toMap(TerminalSmsHistoryEntity::getTerminalId, sms -> sms));

        List<TerminalSmsHistoryEntity> terminalSmsHistoryEntitieList = terminalSmsHistoryService.getBaseMapper().selectBatchIds(smsIdList);
        Map<Long, String> smsMap = terminalSmsHistoryEntitieList.stream()
                .collect(Collectors.toMap(TerminalSmsHistoryEntity::getId, TerminalSmsHistoryEntity::getContent));
        simNumList.forEach(terminalSmsBalanceHistoryEntity -> {
            SimInfoPO simInfo = new SimInfoPO();
            simInfo.setSimNum(terminalSmsBalanceHistoryEntity.getSimNum());
            simInfo.setCostUpdateTime(DateUtil.endOfDay(terminalSmsBalanceHistoryEntity.getBalanceDate()).getTime());
            simInfo.setTotalCost(terminalSmsBalanceHistoryEntity.getCurrentMonthExpense());
            simInfo.setCostDetails(smsMap.get(terminalSmsBalanceHistoryEntity.getLastestSmsId()));
            Long terminalId = terminalSmsBalanceHistoryEntity.getTerminalId();
            TerminalSmsHistoryEntity productSms = productSmsMap.get(terminalId);
            TerminalSmsHistoryEntity businessSms = businessSmsMap.get(terminalId);
            if (ObjectUtil.isNotEmpty(productSms)) {
                simInfo.setProductDetail(productSms.getContent());
                simInfo.setProductUpdateTime(productSms.getReceivedAt().getTime());
            }
            if (ObjectUtil.isNotEmpty(businessSms)) {
                simInfo.setBusinessDetail(businessSms.getContent());
                simInfo.setBusinessUpdateTime(businessSms.getReceivedAt().getTime());
            }
            resultList.add(simInfo);
        });
        return SimInfoResult.builder().records(resultList)
                .pages(page.getPages())
                .total(page.getTotal())
                .size(page.getSize())
                .build();
    }

    @Override
    public SimLogResult getSimLogs(SimParam simParam) {
        Page<SimLogPO> page = new Page<>(simParam.getCurrent(), simParam.getSize());
        IPage<SimLogPO> resultPage = schoolMapper.getToDaySimLogs(page);
        List<SimLogPO> resultList = resultPage.getRecords().stream().peek(simLog -> {
            simLog.setCreateTime(simLog.getCreateDate().getTime());
            simLog.setFeeTime(simLog.getFeeDate().getTime());
            simLog.setProductName(SimPriceEnum.getLabelByCode(simLog.getProductName()));
            simLog.setOperate(TerminalOprCodeEnum.getLabelByCode(simLog.getOperate()));
        }).collect(Collectors.toList());
        return SimLogResult.builder().records(resultList)
                .pages(resultPage.getPages())
                .total(resultPage.getTotal())
                .size(resultPage.getSize())
                .build();
    }


}
