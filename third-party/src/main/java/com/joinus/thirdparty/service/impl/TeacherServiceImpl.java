package com.joinus.thirdparty.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.joinus.common.BaseServiceImpl;
import com.joinus.common.model.enums.ActiveEnum;
import com.joinus.dao.RoleEntity;
import com.joinus.dao.SchoolEntity;
import com.joinus.dao.TeacherEntity;
import com.joinus.dao.enums.IjxRoleEnum;
import com.joinus.dao.mapper.SchoolMapper;
import com.joinus.dao.mapper.TeacherMapper;
import com.joinus.thirdparty.model.result.TeacherBaseInfoResult;
import com.joinus.thirdparty.model.result.TeacherInfoResult;
import com.joinus.thirdparty.service.RoleService;
import com.joinus.thirdparty.service.SchoolService;
import com.joinus.thirdparty.service.TeacherService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;


@Service
@Slf4j
public class TeacherServiceImpl extends BaseServiceImpl<TeacherMapper, TeacherEntity> implements TeacherService {

    @Value("${check.sunshine-catering.app.role.name:学校阳光餐饮}")
    public String roleName;

    @Value("${voucher.approval.role.name:学校阳光餐饮}")
    public String voucherApprovalRoleName;

    @Autowired
    private RoleService roleService;

    @Autowired
    private SchoolMapper schoolMapper;

    @Override
    public TeacherInfoResult checkRoleBytelNum(String telNum) {
        TeacherInfoResult teacherInfoResult = new TeacherInfoResult();
        teacherInfoResult.setHasSpecificRole(false);
        try {
            TeacherEntity teacher = this.getTeacherByTelNum(telNum);
            if (ObjectUtil.isEmpty(teacher)) {
                return teacherInfoResult;
            }
            RoleEntity roleEntity = roleService.getRoleBySchoolIdAndRoleName(teacher.getSchoolId(), roleName, IjxRoleEnum.CANTEEN_STAFF);
            if (ObjectUtil.isEmpty(roleEntity)) {
                return teacherInfoResult;
            }
            if (roleEntity.getId().equals(teacher.getRoleId())) {
                SchoolEntity school =schoolMapper.selectById(teacher.getSchoolId());
                teacherInfoResult.setHasSpecificRole(true);
                teacherInfoResult.setSchoolId(teacher.getSchoolId());
                teacherInfoResult.setTeacherId(teacher.getId());
                teacherInfoResult.setTeacherName(teacher.getTeacherName());
                teacherInfoResult.setSchoolName(school.getSchoolName());
                return teacherInfoResult;
            }

        } catch (Exception e) {
            log.warn("通过手机号去校验是否包含角色出错:{}", e.getMessage(), e);
        }
        return teacherInfoResult;
    }

    @Override
    public TeacherEntity getTeacherByTelNum(String telNum) {
        LambdaQueryWrapper<TeacherEntity> wrapper = Wrappers.lambdaQuery(TeacherEntity.class)
                .eq(TeacherEntity::getTelNum, telNum)
                .eq(TeacherEntity::getIsactive, ActiveEnum.VALID);
        return this.baseMapper.selectOne(wrapper);
    }


    @Override
    public List<TeacherEntity> getTeacherByRoleId(Long roleId) {
        return this.baseMapper.selectList( Wrappers.lambdaQuery(TeacherEntity.class)
                .eq(TeacherEntity::getRoleId, roleId)
                .eq(TeacherEntity::getIsactive, ActiveEnum.VALID));
    }

    @Override
    public List<TeacherBaseInfoResult> getSpecificRoleTeachers(Long schoolId) {
        RoleEntity role = roleService.getRoleBySchoolIdAndRoleName(schoolId, voucherApprovalRoleName, IjxRoleEnum.SCHOOL_SUNSHINE_CATERING);
        if (ObjectUtil.isEmpty(role)) {
            return new ArrayList<>();
        }
        List<TeacherEntity> teacherList = this.getTeacherByRoleId(role.getId());
        return BeanUtil.copyToList(teacherList, TeacherBaseInfoResult.class);
    }
}
