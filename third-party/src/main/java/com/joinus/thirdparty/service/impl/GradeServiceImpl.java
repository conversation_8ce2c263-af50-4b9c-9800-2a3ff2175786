package com.joinus.thirdparty.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.dao.GradeEntity;
import com.joinus.thirdparty.mapper.GradeMapper;
import com.joinus.thirdparty.mapper.impl.GradeMapperImpl;
import com.joinus.thirdparty.service.GradeService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 年级表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-11
 */
@Service("GradeServiceImpl")
public class GradeServiceImpl extends ServiceImpl<GradeMapper, GradeEntity> implements GradeService {

    @Resource
    private GradeMapperImpl gradeMapperImpl;

    @Override
    public List<GradeEntity> loadGradeList(Long schoolId) {
        return gradeMapperImpl.loadGradeList(schoolId, null);
    }
}
