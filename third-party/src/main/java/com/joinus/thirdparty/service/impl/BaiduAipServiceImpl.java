package com.joinus.thirdparty.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.ContentType;
import cn.hutool.http.HttpRequest;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.joinus.common.model.enums.ApiResultCode;
import com.joinus.common.model.response.CommonResponse;
import com.joinus.common.utils.CommonUtils;
import com.joinus.thirdparty.model.param.AddBaiduFaceFeatureParam;
import com.joinus.thirdparty.model.param.BaiduRegisterParam;
import com.joinus.thirdparty.model.result.BaiduFeatureResult;
import com.joinus.thirdparty.service.BaiduAipService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.Objects;

/**
 * <AUTHOR> anpy
 * @create 2023/8/23 8:35
 */

@Slf4j
@Service
public class BaiduAipServiceImpl implements BaiduAipService {

    @Value("${baidu-FaceFeature-Api}")
    private String baiduFaceFeatureApi;

    public static void main(String[] args) {

    }

    @Override
    public BaiduFeatureResult registerBaiduFaceFeature(MultipartFile imageFile, BaiduRegisterParam baiduRegisterParam) {
        try {
            String imageData = null;
            if (StrUtil.isNotEmpty(baiduRegisterParam.getImageUrl())) {
                imageData = baiduRegisterParam.getImageUrl();
            } else if (imageFile != null) {
                imageData = Base64.encodeBase64String(imageFile.getBytes());
            }
            ArrayList<AddBaiduFaceFeatureParam> list = new ArrayList<>();
            AddBaiduFaceFeatureParam param = AddBaiduFaceFeatureParam
                    .builder()
                    .userId(baiduRegisterParam.getUserId())
                    .groupId(baiduRegisterParam.getGroupId())
                    .imageTypeEnum(baiduRegisterParam.getImageType())
                    .imageData(imageData)
                    .build();
            list.add(param);
            String result = HttpRequest.post(baiduFaceFeatureApi)
                    .contentType(ContentType.JSON.getValue())
                    .body(CommonUtils.objectToJson(list))
                    .execute()
                    .body();
            log.info("百度人脸特征码,userId:{},注册结果:{}", baiduRegisterParam.getUserId(), result);
            BaiduFeatureResult baiduTokenResult = new ObjectMapper().readValue(result, BaiduFeatureResult.class);
            if (Objects.equals(baiduTokenResult.getCode(), String.valueOf(ApiResultCode.SUCCESS.getCode()))) {
                if (CollUtil.isNotEmpty(baiduTokenResult.getData())) {
                    BaiduFeatureResult.DataDTO dataDTO = baiduTokenResult.getData().get(0);
                    String faceFeature = dataDTO.getFaceFeature();
                    CommonResponse.ERROR.assertNotNull(faceFeature, "生成人脸特征码失败!");
                    dataDTO.setGroupId(baiduRegisterParam.getGroupId());
                    return baiduTokenResult;
                }
            }
            return null;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("百度人脸特征码：生成失败，userId={},错误原因：{}", baiduRegisterParam.getUserId(), e.getMessage());
            CommonResponse.assertError("生成人脸特征码失败!");
        }
        return null;
    }
}
