package com.joinus.thirdparty.service.impl;

import com.joinus.common.BaseServiceImpl;
import com.joinus.dao.TerminalSmsHistoryEntity;
import com.joinus.dao.mapper.TerminalSmsHistoryMapper;
import com.joinus.thirdparty.service.TerminalSmsHistoryService;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
public class TerminalSmsHistoryServiceImpl extends BaseServiceImpl<TerminalSmsHistoryMapper, TerminalSmsHistoryEntity> implements TerminalSmsHistoryService {


    @Override
    public List<TerminalSmsHistoryEntity> listLast24HourTerminalSms() {
        return this.baseMapper.listLast24HourTerminalSms();
    }
}
