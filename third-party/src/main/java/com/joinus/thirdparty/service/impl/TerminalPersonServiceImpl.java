package com.joinus.thirdparty.service.impl;

import com.joinus.common.BaseServiceImpl;
import com.joinus.common.mapper.TerminalPersonMapper;
import com.joinus.dao.TerminalPersonEntity;
import com.joinus.thirdparty.service.TerminalPersonService;
import org.springframework.stereotype.Service;


/**
* @description 针对表【T_TERMINAL_PERSON】的数据库操作Service实现
* @createDate 2024-5-27 11:45:47
*/
@Service
public class TerminalPersonServiceImpl extends BaseServiceImpl<TerminalPersonMapper, TerminalPersonEntity>
    implements TerminalPersonService {


}




