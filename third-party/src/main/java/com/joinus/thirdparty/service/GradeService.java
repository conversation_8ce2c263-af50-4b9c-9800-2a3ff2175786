package com.joinus.thirdparty.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.joinus.dao.GradeEntity;

import java.util.List;

/**
* 年级服务类
* <AUTHOR> anpy
* @create 2023/10/19 9:27
*/
public interface GradeService extends IService<GradeEntity> {

    /**
     * 根据学校id获取年级列表
     *
     * @param schoolId 学校id
     * @return 年级列表
     */
    List<GradeEntity> loadGradeList(Long schoolId);

}
