package com.joinus.thirdparty.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joinus.common.BaseServiceImpl;
import com.joinus.dao.TerminalSmsBalanceHistoryEntity;
import com.joinus.dao.mapper.TerminalSmsBalanceHistoryMapper;
import com.joinus.thirdparty.service.TerminalSmsBalanceHistoryService;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;


@Service
public class TerminalSmsBalanceHistoryServiceImpl extends BaseServiceImpl<TerminalSmsBalanceHistoryMapper, TerminalSmsBalanceHistoryEntity> implements TerminalSmsBalanceHistoryService {


    @Override
    public Page<TerminalSmsBalanceHistoryEntity> getYesterdayTerminalSmsBalance(int current, int size) {
        Page<TerminalSmsBalanceHistoryEntity> page = new Page<>(current, size);
        QueryWrapper<TerminalSmsBalanceHistoryEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.ge("balance_date", LocalDateTime.now().minusDays(1).toLocalDate().atStartOfDay());
        return baseMapper.selectPage(page, queryWrapper);
    }
}
