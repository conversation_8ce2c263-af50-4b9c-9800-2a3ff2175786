package com.joinus.thirdparty.service;


import com.joinus.common.model.OperatorVO;
import com.joinus.common.model.UploadAuthResultParam;
import com.joinus.common.model.param.CreateGroupParam;
import com.joinus.dao.TerminalEntity;
import com.joinus.thirdparty.model.param.*;
import com.joinus.thirdparty.model.result.TerminalInfoResult;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;
import java.util.List;

public interface TerminalService {


    TerminalEntity    getAllTerminalByTerminalNum(String terminalNum);

    void uninstallTerminal(TerminalEntity terminalEntity, UninstallTerminalParam uninstallTerminalParam);

    void moveTerminal(MoveTerminalParam moveTerminalParam, TerminalEntity terminalEntity);

    void addInstallTerminal(AddInstallTerminalParam addInstallTerminalParam, TerminalEntity terminalEntity);

    void updateParentExistStatus(long parentTerminalId);


    void addTerminalService(TerminalServiceParam terminalServiceParam, TerminalEntity terminalEntity);


    TerminalInfoResult getActiveTerminalInfo(TerminalParam terminalParam);

    TerminalInfoResult getTerminalOnlineInfo(TerminalParam terminalParam);


}
