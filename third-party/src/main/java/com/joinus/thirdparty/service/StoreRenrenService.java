package com.joinus.thirdparty.service;

import com.joinus.thirdparty.model.param.StoreBusinessPointsExcel;
import com.joinus.thirdparty.model.param.StoreBusinessPointsParam;
import com.joinus.thirdparty.model.result.StoreRenrenBasicInfoResult;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.util.List;

public interface StoreRenrenService {
    Long getMemberIdByMobille(String mobile) throws UnsupportedEncodingException, Exception;

    StoreRenrenBasicInfoResult getBasicInfoByMemberId(Long memberId) throws UnsupportedEncodingException, Exception;

    StoreRenrenBasicInfoResult changeBusinessPoints(Long memberId, StoreBusinessPointsParam param) throws UnsupportedEncodingException, Exception;

    String importBusinessPoints(MultipartFile file) throws IOException;

    List<StoreBusinessPointsExcel> downloadExcel(String uuid) throws FileNotFoundException;
}
