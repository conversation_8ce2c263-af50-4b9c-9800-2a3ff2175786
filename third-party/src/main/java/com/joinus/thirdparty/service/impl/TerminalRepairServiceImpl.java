package com.joinus.thirdparty.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.common.mapper.TerminalServiceMapper;
import com.joinus.dao.*;
import com.joinus.thirdparty.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class TerminalRepairServiceImpl extends ServiceImpl<TerminalServiceMapper, TerminalServiceEntity> implements TerminalRepairService {


    @Override
    public void saveTerminalRepair(TerminalServiceEntity terminalEntity) {
        this.save(terminalEntity);
    }
}
