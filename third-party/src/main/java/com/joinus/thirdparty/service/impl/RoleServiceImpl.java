package com.joinus.thirdparty.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.joinus.common.BaseServiceImpl;
import com.joinus.common.model.enums.ActiveEnum;
import com.joinus.dao.RoleEntity;
import com.joinus.dao.TeacherEntity;
import com.joinus.dao.enums.IjxRoleEnum;
import com.joinus.dao.mapper.RoleMapper;
import com.joinus.dao.mapper.TeacherMapper;
import com.joinus.thirdparty.service.RoleService;
import com.joinus.thirdparty.service.TeacherService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;


@Service
@Slf4j
public class RoleServiceImpl extends BaseServiceImpl<RoleMapper, RoleEntity> implements RoleService {

    @Override
    public RoleEntity getRoleBySchoolIdAndRoleName(Long schoolId, String roleName, IjxRoleEnum roleType) {
        LambdaQueryWrapper<RoleEntity> wrapper = Wrappers.lambdaQuery(RoleEntity.class)
                .eq(RoleEntity::getSchoolId, schoolId)
                .eq(RoleEntity::getRoleType, roleType)
                .eq(RoleEntity::getRoleName, roleName);
        return this.baseMapper.selectOne(wrapper);
    }

    @Override
    public RoleEntity getRoleBySchoolIdAndRoleType(Long schoolId, Integer roleType) {
        LambdaQueryWrapper<RoleEntity> wrapper = Wrappers.lambdaQuery(RoleEntity.class)
                .eq(RoleEntity::getSchoolId, schoolId)
                .eq(RoleEntity::getRoleType, roleType);
        return this.baseMapper.selectOne(wrapper);
    }
}
