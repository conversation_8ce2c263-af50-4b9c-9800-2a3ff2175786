package com.joinus.thirdparty.service.impl;

import com.joinus.common.BaseServiceImpl;
import com.joinus.common.mapper.DictionaryMapper;
import com.joinus.dao.DictionaryEntity;
import com.joinus.thirdparty.service.DictionaryService;
import org.springframework.stereotype.Service;


/**
* @description 针对表【sys_dict】的数据库操作Service实现
* @createDate 2024-5-27 11:45:47
*/
@Service
public class DictionaryServiceImpl extends BaseServiceImpl<DictionaryMapper, DictionaryEntity>
    implements DictionaryService {


}




