package com.joinus.thirdparty.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.joinus.common.model.enums.StudentActiveTypeEnum;
import com.joinus.dao.ClassEntity;
import com.joinus.dao.SchoolEntity;
import com.joinus.thirdparty.mapper.impl.ClassMapperImpl;
import com.joinus.thirdparty.service.ClassService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
* 班级表 服务实现类
* <AUTHOR> anpy
* @create 2023/10/19 9:26
*/
@Service("ClassServiceImpl")
public class ClassServiceImpl implements ClassService {

    @Resource
    private ClassMapperImpl classMapperImpl;

    @Override
    public ClassEntity getOrAddClasses(Long gradeId, Long classId, SchoolEntity school) {
        ClassEntity classEntity;
        if (ObjectUtil.isNotEmpty(classId)) {
            //指定了班级
            classEntity = classMapperImpl.findClassByIdAndGradeId(gradeId, classId);
            if (ObjectUtil.isNull(classEntity)) {
                //年级班级不匹配，说明调年级，查目标年级的待分配班级
                classEntity = getAllotClazz(gradeId, school);
            }
        } else {
            //没有指定班级,查待分配班级
            classEntity = getAllotClazz(gradeId, school);
        }
        return classEntity;
    }

    @Override
    public List<ClassEntity> loadClassList(Long gradeId) {
        return classMapperImpl.findClassByClassNameAndGradeId(null, gradeId);
    }

    private ClassEntity getAllotClazz(Long gradeId, SchoolEntity school) {
        ClassEntity clazz;
        List<ClassEntity> allotClazzList = classMapperImpl.findClassByClassNameAndGradeId("待分配班级", gradeId);
        if (CollUtil.isEmpty(allotClazzList)) {
            //没有则新增待分配班级
            clazz = new ClassEntity();
            clazz.setSchoolId(school.getId());
            clazz.setSchoolName(school.getSchoolName());
            clazz.setGradeId(gradeId);
            clazz.setClassName("待分配班级");
            clazz.setIsActive(StudentActiveTypeEnum.NORMAL.getIsActive());
            classMapperImpl.addClass(clazz);
        } else {
            clazz = allotClazzList.get(0);
        }
        return clazz;
    }
}
