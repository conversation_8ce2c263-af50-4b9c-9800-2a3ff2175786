package com.joinus.thirdparty.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.joinus.common.BaseServiceImpl;
import com.joinus.dao.RoleEntity;
import com.joinus.dao.SchoolEntity;
import com.joinus.dao.SchoolImagesEntity;
import com.joinus.dao.TeacherEntity;
import com.joinus.dao.enums.IjxRoleEnum;
import com.joinus.dao.mapper.SchoolMapper;
import com.joinus.thirdparty.model.result.SchoolInfoResult;
import com.joinus.thirdparty.service.RoleService;
import com.joinus.thirdparty.service.SchoolImagesService;
import com.joinus.thirdparty.service.SchoolService;
import com.joinus.thirdparty.service.TeacherService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
@Slf4j
@AllArgsConstructor
public class SchoolServiceImpl extends BaseServiceImpl<SchoolMapper, SchoolEntity> implements SchoolService {
    @Autowired
    private final SchoolImagesService schoolImgsService;
    @Autowired
    private TeacherService  teacherService;
    @Autowired
    private RoleService roleService;
    @Override
    public SchoolInfoResult getSchoolInfo(Long schoolId) {
        SchoolInfoResult schoolInfoResult = new SchoolInfoResult();
        SchoolEntity schoolEntity = this.baseMapper.selectById(schoolId);
        if(ObjectUtil.isEmpty(schoolEntity) || schoolEntity.getIsActive() != 1){
            return schoolInfoResult;
        }
        List<SchoolImagesEntity> schoolImageList = schoolImgsService.getSchoolImages(schoolId);
        if (CollUtil.isNotEmpty(schoolImageList)) {
            schoolInfoResult.setSchoolImg(schoolImageList.get(0).getImageUrl());
        }

        RoleEntity role = roleService.getRoleBySchoolIdAndRoleType(schoolId, IjxRoleEnum.ADMINISTRATOR.getType());
        if(ObjectUtil.isEmpty(role)){
            return schoolInfoResult;
        }
        List<TeacherEntity> teacherList = teacherService.getTeacherByRoleId(role.getId());
        if(CollUtil.isEmpty(teacherList)){
            return schoolInfoResult;
        }
        TeacherEntity teacherEntity = teacherList.get(0);
        schoolInfoResult.setAdminName(teacherEntity.getTeacherName());
        schoolInfoResult.setAdminPhone(teacherEntity.getTelNum());
        return schoolInfoResult;
    }
}
