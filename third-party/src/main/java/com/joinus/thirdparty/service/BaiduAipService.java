package com.joinus.thirdparty.service;

import com.joinus.thirdparty.model.param.BaiduRegisterParam;
import com.joinus.thirdparty.model.result.BaiduFeatureResult;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR> anpy
 * @create 2023/8/23 8:34
 */
public interface BaiduAipService {

    /**
     * 注册百度人脸特征码
     */
    BaiduFeatureResult registerBaiduFaceFeature(MultipartFile imageFile, BaiduRegisterParam baiduRegisterParam);

}
