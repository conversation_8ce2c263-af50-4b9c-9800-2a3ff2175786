package com.joinus.thirdparty.mapper.impl;

import cn.hutool.core.util.StrUtil;
import com.joinus.common.BaseServiceImpl;
import com.joinus.common.model.enums.StudentActiveTypeEnum;
import com.joinus.dao.GradeEntity;
import com.joinus.thirdparty.mapper.GradeMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* 年级表 服务实现类
* <AUTHOR> anpy
* @create 2023/10/19 9:14
*/
@Service("GradeMapperImpl")
public class GradeMapperImpl extends BaseServiceImpl<GradeMapper, GradeEntity> {

    /**
     * 根据学校id、年级名称获取年级列表
     * @param schoolId 学校id
     * @param gradeName 年级名称
     */
    public List<GradeEntity> loadGradeList(Long schoolId, String gradeName) {
        return lambdaQuery()
                .eq(GradeEntity::getSchoolId, schoolId)
                .eq(StrUtil.isNotEmpty(gradeName), GradeEntity::getGradeName, gradeName)
                .eq(GradeEntity::getIsActive, StudentActiveTypeEnum.NORMAL.getIsActive())
                .list();
    }

}
