package com.joinus.thirdparty.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.joinus.dao.ExternalTerminalEntity;
import com.joinus.thirdparty.model.po.StudentInfoPO;
import com.joinus.thirdparty.model.po.ZhongLiangStudentInfoPO;
import org.apache.ibatis.annotations.Param;

/**
 * 三方平台设备
 *
 * <AUTHOR> anpy
 * @create 2023/8/24 13:49
 */
public interface ExternalTerminalMapper extends BaseMapper<ExternalTerminalEntity> {

    /**
     * 根据售货机deviceNum获取全量学生
     *
     * @param pageParams current:第几页
     *                   size：请求数量
     * @param deviceNum  : 售货机设备编号
     * @return 单个学校有百度人脸特征id的全量学生
     */
    IPage<StudentInfoPO> loadStudentByDeviceNum(IPage<StudentInfoPO> pageParams, @Param("deviceNum") String deviceNum);

    /**
     * 根据售货机deviceNum获取增量学生
     *
     * @param pageParams current:第几页
     *                   size：请求数量
     * @param deviceNum  : 售货机设备编号
     * @param updateTime     增量时间
     * @return 单个学校有百度人脸特征id的minute分钟之内的增量学生
     */
    IPage<StudentInfoPO> loadStudentByDeviceNumIncrement(IPage<StudentInfoPO> pageParams, @Param("deviceNum") String deviceNum, @Param("updateTime") String updateTime);

    IPage<ZhongLiangStudentInfoPO> loadZhongLiangStudentByDeviceNum(IPage<ZhongLiangStudentInfoPO> pageParams, @Param("deviceNum") String deviceNum);

    IPage<ZhongLiangStudentInfoPO> loadZhongLiangStudentByDeviceNumIncrement(IPage<ZhongLiangStudentInfoPO> pageParams, @Param("deviceNum") String deviceNum, @Param("updateTime") String updateTime);

}
