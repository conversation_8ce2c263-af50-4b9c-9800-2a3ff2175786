package com.joinus.thirdparty.mapper.impl;

import cn.hutool.core.util.StrUtil;
import com.joinus.common.BaseServiceImpl;
import com.joinus.dao.ClassEntity;
import com.joinus.thirdparty.mapper.ClassMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* 班级表 服务实现类
* <AUTHOR> anpy
* @create 2023/10/19 9:27
*/
@Service("ClassMapperImpl")
public class ClassMapperImpl extends BaseServiceImpl<ClassMapper, ClassEntity> {

    /**
     * 根据年级id和班级id查找班级
     */
    public ClassEntity findClassByIdAndGradeId(Long gradeId, Long classId) {
        return lambdaQuery()
                .eq(ClassEntity::getId, classId)
                .eq(ClassEntity::getGradeId, gradeId)
                .one();
    }

    /**
     * 根据年级id和班级名称查找班级
     */
    public List<ClassEntity> findClassByClassNameAndGradeId(String className, Long gradeId) {
        return lambdaQuery()
                .eq(StrUtil.isNotEmpty(className), ClassEntity::getClassName, className)
                .eq(ClassEntity::getGradeId, gradeId)
                .list();
    }

    public void addClass(ClassEntity classEntity) {
        save(classEntity);
    }

}
