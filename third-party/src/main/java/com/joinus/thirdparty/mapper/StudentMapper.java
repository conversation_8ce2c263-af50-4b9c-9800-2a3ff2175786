package com.joinus.thirdparty.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.joinus.common.model.param.StudentCardInfoParam;
import com.joinus.common.model.param.StudentLeaveParam;
import com.joinus.dao.StudentEntity;
import com.joinus.thirdparty.model.param.StudentParam;
import com.joinus.thirdparty.model.po.StudentCardInfoPO;
import com.joinus.thirdparty.model.po.StudentInfoPO;
import com.joinus.thirdparty.model.po.StudentLeaveInfoPO;
import com.joinus.thirdparty.model.result.DormInfoResult;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR> anpy
 * @create 2023/8/25 10:35
 */
public interface StudentMapper extends BaseMapper<StudentEntity> {




    /**
     *
     *
     * @param pageParams 分页信息
     * @return 学生刷卡数据
     */
    IPage<StudentCardInfoPO> getTodayStudentCardInfoBySchoolId(IPage<StudentCardInfoPO> pageParams, @Param("param") StudentCardInfoParam param);

    /**
     *
     *
     * @param pageParams 分页信息
     * @return 学生刷卡数据
     */
    IPage<StudentCardInfoPO> getHisStudentCardInfoBySchoolId(IPage<StudentCardInfoPO> pageParams, @Param("param") StudentCardInfoParam param);


    /**
     * 同步学生请假
     *
     * @param pageParams 分页信息
     * @return 同步学生请假
     */
    IPage<StudentLeaveInfoPO> getStudentLeavesInfoBySchoolId(IPage<StudentLeaveInfoPO> pageParams, @Param("param") StudentLeaveParam param);


    /**
     * 同步学生
     *
     * @param pageParams 分页信息
     * @return 学生数据
     */
    IPage<StudentInfoPO> getStudentsBySchoolId(IPage<StudentInfoPO> pageParams, Long schoolId);

    /**
     * 同步学生
     *
     * @param pageParams 分页信息
     * @return 学生数据
     */
    IPage<StudentInfoPO> getIncrementSyncStudent(IPage<StudentInfoPO> pageParams, @Param("updateTime") String updateTime,  @Param("schoolId")Long schoolId);

    /**
     * 四十四中图书馆同步学生
     *
     * @param pageParams 分页信息
     * @return 学生数据
     */
    IPage<StudentCardInfoPO> syncLibraryStudent(IPage<StudentCardInfoPO> pageParams);

    /**
     * 四十四中图书馆增量同步学生
     *
     * @param pageParams 分页信息
     * @param updateTime 学生更新时间
     * @return 学生数据
     */
    IPage<StudentCardInfoPO> incrementSyncLibraryStudent(IPage<StudentCardInfoPO> pageParams, @Param("updateTime") String updateTime);

    List<StudentEntity> findStudentsByParam(@Param("param") StudentParam studentParam);

    List<DormInfoResult> getDormsBySchoolId( Long schoolId);
    IPage<StudentEntity> findStudentsByParam(IPage<StudentEntity> pageParams, @Param("param") StudentParam studentParam);
}
