package com.joinus.thirdparty.config;

import com.joinus.thirdparty.interceptor.ExternalInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * <p> Title: WebConfig </p>
 * <p> Description: Web模块主配置类 </p>
 * <p> Copyright: Copyright (c) 郑州家音顺达通讯有限公司 2022 </p>
 *
 * <AUTHOR>
 * @date 创建日期：2024-4-18 15:23:40
 */

@Configuration
@ComponentScan("com.joinus.thirdparty.interceptor")
public class WebConfig implements WebMvcConfigurer {

    @Autowired
    private ExternalInterceptor externalInterceptor;
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(externalInterceptor).addPathPatterns("/studentCqController/**").excludePathPatterns("/druid/**",
                "/*.ico", "/*/api-docs", "/swagger**", "/swagger-resources/**", "/webjars/**", "/configuration/**");
    }
}
