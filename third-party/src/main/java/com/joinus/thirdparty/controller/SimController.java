package com.joinus.thirdparty.controller;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.joinus.common.common.BaseController;
import com.joinus.common.model.response.ApiResult;
import com.joinus.common.model.response.Result;
import com.joinus.dao.TerminalEntity;
import com.joinus.thirdparty.constant.Constants;
import com.joinus.thirdparty.model.param.*;
import com.joinus.thirdparty.model.result.SimInfoResult;
import com.joinus.thirdparty.model.result.SimLogResult;
import com.joinus.thirdparty.model.result.TerminalInfoResult;
import com.joinus.thirdparty.service.SimService;
import com.joinus.thirdparty.service.TerminalService;
import com.joinus.thirdparty.util.RedisUtil;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

@RestController
@Api(value = "/sim", tags = "sim卡")
@RequestMapping("/sim")
@Slf4j
public class SimController extends BaseController {

    @Autowired
    private SimService simService;

    @GetMapping("/ledgers")
    public ApiResult<SimInfoResult> getSimDetails(@Valid  SimParam simParam) {
        log.info("接收到飞书请求sim卡相关的参数:{}", simParam);
        return ApiResult.success(simService.getSimDetails(simParam));
    }

    @GetMapping("/log")
    public ApiResult<SimLogResult> getSimLogs(@Valid  SimParam simParam) {
        log.info("接收到飞书请求simLog相关的参数:{}", simParam);
        return ApiResult.success(simService.getSimLogs(simParam));
    }
}
