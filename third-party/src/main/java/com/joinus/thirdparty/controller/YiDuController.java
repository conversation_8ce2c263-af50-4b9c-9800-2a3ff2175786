package com.joinus.thirdparty.controller;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.joinus.common.common.BaseController;
import com.joinus.common.common.ResultVO;
import com.joinus.common.model.response.CommonResponse;
import com.joinus.dao.ClassEntity;
import com.joinus.dao.GradeEntity;
import com.joinus.dao.StudentEntity;
import com.joinus.thirdparty.model.param.YiDuStudentListParam;
import com.joinus.thirdparty.model.result.YiDuStudentResult;
import com.joinus.thirdparty.service.ClassService;
import com.joinus.thirdparty.service.GradeService;
import com.joinus.thirdparty.service.StudentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

@Slf4j
@RestController
@Api(value = "/yidu", tags = "益读图书馆相关接口")
@RequestMapping("/yidu")
public class YiDuController extends BaseController {

    /**
     * 益读图书学校白名单
     */
    @Value("${yidu-school-white}")
    private String yiDuSchoolWhite;
    @Resource
    private GradeService gradeService;
    @Resource
    private ClassService classService;
    @Resource
    private StudentService studentService;

    private void verifySchoolId(Long schoolId) {
        CommonResponse.ERROR.assertNotNull(schoolId, "学校id不能为空");
        CommonResponse.ERROR.assertNotEmpty(yiDuSchoolWhite, "学校白名单不能为空");
        String[] schoolIds = yiDuSchoolWhite.split(",");
        if (!Arrays.asList(schoolIds).contains(schoolId.toString())) {
            CommonResponse.assertError("该学校无权限");
        }
    }

    @ApiOperation(value = "获取年级列表", notes = "获取年级列表")
    @GetMapping("/grade/list")
    public ResultVO yiDuGradeList(Long schoolId) {
        verifySchoolId(schoolId);
        CommonResponse.ERROR.assertNotNull(schoolId, "学校ID不能为空");
        List<GradeEntity> gradeEntities = gradeService.loadGradeList(schoolId);
        return ResultVO.ok().putData("gradeList", gradeEntities);
    }

    @ApiOperation(value = "获取班级列表", notes = "获取班级列表")
    @GetMapping("/class/list")
    public ResultVO yiDuClassList(Long schoolId, Long gradeId) {
        verifySchoolId(schoolId);
        CommonResponse.ERROR.assertNotNull(gradeId, "年级ID不能为空");
        List<ClassEntity> classEntities = classService.loadClassList(gradeId);
        return ResultVO.ok().putData("classList", classEntities);
    }

    @ApiOperation(value = "获取学生列表", notes = "获取学生列表")
    @GetMapping("/student/list")
    public ResultVO yiDuStudentList(YiDuStudentListParam yiduStudentListParam) {
        verifySchoolId(yiduStudentListParam.getSchoolId());
        IPage<YiDuStudentResult> studentList = studentService.loadYiDuStudentList(yiduStudentListParam);
        return ResultVO.ok(studentList);
    }

    @ApiOperation(value = "根据照片获取学生信息", notes = "根据照片获取学生信息")
    @GetMapping("/student/detailByPhoto")
    public ResultVO featureAndCompare(Long schoolId, String photoUrl) {
        verifySchoolId(schoolId);
        CommonResponse.ERROR.assertNotEmpty(photoUrl, "学生照片不能为空");
        StudentEntity studentEntity = studentService.loadStudentByImg(schoolId, photoUrl);
        if (studentEntity == null) {
            return ResultVO.error("未找到学生");
        }
        return ResultVO.ok().putData("student", BeanUtil.copyProperties(studentEntity, YiDuStudentResult.class));
    }
}
