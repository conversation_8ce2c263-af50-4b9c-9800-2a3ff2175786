package com.joinus.thirdparty.controller;

import com.joinus.common.common.ResultVO;
import com.joinus.common.model.response.ApiResult;
import com.joinus.thirdparty.model.param.BaiduRegisterParam;
import com.joinus.thirdparty.model.result.BaiduFeatureResult;
import com.joinus.thirdparty.service.BaiduAipService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR> anpy
 * @create 2023/8/21 15:58
 */

@Slf4j
@Validated
@RestController
@RequestMapping("/school-vending/baidu/aip")
@Api(value = "/api-external/baidu/aip", tags = "百度人脸识别")
public class BaiduAipController {

    @Resource
    private BaiduAipService baiduAipService;

    @PostMapping(value = "/register", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperation(value = "注册人脸", response = ResultVO.class)
    @ResponseBody
    public ApiResult<BaiduFeatureResult.DataDTO> registerFaceFeature(@Valid BaiduRegisterParam baiduRegisterParam) {
        log.info("注册人脸参数 ：{}", baiduRegisterParam);
        BaiduFeatureResult baiduTokenResult = baiduAipService.registerBaiduFaceFeature(baiduRegisterParam.getImageFile(), baiduRegisterParam);
        if (baiduTokenResult != null) {
            return ApiResult.success(baiduTokenResult.getData().get(0));
        }
        return ApiResult.failed("注册人脸特征码失败");
    }

}
