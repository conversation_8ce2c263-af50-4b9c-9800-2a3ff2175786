package com.joinus.thirdparty.controller;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.joinus.common.common.BaseController;
import com.joinus.common.model.response.ApiResult;
import com.joinus.common.model.response.Result;
import com.joinus.dao.TerminalEntity;
import com.joinus.thirdparty.constant.Constants;
import com.joinus.thirdparty.model.param.AddInstallTerminalParam;
import com.joinus.thirdparty.model.param.MoveTerminalParam;
import com.joinus.thirdparty.model.param.TerminalParam;
import com.joinus.thirdparty.model.param.UninstallTerminalParam;
import com.joinus.thirdparty.model.result.TerminalInfoResult;
import com.joinus.thirdparty.service.TerminalService;
import com.joinus.thirdparty.util.RedisUtil;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

@RestController
@Api(value = "/terminal", tags = "设备")
@RequestMapping("/terminal")
@Slf4j
public class TerminalController extends BaseController {

    @Resource
    private TerminalService terminalService;
    @Resource
    private RedisUtil redisUtil;

    @PostMapping("/uninstall")
    public Object uninstallTerminal(@Valid @RequestBody UninstallTerminalParam uninstallTerminalParam ) {
        log.info("接收到飞书请求卸载的参数:{}", uninstallTerminalParam);
        try {
            if (StrUtil.isNotBlank((String) redisUtil.get(Constants.REDIS_FEI_SHU_KEY_PREFIX + uninstallTerminalParam.getRecordId()))) {
                log.info("卸载,不允许重复调用:{}", uninstallTerminalParam);
                return Result.error("卸载,不允许重复调用!");
            }
            redisUtil.set(Constants.REDIS_FEI_SHU_KEY_PREFIX + uninstallTerminalParam.getRecordId(), "0", Constants.REDIS_INTERVAL_TIME);
            if(ObjectUtil.isEmpty( uninstallTerminalParam.getTerminalNum()) ||ObjectUtil.isEmpty(uninstallTerminalParam.getFeiShuUserIds()) ){
                return Result.error("参数不允许为空!");
            }
            TerminalEntity terminal = terminalService.getAllTerminalByTerminalNum(uninstallTerminalParam.getTerminalNum());
            if (ObjectUtil.isEmpty(terminal)) {
                log.info("未查询到设备,设备号:{}", uninstallTerminalParam.getTerminalNum());
                return Result.success("设备卸载成功!");
            }
            //装卸设备
            terminalService.uninstallTerminal(terminal,uninstallTerminalParam);

        } catch (Exception e) {
            e.printStackTrace();
            redisUtil.del(Constants.REDIS_FEI_SHU_KEY_PREFIX + uninstallTerminalParam.getRecordId());
            log.error("卸载设备失败,设备号:{},:{}", uninstallTerminalParam.getTerminalNum(), e.getMessage());
            return Result.error("设备卸载失败!");
        }
        return Result.success("设备卸载成功!");
    }

    @PostMapping("/move")
    public Object moveTerminal(@Valid @RequestBody MoveTerminalParam moveTerminalParam) {
        log.info("接收到飞书请求移机的参数:{}", moveTerminalParam);
        try {
            if (StrUtil.isNotBlank((String) redisUtil.get(Constants.REDIS_FEI_SHU_KEY_PREFIX + moveTerminalParam.getRecordId()))) {
                log.info("移机,不允许重复调用:{}", moveTerminalParam);
                return Result.error("移机,不允许重复调用!");
            }
            redisUtil.set(Constants.REDIS_FEI_SHU_KEY_PREFIX + moveTerminalParam.getRecordId(), "0", Constants.REDIS_INTERVAL_TIME);
            if (ObjectUtil.isEmpty(moveTerminalParam.getTerminalNum()) || ObjectUtil.isEmpty(moveTerminalParam.getFeiShuUserIds())) {
                return Result.error("参数不允许为空!");
            }
            TerminalEntity terminal = terminalService.getAllTerminalByTerminalNum(moveTerminalParam.getTerminalNum());
            if (ObjectUtil.isEmpty(terminal)) {
                log.info("未查询到设备,设备号:{}", moveTerminalParam.getTerminalNum());
                return Result.success("设备移机失败!");
            }
            //移机设备
            terminalService.moveTerminal(moveTerminalParam,terminal);

        } catch (Exception e) {
            e.printStackTrace();
            redisUtil.del(Constants.REDIS_FEI_SHU_KEY_PREFIX + moveTerminalParam.getRecordId());
            log.error("移机设备失败,设备号:{},:{}", moveTerminalParam.getTerminalNum(), e.getMessage());
            return Result.error("设备移机失败!");
        }
        return Result.success("设备移机成功!");
    }


    @PostMapping("/add-install")
    public Object addInstallTerminal(@Valid @RequestBody AddInstallTerminalParam addInstallTerminalParam) {
        log.info("接收到飞书请求新装/加装的参数:{}", addInstallTerminalParam);
        try {
            if (StrUtil.isNotBlank((String) redisUtil.get(Constants.REDIS_FEI_SHU_KEY_PREFIX + addInstallTerminalParam.getRecordId()))) {
                log.info("新装/加装,不允许重复调用:{}", addInstallTerminalParam);
                return Result.error("新装/加装,不允许重复调用!");
            }
            redisUtil.set(Constants.REDIS_FEI_SHU_KEY_PREFIX + addInstallTerminalParam.getRecordId(), "0", Constants.REDIS_INTERVAL_TIME);
            if (ObjectUtil.isEmpty(addInstallTerminalParam.getTerminalNum()) || ObjectUtil.isEmpty(addInstallTerminalParam.getFeiShuUserIds())) {
                return Result.error("参数不允许为空!");
            }
            TerminalEntity terminal = terminalService.getAllTerminalByTerminalNum(addInstallTerminalParam.getTerminalNum());
            if (ObjectUtil.isEmpty(terminal)) {
                log.info("未查询到设备,设备号:{}", addInstallTerminalParam.getTerminalNum());
                return Result.success("设备新装/加装失败!");
            }
            //新装/加装
            terminalService.addInstallTerminal(addInstallTerminalParam,terminal);

        } catch (Exception e) {
            e.printStackTrace();
            redisUtil.del(Constants.REDIS_FEI_SHU_KEY_PREFIX + addInstallTerminalParam.getRecordId());
            log.error("新装/加装设备失败,设备号:{},:{}", addInstallTerminalParam.getTerminalNum(), e.getMessage());
            return Result.error("设备新装/加装失败!");
        }
        return Result.success("设备新装/加装成功!");
    }

    @GetMapping("/feishu/active-terminal-info")
    public ApiResult<TerminalInfoResult> getActiveTerminalInfo(@Valid  TerminalParam terminalParam) {
        return ApiResult.success(terminalService.getActiveTerminalInfo(terminalParam));
    }

    @GetMapping("/feishu/terminal-online-info")
    public ApiResult<TerminalInfoResult> getTerminalOnlineInfo(@Valid  TerminalParam terminalParam) {
        return ApiResult.success(terminalService.getTerminalOnlineInfo(terminalParam));
    }

}
