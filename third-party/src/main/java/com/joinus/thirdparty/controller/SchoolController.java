package com.joinus.thirdparty.controller;

import cn.hutool.core.util.StrUtil;
import com.joinus.common.model.param.PageParam;
import com.joinus.common.model.param.StudentCardInfoParam;
import com.joinus.common.model.param.StudentLeaveParam;
import com.joinus.common.model.response.ApiResult;
import com.joinus.thirdparty.model.result.*;
import com.joinus.thirdparty.service.SchoolService;
import com.joinus.thirdparty.service.StudentService;
import com.joinus.thirdparty.service.TeacherService;
import com.joinus.thirdparty.util.JwtUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR> anpy
 * @create 2023/10/7 13:52
 */

@Slf4j
@RestController
@Validated
@RequestMapping("/schools")
@Api(value = "/schools", tags = "学校信息")
public class SchoolController {

    @Resource
    private StudentService studentService;

    @Resource
    private TeacherService teacherService;


    @Resource
    private SchoolService schoolService;

    @GetMapping(value = "/students", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperation(value = "学校全量学生同步")
    @ResponseBody
    public ApiResult<StudentInfoResult> getStudents(@RequestHeader("Authorization") String authorizationHeader, @Valid PageParam param) {
        JwtUtil jwtUtil = new JwtUtil();
        Long schoolId = jwtUtil.getSchoolIdByToken(authorizationHeader);
        if (null == schoolId) {
            return ApiResult.failed("通过token获取shoolId失败!");
        }
        return ApiResult.success(studentService.getStudentsBySchoolId(param, schoolId));
    }

    @GetMapping(value = "/students/increment", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperation(value = "学校增量学生同步")
    @ResponseBody
    public ApiResult<StudentInfoResult> incrementSyncStudent(@RequestHeader("Authorization") String authorizationHeader, @Valid PageParam param, String updateTime) {
        if (StrUtil.isEmpty(updateTime)) {
            return ApiResult.failed("更新时间不能为空");
        }
        JwtUtil jwtUtil = new JwtUtil();
        Long schoolId = jwtUtil.getSchoolIdByToken(authorizationHeader);
        if (null == schoolId) {
            return ApiResult.failed("通过token获取shoolId失败!");
        }
        return ApiResult.success(studentService.getIncrementSyncStudent(param, updateTime, schoolId));
    }

    @GetMapping(value = "/students/daily-attendance-records", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperation(value = "按日全量同步学生刷卡信息")
    @ResponseBody
    public ApiResult<StudentCardInfoResult> getStudentCardInfoBySchoolId(@Valid StudentCardInfoParam param) {

        return ApiResult.success(studentService.getStudentCardInfoBySchoolId(param, 101L));
    }

    @GetMapping(value = "/dorms", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperation(value = "全量同步宿舍信息")
    @ResponseBody
    public ApiResult<List<DormInfoResult>> getDormBySchoolId() {

        return ApiResult.success(studentService.getDormsBySchoolId(101L));
    }

    @GetMapping(value = "/students/daily-leaves", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperation(value = "按日全量同步学生请假信息")
    @ResponseBody
    public ApiResult<StudentLeaveInfoResult> getStudentLeavesInfoBySchoolId(@Valid StudentLeaveParam param) {

        return ApiResult.success(studentService.getStudentLeavesInfoBySchoolId(param, 101L));
    }


    @GetMapping(value = "/{schoolId}/teachers/specific-role", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperation(value = "获取学校下特定角色下老师信息")
    @ResponseBody
    public ApiResult<List<TeacherBaseInfoResult>> getSpecificRoleTeachers(@PathVariable("schoolId") Long schoolId) {
        return ApiResult.success(teacherService.getSpecificRoleTeachers(schoolId));
    }

    @GetMapping(value = "/{schoolId}/info", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperation(value = "获取学校信息")
    @ResponseBody
    public ApiResult<SchoolInfoResult> getSchoolInfo(@PathVariable("schoolId") Long schoolId) {
        return ApiResult.success(schoolService.getSchoolInfo(schoolId));
    }
}
