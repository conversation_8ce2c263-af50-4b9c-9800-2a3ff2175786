package com.joinus.thirdparty.controller;

import cn.hutool.http.ContentType;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.joinus.common.common.BaseController;
import com.joinus.common.model.response.ApiResult;
import com.joinus.thirdparty.model.param.FeishuCardSettlementParam;
import com.joinus.thirdparty.service.TerminalService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/fei-shu")
@Slf4j
public class FeishuController {

    @Resource
    private TerminalService terminalService;
    @Value("${server.domain.basic-admin:http://127.0.0.1:9100}")
    private String basicAdminDomain;

    @PostMapping("/card/approval/settlement")
    public ResponseEntity cardApproalOfSettlement(@RequestBody FeishuCardSettlementParam param) {
        HttpResponse response = HttpUtil.createPost(basicAdminDomain + "/api/basic-admin/card/approval/settlement")
                .contentType(ContentType.JSON.getValue())
                .body(JSONUtil.toJsonStr(param))
                .setConnectionTimeout(3 * 1000)
                .setReadTimeout(10 * 1000)
                .execute();
        String body = response.body();
        if (null == response || !response.isOk() || !JSONUtil.isTypeJSON(body)
                || JSONUtil.parseObj(body).getInt("code") != 200) {
            String errMsg = JSONUtil.isTypeJSON(body) ? JSONUtil.parseObj(body).getStr("msg") : "自动结算异常";
            return ResponseEntity.status(300).body(errMsg);
        }
        return ResponseEntity.ok(response.body());
    }
}
