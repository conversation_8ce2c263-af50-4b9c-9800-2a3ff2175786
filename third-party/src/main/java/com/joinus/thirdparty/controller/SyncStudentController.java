package com.joinus.thirdparty.controller;

import cn.hutool.core.util.StrUtil;
import com.joinus.common.common.ResultVO;
import com.joinus.common.model.param.PageParam;
import com.joinus.common.model.response.ApiResult;
import com.joinus.thirdparty.model.param.FaceFeatureFullPageParam;
import com.joinus.thirdparty.model.param.FaceFeatureIncrementPageParam;
import com.joinus.thirdparty.model.result.SyncStudentCardInfoResult;
import com.joinus.thirdparty.model.result.SyncStudentInfoResult;
import com.joinus.thirdparty.model.result.SyncZhongLiangStudentInfoResult;
import com.joinus.thirdparty.service.SchoolVendingService;
import com.joinus.thirdparty.service.StudentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR> anpy
 * @create 2023/10/7 13:52
 */

@Slf4j
@RestController
@Validated
@RequestMapping("/sync/student")
@Api(value = "/sync", tags = "学生同步")
public class SyncStudentController {

    @Resource
    private StudentService studentService;
    @Resource
    private SchoolVendingService schoolVendingService;

    @GetMapping(value = "/library", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperation(value = "四十四中图书馆全量学生同步")
    @ResponseBody
    public ApiResult<SyncStudentCardInfoResult> syncLibraryStudent(@Valid PageParam param) {
        return ApiResult.success(studentService.syncLibraryStudent(param));
    }

    @GetMapping(value = "/library/increment", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperation(value = "四十四中图书馆增量学生同步")
    @ResponseBody
    public ApiResult<SyncStudentCardInfoResult> incrementSyncLibraryStudent(@Valid PageParam param, String updateTime) {
        if (StrUtil.isEmpty(updateTime)) {
            return ApiResult.failed("更新时间不能为空");
        }
        return ApiResult.success(studentService.incrementSyncLibraryStudent(param, updateTime));
    }

    @GetMapping(value = "/vending/face-feature/full/page", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperation(value = "全量获取特征码", response = ResultVO.class)
    @ResponseBody
    public ApiResult<SyncStudentInfoResult> faceFeatureFullPage(@Valid PageParam param, @Valid FaceFeatureFullPageParam messageBoardDetailListParam) {
        return ApiResult.success(schoolVendingService.faceFeatureFullPage(param, messageBoardDetailListParam));
    }

    @GetMapping(value = "/vending/face-feature/increment/page", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperation(value = "增量获取特征码", response = ResultVO.class)
    @ResponseBody
    public ApiResult<SyncStudentInfoResult> faceFeatureIncrementPage(PageParam param, FaceFeatureIncrementPageParam faceFeatureIncrementPageParam) {
        return ApiResult.success(schoolVendingService.faceFeatureIncrementPage(param, faceFeatureIncrementPageParam));
    }


    @GetMapping(value = "/zhongliang/vending/face-feature/full/page", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperation(value = "中粮全量获取特征码", response = ResultVO.class)
    @ResponseBody
    public ApiResult<SyncZhongLiangStudentInfoResult> zhongLiangFaceFeatureFullPage(@Valid PageParam param, @Valid FaceFeatureFullPageParam messageBoardDetailListParam) {
        return ApiResult.success(schoolVendingService.zhongLiangFaceFeatureFullPage(param, messageBoardDetailListParam));
    }

    @GetMapping(value = "/zhongliang/vending/face-feature/increment/page", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperation(value = "中粮增量获取特征码", response = ResultVO.class)
    @ResponseBody
    public ApiResult<SyncZhongLiangStudentInfoResult> zhongLiangFaceFeatureIncrementPage(PageParam param, FaceFeatureIncrementPageParam faceFeatureIncrementPageParam) {
        return ApiResult.success(schoolVendingService.zhongLiangFaceFeatureIncrementPage(param, faceFeatureIncrementPageParam));
    }
}
