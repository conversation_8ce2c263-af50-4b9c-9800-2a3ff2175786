package com.joinus.thirdparty.controller;

import cn.hutool.core.util.StrUtil;
import com.joinus.common.model.response.ApiResult;
import com.joinus.thirdparty.model.result.TeacherInfoResult;
import com.joinus.thirdparty.service.StudentService;
import com.joinus.thirdparty.service.TeacherService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR> maweihua
 * @create 2024-12-24 15:39:20
 */

@Slf4j
@RestController
@Validated
@RequestMapping("/teachers")
@Api(value = "/teachers")
public class TeacherController {

    @Resource
    private TeacherService teacherService;

    @GetMapping(value = "/{telNum}/check-specific-role")
    @ApiOperation(value = "通过手机号校验是否包含特定角色")
    @ResponseBody
    public ApiResult<TeacherInfoResult> checkRole(@PathVariable("telNum") String telNum) {
        return ApiResult.success(teacherService.checkRoleBytelNum(telNum));
    }


}
