package com.joinus.thirdparty.controller;

import cn.hutool.core.util.StrUtil;
import com.joinus.common.common.ResultVO;
import com.joinus.common.model.param.PageParam;
import com.joinus.common.model.response.ApiResult;
import com.joinus.common.model.response.CommonResponse;
import com.joinus.common.model.response.EmptyResult;
import com.joinus.thirdparty.model.param.DeviceParam;
import com.joinus.thirdparty.model.param.FaceFeatureFullPageParam;
import com.joinus.thirdparty.model.param.FaceFeatureIncrementPageParam;
import com.joinus.thirdparty.model.result.SyncStudentCardInfoResult;
import com.joinus.thirdparty.model.result.SyncStudentInfoResult;
import com.joinus.thirdparty.service.SchoolVendingService;
import com.joinus.thirdparty.service.StudentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 外部设备
 * <AUTHOR> anpy
 * @create 2023/10/9 15:59
 */

@Slf4j
@RestController
@Validated
@RequestMapping("/sync/student")
@Api(value = "/sync", tags = "外部设备")
public class ExternalTerminalController {

    @Resource
    private SchoolVendingService schoolVendingService;
    @PostMapping(value = "/deviceNum/bind", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperation(value = "绑定设备编号", response = ResultVO.class)
    @ResponseBody
    public ApiResult<EmptyResult> bindDevice(@RequestBody DeviceParam deviceParam) {
        CommonResponse.ERROR.assertNotEmpty(deviceParam.getDeviceNum(),"订单号不能为空！");
        CommonResponse.ERROR.assertNotNull(deviceParam.getSchoolId(),"学校id不能为空！");
        schoolVendingService.bindDevice(deviceParam);
        return ApiResult.success();
    }

    @PostMapping(value = "/deviceNum/unbind", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperation(value = "解绑设备", response = ResultVO.class)
    @ResponseBody
    public ApiResult<EmptyResult> unbindDevice(String deviceNum) {
        CommonResponse.ERROR.assertNotEmpty(deviceNum,"订单号不能为空！");
        schoolVendingService.unBindDevice(deviceNum);
        return ApiResult.success();
    }
}
