package com.joinus.thirdparty.controller;

import cn.hutool.core.util.StrUtil;
import com.joinus.common.common.ResultVO;
import com.joinus.common.model.enmus.IPayDeviceTypeEnum;
import com.joinus.common.model.param.OrderRefundParam;
import com.joinus.common.model.param.PageParam;
import com.joinus.common.model.response.ApiResult;
import com.joinus.common.model.response.CommonResponse;
import com.joinus.common.model.response.EmptyResult;
import com.joinus.common.model.response.IPayResult;
import com.joinus.thirdparty.model.param.*;
import com.joinus.thirdparty.model.result.OrderResult;
import com.joinus.thirdparty.model.result.SyncStudentInfoResult;
import com.joinus.thirdparty.model.result.SyncZhongLiangStudentInfoResult;
import com.joinus.thirdparty.service.SchoolVendingService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 自动售货机
 *
 * <AUTHOR> anpy
 * @create 2023/8/21 9:33
 */
@Slf4j
@RestController
@Validated
@RequestMapping("/school-vending")
@Api(value = "/school-vending", tags = "自动售货机相关接口")
public class SchoolVendingController {

    @Resource
    private SchoolVendingService schoolVendingService;

    @GetMapping(value = "/face-feature/full/page", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperation(value = "全量获取特征码", response = ResultVO.class)
    @ResponseBody
    public ApiResult<SyncStudentInfoResult> faceFeatureFullPage(@Valid PageParam param, @Valid FaceFeatureFullPageParam messageBoardDetailListParam) {
        return ApiResult.success(schoolVendingService.faceFeatureFullPage(param, messageBoardDetailListParam));
    }

    @GetMapping(value = "/face-feature/increment/page", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperation(value = "增量获取特征码", response = ResultVO.class)
    @ResponseBody
    public ApiResult<SyncStudentInfoResult> faceFeatureIncrementPage(PageParam param, FaceFeatureIncrementPageParam faceFeatureIncrementPageParam) {
        return ApiResult.success(schoolVendingService.faceFeatureIncrementPage(param, faceFeatureIncrementPageParam));
    }

    @PostMapping(value = "/order", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperation(value = "下单支付", response = ResultVO.class)
    @ResponseBody
    public ApiResult<OrderResult> order(@RequestBody OrderParam orderParam) {
        if (StrUtil.isEmpty(orderParam.getDeviceNum())) {
            orderParam.setDeviceNum("1707600008");
        }
        orderParam.setDeviceType(IPayDeviceTypeEnum.SCHOOL_VENDING.getDeviceType().toString());
        IPayResult iPayResult = schoolVendingService.order(orderParam);
        if (iPayResult != null && StrUtil.isNotEmpty(iPayResult.getIjxOrderId())) {
            return ApiResult.success(OrderResult.builder().ijxOrderId(iPayResult.getIjxOrderId()).build());
        }
        return ApiResult.failed("支付失败");
    }

    @PostMapping(value = "/order/refund", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperation(value = "退款", response = ResultVO.class)
    @ResponseBody
    public ApiResult<EmptyResult> orderRefund(@RequestBody OrderRefundParam orderRefundParam) {
        CommonResponse.ERROR.assertNotEmpty(orderRefundParam.getIjxOrderId(), "订单id不能为空");
        if (StrUtil.isEmpty(orderRefundParam.getDeviceNum())) {
            orderRefundParam.setDeviceNum("1707600008");
        }
        schoolVendingService.refundOrder(orderRefundParam);
        return ApiResult.success();
    }

    @PostMapping(value = "/snapshot", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperation(value = "上传快照", response = ResultVO.class)
    @ResponseBody
    public ApiResult<EmptyResult> snapshot(SnapshotParam snapshotParam) {
        CommonResponse.ERROR.assertNotEmpty(snapshotParam.getIjxOrderId(), "订单号不能为空！");
        schoolVendingService.uploadSnapshot(snapshotParam);
        return ApiResult.success();
    }

    @GetMapping(value = "/snapshot", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperation(value = "查看快照", response = ResultVO.class)
    @ResponseBody
    public ApiResult<String> loadSnapshot(String ijxOrderId) {
        CommonResponse.ERROR.assertNotEmpty(ijxOrderId, "订单号不能为空！");
        return ApiResult.success(schoolVendingService.loadSnapshot(ijxOrderId));
    }
}
