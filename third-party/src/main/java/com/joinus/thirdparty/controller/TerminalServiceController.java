package com.joinus.thirdparty.controller;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.joinus.common.common.BaseController;
import com.joinus.common.model.response.Result;
import com.joinus.dao.TerminalEntity;
import com.joinus.thirdparty.constant.Constants;
import com.joinus.thirdparty.model.param.MoveTerminalParam;
import com.joinus.thirdparty.model.param.TerminalServiceParam;
import com.joinus.thirdparty.model.param.UninstallTerminalParam;
import com.joinus.thirdparty.service.TerminalService;
import com.joinus.thirdparty.util.RedisUtil;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

@RestController
@Api(value = "/terminal-service", tags = "维修记录单")
@RequestMapping("/terminal-service")
@Slf4j
public class TerminalServiceController extends BaseController {

    @Resource
    private TerminalService terminalService;
    @Resource
    private RedisUtil redisUtil;

    @PostMapping("/add")
    public Object addTerminalService(@Valid @RequestBody TerminalServiceParam terminalServiceParam ) {
        log.info("接收到飞书请求新增维修记录单的参数:{}", terminalServiceParam);
        try {
            if (StrUtil.isNotBlank((String) redisUtil.get(Constants.REDIS_FEI_SHU_KEY_PREFIX + terminalServiceParam.getRecordId()))) {
                log.info("新增维修记录单,不允许重复调用:{}", terminalServiceParam);
                return Result.error("新增维修记录单,不允许重复调用!");
            }
            redisUtil.set(Constants.REDIS_FEI_SHU_KEY_PREFIX + terminalServiceParam.getRecordId(), "0", Constants.REDIS_INTERVAL_TIME);
            if (ObjectUtil.isEmpty(terminalServiceParam.getTerminalNum()) || ObjectUtil.isEmpty(terminalServiceParam.getFeiShuUserIds())) {
                return Result.error("参数不允许为空!");
            }
            TerminalEntity terminal = terminalService.getAllTerminalByTerminalNum(terminalServiceParam.getTerminalNum());
            if (ObjectUtil.isEmpty(terminal)) {
                log.info("未查询到设备,设备号:{}", terminalServiceParam.getTerminalNum());
                return Result.success("维修记录单新增成功!");
            }
            //装卸设备
            terminalService.addTerminalService(terminalServiceParam, terminal);

        } catch (Exception e) {

            e.printStackTrace();
            log.error("维修记录单新增失败,设备号:{},:{}", terminalServiceParam.getTerminalNum(), e.getMessage());
            return Result.error("维修记录单新增失败!");
        }
        return Result.success("维修记录单新增成功!");
    }

}
