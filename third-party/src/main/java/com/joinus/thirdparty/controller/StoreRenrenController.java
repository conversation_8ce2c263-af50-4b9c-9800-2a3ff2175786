package com.joinus.thirdparty.controller;

import cn.hutool.core.date.DateUtil;
import com.alibaba.excel.EasyExcel;
import com.joinus.common.model.response.ApiResult;
import com.joinus.thirdparty.model.param.StoreBusinessPointsExcel;
import com.joinus.thirdparty.model.param.StoreBusinessPointsParam;
import com.joinus.thirdparty.model.result.StoreRenrenBasicInfoResult;
import com.joinus.thirdparty.service.StoreRenrenService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.List;

@RestController
@Validated
@Slf4j
@RequestMapping("/store/renren")
public class StoreRenrenController {

    @Resource
    private StoreRenrenService storeRenrenService;

    @GetMapping("/member-id")
    public ApiResult getMemberIdByMobille(@NotEmpty(message = "手机号码不能为空") String mobile) throws Exception {
        Long memberId = storeRenrenService.getMemberIdByMobille(mobile);
        return ApiResult.success(memberId);

    }

    @GetMapping("/member/{memberId}")
    public ApiResult getBasicInfoByMemberId(@PathVariable Long memberId) throws Exception {
        StoreRenrenBasicInfoResult result = storeRenrenService.getBasicInfoByMemberId(memberId);
        return ApiResult.success(result);
    }

    @PostMapping("/member/business-points/{memberId}")
    public ApiResult changeBusinessPoints(@PathVariable Long memberId,
                                          @Validated @RequestBody StoreBusinessPointsParam param) throws Exception {
        StoreRenrenBasicInfoResult result = storeRenrenService.changeBusinessPoints(memberId, param);
        return ApiResult.success(result);
    }

    @PostMapping("/member/business-points/import")
    public ResponseEntity importBusinessPoints(MultipartFile file) throws Exception {
        if (file.isEmpty()) {
            return ResponseEntity.status(300).body("文件不能为空");
        }
        // 解析并处理数据，最后添加新的列
        String result = storeRenrenService.importBusinessPoints(file);
        return ResponseEntity.ok(result);
    }

    @GetMapping("/member/business-points/import-result/download")
    public void downloadTemplate(HttpServletResponse response, String uuid) throws IOException {
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode("积分操作结果文件" + DateUtil.now(), "UTF-8");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
        // 传入response的输出流，用于写入文件到响应中
        List<StoreBusinessPointsExcel> storeBusinessPointsExcelList = storeRenrenService.downloadExcel(uuid);

        EasyExcel.write(response.getOutputStream(), StoreBusinessPointsExcel.class)
                .sheet("Sheet1")
                .doWrite(storeBusinessPointsExcelList);
    }
}
