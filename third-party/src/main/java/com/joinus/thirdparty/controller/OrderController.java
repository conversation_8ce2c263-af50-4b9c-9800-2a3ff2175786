package com.joinus.thirdparty.controller;

import cn.hutool.core.util.StrUtil;
import com.joinus.common.common.ResultVO;
import com.joinus.common.model.enmus.IPayDeviceTypeEnum;
import com.joinus.common.model.response.ApiResult;
import com.joinus.common.model.response.IPayResult;
import com.joinus.thirdparty.model.param.OrderParam;
import com.joinus.thirdparty.model.result.OrderResult;
import com.joinus.thirdparty.service.SchoolVendingService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR> anpy
 * @create 2024/5/7 16:43
 */
@Slf4j
@RestController
@RequestMapping("/order")
public class OrderController {

    @Resource
    private SchoolVendingService schoolVendingService;

    @PostMapping(produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperation(value = "下单支付", response = ResultVO.class)
    @ResponseBody
    public ApiResult<OrderResult> order(@RequestBody OrderParam orderParam) {
        if(StrUtil.isEmpty(orderParam.getDeviceType())){
            orderParam.setDeviceType(IPayDeviceTypeEnum.SCHOOL_VENDING.getDeviceType().toString());
        }
        IPayResult iPayResult = schoolVendingService.order(orderParam);
        if (iPayResult != null && StrUtil.isNotEmpty(iPayResult.getIjxOrderId())) {
            return ApiResult.success(OrderResult.builder().ijxOrderId(iPayResult.getIjxOrderId()).build());
        }
        return ApiResult.failed("支付失败");
    }


}
