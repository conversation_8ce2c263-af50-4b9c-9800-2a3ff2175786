
package com.joinus.thirdparty.util;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.TypeReference;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.ContentType;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.hutool.jwt.JWT;
import cn.hutool.jwt.JWTUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.Base64Utils;
import org.springframework.util.DigestUtils;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.*;

@Slf4j
@Component
public class JwtUtil {


  public  Long getSchoolIdByToken(String token){
      Long schoolId = null;
      if (StrUtil.isNotBlank(token)) {
          try {
              JWT jwt = JWTUtil.parseToken(token.replace("Bearer ", ""));
              String clientId = Convert.toStr(jwt.getPayload().getClaim("client_id"));
              String[] clientIds = clientId.split("-");
              schoolId = Convert.toLong(clientIds[2]);
          } catch (Exception e) {
              log.info("转换token 出错,请求token:{},错误原因:{}", token, e.getMessage(), e);
          }
      }
      return schoolId;
  }

}
