package com.joinus.thirdparty.util;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.json.JSONUtil;
import com.google.gson.Gson;
import com.joinus.common.model.response.CommonResponse;
import com.joinus.thirdparty.model.po.StudentInfoPO;
import com.qiniu.common.QiniuException;
import com.qiniu.http.Response;
import com.qiniu.storage.BucketManager;
import com.qiniu.storage.Configuration;
import com.qiniu.storage.Region;
import com.qiniu.storage.UploadManager;
import com.qiniu.storage.model.BatchStatus;
import com.qiniu.storage.model.DefaultPutRet;
import com.qiniu.util.Auth;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URI;
import java.net.URL;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


/**
 * 操作七牛空间文件的工具类
 *
 */
@Component
@Slf4j
public class QiniuUtil {

	// 设置需要操作的账号的AK和SK
	static String ACCESS_KEY = "UEAq301iSGQxYFtbTH0WaVm2zGKCqxg80Qy88hTs";
	static String SECRET_KEY = "GPC8DZuXOi7Y2fOPJr5blm5L1vQwychRXQvU1dr2";
	static Auth auth = Auth.create(ACCESS_KEY, SECRET_KEY);
	static BucketManager bucketManager = new BucketManager(auth,new Configuration());
	static UploadManager uploadManager = new UploadManager(new Configuration());
	static BucketManager bucketManagerHuaDong = new BucketManager(auth,new Configuration(Region.huadong()));
	static BucketManager bucketManagerHuaBei = new BucketManager(auth,new Configuration(Region.huabei()));

	// 创建上传对象
	static String bucket = "ijxapp";
	static String visitingSchoolBucket = "visiting-schools";
	//要上传的空间

	public static final String DOMAIN = "https://img.967111.com/";

	public static final String visitingSchoolDomain = "https://img.fangxiao.top/";

	public static final String  regular ="^(https?://[^/]+)";


	@Getter
	private static String domainToQiNiuYunBucketJson;

	@Value("${domain.to.qi-niu-yun.bucket.json:}")
	public void setDomainToQiNiuYunBucketJson(String domainToQiNiuYunBucketJson) {
		QiniuUtil.domainToQiNiuYunBucketJson = domainToQiNiuYunBucketJson;
	}
	private static String getQiNiuToken() {
		return auth.uploadToken(bucket);
	}
	/**
	 *  拷贝图片
	 * @param url
	 * @return
	 */
	public String copyImg(String url){
		String imgUrl="";
		try{
			String  uuid = UUID.randomUUID().toString();
			String currentDateDay = DateUtil.today();
			bucketManager.copy(bucket, url.replace(DOMAIN,""), bucket, "external/school/"+currentDateDay+"/"+uuid+".jpg");
			Response response = bucketManager.deleteAfterDays(bucket, "external/school/"+currentDateDay+"/"+uuid+".jpg", 1);
			if(response.statusCode==200) {
				imgUrl = DOMAIN + "external/school/" + currentDateDay + "/" + uuid + ".jpg";
			}
		}catch (Exception e){
			log.error(e.getMessage());
		}

		return imgUrl;
	}


	public void batchCopyImg(List<StudentInfoPO> studentList)  {
		try {
			BucketManager.BatchOperations batchOperationsHuaDong = new BucketManager.BatchOperations();
			BucketManager.BatchOperations batchOperationsHuaBei = new BucketManager.BatchOperations();
			Map<String, String> bucketMap = JSONUtil.toBean(domainToQiNiuYunBucketJson, Map.class);
			Pattern pattern =Pattern.compile(regular);
			String currentDateDay = DateUtil.today();
			studentList.forEach(student -> {
				try {
					if (ObjectUtil.isNotEmpty(student.getStudentImg())) {

						Matcher matcher = pattern.matcher(student.getStudentImg());
						if (matcher.find()) {

							String domainName=matcher.group(1);
							String sourceBucket=bucketMap.get(domainName);
							if(ObjectUtil.isNotEmpty(sourceBucket)) {
								String uuid = UUID.randomUUID().toString();
								String targetBucket=bucket;
								String targetDomainName=DOMAIN;
								if (sourceBucket.equals(bucket)) {
									batchOperationsHuaDong.addCopyOp(sourceBucket, student.getStudentImg().replace(domainName + "/", ""), targetBucket, getImagePath("", currentDateDay, uuid));
									batchOperationsHuaDong.addDeleteAfterDaysOps(targetBucket, 1, getImagePath("", currentDateDay, uuid));
								} else {
									targetBucket = visitingSchoolBucket;
									targetDomainName = visitingSchoolDomain;
									batchOperationsHuaBei.addCopyOp(sourceBucket, student.getStudentImg().replace(domainName + "/", ""), targetBucket, getImagePath("", currentDateDay, uuid));
									batchOperationsHuaBei.addDeleteAfterDaysOps(targetBucket, 1, getImagePath("", currentDateDay, uuid));
								}
								student.setStudentImg(getImagePath(targetDomainName,currentDateDay,uuid));
							}else{
								student.setStudentImg("");
							}
						}else{
							student.setStudentImg("");
						}
					}
				} catch (Exception e) {
					e.getMessage();
					log.error(e.getMessage());
				}
			});
			if (StrUtil.isNotBlank(batchOperationsHuaDong.execBucket())) {
				Response responseHuaDong = bucketManagerHuaDong.batch(batchOperationsHuaDong);
				BatchStatus[] batchStatusesHuaDong = responseHuaDong.jsonToObject(BatchStatus[].class);
				for (int i = 0; i < batchStatusesHuaDong.length; i++) {
					BatchStatus status = batchStatusesHuaDong[i];
					if (status.code != 200) {
						log.warn("华东,第" + (i + 1) + "个操作失败, 错误码：" + status.code + "，原因：" + status.data.error);
					}
				}
			}
			if (StrUtil.isNotBlank(batchOperationsHuaBei.execBucket())) {
				Response responseHuaBei = bucketManagerHuaBei.batch(batchOperationsHuaBei);
				BatchStatus[] batchStatusesHuaBei = responseHuaBei.jsonToObject(BatchStatus[].class);
				for (int i = 0; i < batchStatusesHuaBei.length; i++) {
					BatchStatus status = batchStatusesHuaBei[i];
					if (status.code != 200) {
						log.warn("华北第" + (i + 1) + "个操作失败, 错误码：" + status.code + "，原因：" + status.data.error);
					}
				}
			}
		} catch (Exception e) {
			e.getMessage();
			log.error(e.getMessage());
		}


	}


	private String getImagePath(String domainName, String currentDateDay, String uuid) {
		return domainName + "external/school/" + currentDateDay + "/" + uuid + ".jpg";
	}


    public String uploadImg(String url){
		try {
			String key = String.format("ijx-admin-sub-terminal-basic-info%s%s.jpeg",
					DateUtil.format(DateUtil.date(), "/yyyy/MM/dd/HH/mm/ss/"),
					UUID.randomUUID());
			URL imgUrl = new URL(url);
			Response response = uploadManager.put(input2byte(imgUrl.openStream()), key, getQiNiuToken());
			DefaultPutRet putRet = new Gson().fromJson(response.bodyString(), DefaultPutRet.class);
			return DOMAIN + putRet.key;
		} catch (Exception e) {
			e.printStackTrace();
			log.error("快照上传失败,错误信息：{}", e.getMessage());
			//CommonResponse.assertError("快照上传失败");
		}
		return null;
	}

	public static final byte[] input2byte(InputStream inStream)
			throws IOException {
		ByteArrayOutputStream swapStream = new ByteArrayOutputStream();
		byte[] buff = new byte[100];
		int rc = 0;
		while ((rc = inStream.read(buff, 0, 100)) > 0) {
			swapStream.write(buff, 0, rc);
		}
		byte[] in2b = swapStream.toByteArray();
		return in2b;
	}


}
