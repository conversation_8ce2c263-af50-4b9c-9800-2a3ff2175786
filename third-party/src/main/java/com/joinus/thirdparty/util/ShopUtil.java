package com.joinus.thirdparty.util;

import cn.hutool.core.lang.TypeReference;
import cn.hutool.http.ContentType;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Base64Utils;
import org.springframework.util.DigestUtils;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.*;

@Slf4j
public class ShopUtil {

    private static String apiKey = "V55P3W6ot3faL1Ue9zirdjOv316qbxsG";
    private static String path_get_memberid = "/member/detail/get-id-by-mobile";
    private static String path_get_basic = "/member/detail/get-basic";
    private static String path_add_credit = "/member/finance/send-credit";
    private static String domain = "https://store.yunshuguan.cn/wap/3/api/apps/openApi";
    private static String openAppID = "100080";
    private static String openAppApiKey = "q46cOjsU4nLHnJelAjquumZEajSkqJCC";

    public static void main(String[] args) throws Exception {
        String mobile = "18336091139";
//        Long memberId = getMemberIdByMobile(mobile);
        Long memberId = 17261L;
        log.info("memberId {}", memberId);

        JSONObject basicInfo = getBasicInfo(memberId);
        log.info("mobile {} memberId {} basicInfo {}", mobile, memberId, basicInfo);

//        JSONObject jsonParam = new JSONObject().set("member_id", memberId)
//                .set("num", 1)
//                .set("remark", "测试扣减积分")
//                .set("change_type", 2);
//        JSONObject sendCredit = sendCredit(jsonParam);
//        log.info("sendCredit param {} result {}", JSONUtil.toJsonStr(jsonParam), sendCredit);
    }

    public static Long getMemberIdByMobile(String mobile) throws Exception {
        JSONObject getParam = new JSONObject().set("mobile", mobile);
        JSONObject jsonObject = get(path_get_memberid, getParam);
        if (null == jsonObject || 0 != jsonObject.getInt("error")) {
            log.info("获取人人商城id失败 {}", mobile);
            throw new RuntimeException(null == jsonObject ? "获取人人商城会员基本信息失败" : jsonObject.getStr("message"));
        }
        return jsonObject.getLong("member_id");
    }

    public static JSONObject getBasicInfo(Long memberId) throws Exception {
        JSONObject getParam = new JSONObject().set("member_id", memberId);
        JSONObject jsonObject = get(path_get_basic, getParam);
        if (null == jsonObject || 0 != jsonObject.getInt("error")) {
            log.info("获取人人商城会员基本信息失败 {}", memberId);
            throw new RuntimeException(null == jsonObject ? "获取人人商城会员基本信息失败" : jsonObject.getStr("message"));
        }
        return jsonObject.getJSONObject("member");
    }

    public static JSONObject sendCredit(JSONObject jsonParam) throws Exception {
        JSONObject jsonObject = post(path_add_credit, jsonParam);
        if (null == jsonObject || 0 != jsonObject.getInt("error")) {
            log.info("积分增加/扣减失败 {}", jsonParam);
            throw new RuntimeException(null == jsonObject ? "积分增加/扣减失败" : jsonObject.getStr("message"));
        }
        return jsonObject;
    }

    public static JSONObject get(String path, JSONObject paramJson) throws UnsupportedEncodingException {
        String url = domain + path ;
        HashMap<String, String> paramMap = new HashMap<>();
        String timestampStr = timestampStr();
        String nonceStr = randomStr(32);
        paramMap.put("api_key", apiKey);
        paramMap.put("timestamp", timestampStr);
        paramMap.put("open_app_id", openAppID);
        paramMap.put("nonce_str", nonceStr);
        // 业务参数
//        paramMap.put("mobile", "18336091139");
        paramMap.putAll(paramJson.toBean(new TypeReference<Map<String, String>>(){}));
        String sortedParam = kSort2Str(paramMap);

        paramMap.put("sign", md5Str(sortedParam + apiKey + base64Str(timestampStr + apiKey + sortedParam) + nonceStr));
        paramMap.put("open_app_sign", md5Str("913702023503242914" + openAppID + sortedParam + md5Str(timestampStr + openAppApiKey + sortedParam) + nonceStr));
        StringBuilder queryString = new StringBuilder();
        boolean first = true;
        for (Map.Entry<String, String> entry : paramMap.entrySet()) {
            if (!first) {
                queryString.append("&");
            } else {
                first = false;
            }
            queryString.append(URLEncoder.encode(entry.getKey(), "UTF-8"));
            queryString.append("=");
            queryString.append(URLEncoder.encode(entry.getValue(), "UTF-8"));
        }

        url = url + "?" + queryString;
        log.info("人人商城V5 GET start url{}", url);
        HttpResponse response = HttpUtil.createGet(url).execute();
        log.info("人人商城V5 GET responseBody {}", response.body());
        if (response.isOk() && JSONUtil.isTypeJSON(response.body())) {
            return JSONUtil.parseObj(response.body());
        }
        log.warn("人人商城V5 GET error {} {}", response.getStatus(), response.body());
        log.info("人人商城V5 GET end{}", url);
        return null;
    }


    public static JSONObject post(String path, JSONObject paramJson) throws UnsupportedEncodingException {
        String url = domain + path ;
        HashMap<String, String> paramMap = new HashMap<>();
        String timestampStr = timestampStr();
        String nonceStr = randomStr(32);
        paramMap.put("api_key", apiKey);
        paramMap.put("timestamp", timestampStr);
        paramMap.put("open_app_id", openAppID);
        paramMap.put("nonce_str", nonceStr);
        // 业务参数
        //        paramMap.put("mobile", "18336091139");
        paramMap.putAll(paramJson.toBean(new TypeReference<Map<String, String>>(){}));
        String sortedParam = kSort2Str(paramMap);

        paramMap.put("sign", md5Str(sortedParam + apiKey + base64Str(timestampStr + apiKey + sortedParam) + nonceStr));
        paramMap.put("open_app_sign", md5Str("913702023503242914" + openAppID + sortedParam + md5Str(timestampStr + openAppApiKey + sortedParam) + nonceStr));

        log.info("人人商城V5 POST start url {} body {}", url, JSONUtil.toJsonStr(paramMap));
        HashMap<String, Object> newMap = new HashMap<>(paramMap);
        HttpResponse response = HttpUtil.createPost(url)
                .contentType(ContentType.MULTIPART.getValue())
                .form(newMap)
                .execute();
        log.info("人人商城V5 POST responseBody {}", response.body());
        if (response.isOk() && JSONUtil.isTypeJSON(response.body())) {
            return JSONUtil.parseObj(response.body());
        } else {
            log.warn("人人商城V5 POST error {} {}", response.getStatus(), response.body());
        }
        log.info("人人商城V5 POST end{}", url);
        return null;
    }
    /**
     * [args]
     * @return void
     * @description 人人商城demo
     * <AUTHOR>
     * @date 2024/7/18 09:52
     */
    public static void main1(String[] args) throws IOException {
        // 人人商城V5店铺创建的API_KEY
        String apiKey = "V5suyZ45GcSyIURW2kPqVpQyQqC0Vx1z";
        // 开放应用的APP_ID
        String openAppID = "100001";
        // 开放应用的APP_API_KEY
        String openAppApiKey = "Aojtd3ZwUdfuoNu6joJ6DuZ96O6FID9T";
        // 请求接口地址
        String url = "https://yousitedomain.com/wap/3/api/apps/openApi/user/index/check-password";

        // 定义当前时间戳
        String timestamp = timestampStr();
        // 定义请求参数为HashMap
        HashMap<String, String> requestParams = new HashMap<>();
        // 公用请求参数
        requestParams.put("api_key", apiKey);
        requestParams.put("timestamp", timestamp);
        requestParams.put("open_app_id", openAppID);
        requestParams.put("nonce_str", randomStr(32));
        // 业务参数
        requestParams.put("username", "admin");
        requestParams.put("password", "admin");
        // 字典排序后转为字符串
        String queryString = kSort2Str(requestParams);
        // 计算sign中的base64值
        String bas64Str = base64Str(timestamp + apiKey + queryString);
        // 计算open_app_sign中的md5值
        String md5Encode = md5Str(timestamp + openAppApiKey + queryString);
        // 计算签名
        requestParams.put("sign", md5Str(queryString + apiKey + bas64Str + requestParams.get("nonce_str")));
        requestParams.put("open_app_sign",
                md5Str("913702023503242914" + openAppID + queryString + md5Encode + requestParams.get("nonce_str")));
        // 以下开始请求!!! 可以换成自己的httpClient
//        OkHttpClient client = new OkHttpClient().newBuilder().build();
//        MultipartBody.Builder builder = new MultipartBody.Builder().setType(MultipartBody.FORM);
//        for (String key : requestParams.keySet()) {
//            builder.addFormDataPart(key, requestParams.get(key));
//        }
//        RequestBody body = builder.build();
//        Request request = new Request.Builder().url(url).method("POST", body).build();
//        Response response = client.newCall(request).execute();
//        // 打印接口返回结果
//        System.out.println(response.body().string());
    }

    private static String timestampStr() {
        Date date = new Date();
        return String.valueOf(date.getTime() / 1000);
    }

    private static String randomStr(int length) {
        String str = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        Random random = new Random();
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < length; i++) {
            int number = random.nextInt(62);
            sb.append(str.charAt(number));
        }
        return sb.toString();
    }

    private static String kSort2Str(HashMap<String,
            String> params) {
        TreeMap<String, String> paramTreeMap = new TreeMap<>(params);
        StringBuilder str = new
                StringBuilder();
        for (String key : paramTreeMap.keySet()) {
            log.info("============ {} {}", key, paramTreeMap.get(key));
            str.append(key).append("=").append
                    (paramTreeMap.get(key)).append("&");
        }
        return str.substring(0, str.length() - 1);
    }

    private static String md5Str(String str) {
        return
                DigestUtils.md5DigestAsHex(str.getBytes());
    }

    private static String base64Str(String str) {
        return Base64Utils
                .encodeToString(str.getBytes());
    }

}
