package com.joinus.thirdparty.interceptor;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpStatus;
import cn.hutool.http.HttpUtil;
import cn.hutool.http.Method;
import cn.hutool.json.JSONUtil;
import com.qiniu.util.IOUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

@Slf4j
@Component
public class ExternalInterceptor implements HandlerInterceptor {
    @Value("${external.server.domain.name:http://192.168.2.99:8511}")
    private String externalServerDomainName;

    @Override
    public boolean preHandle(HttpServletRequest request, @NotNull HttpServletResponse response, @NotNull Object handler) throws Exception {
        String uri = request.getRequestURI();
        if (uri.contains("/studentCqController")) {

            String token = request.getHeader("Authorization");

            if (StringUtils.isBlank(token)) {
                return write(response, HttpStatus.HTTP_UNAUTHORIZED, "token不可为空！");
            } else {
                String param = request.getQueryString();
                String url = externalServerDomainName + uri.substring(uri.indexOf("/studentCqController"));
                if (ObjectUtil.isNotEmpty(param)) {
                    url += "?" + param;
                }
                byte[] requestBody = IOUtils.toByteArray(request.getInputStream());
                String methodStr = request.getMethod();
                Method method = Method.valueOf(methodStr);
                Map<String, String> headers = new HashMap<>();
                // 获取并复制所有的请求头
                Enumeration<String> headerNames = request.getHeaderNames();
                while (headerNames.hasMoreElements()) {
                    String headerName = headerNames.nextElement();
                    headers.put(headerName, request.getHeader(headerName));
                }
                // 创建和执行新的请求
                HttpResponse httpResponse = HttpUtil.createRequest(method, url)
                        .addHeaders(headers)
                        .body(requestBody)
                        .execute();
                // 获取字符编码
                String charset = httpResponse.charset();
                // 处理新请求的响应
                response.setStatus(httpResponse.getStatus());
                response.setCharacterEncoding(charset);
                response.setContentType(httpResponse.header("Content-Type"));
                // 使用正确的编码写入响应体
                response.getWriter().write(new String(httpResponse.bodyBytes(), charset));
                return false;
            }

        }
        return true;
    }

    /**
     * @param response, code, msg
     * @return boolean
     * @Description 错误TOKEN响应
     * <AUTHOR> @date 2024-4-18 15:28:17
     */
    public static boolean write(HttpServletResponse response, Integer code, String msg) throws IOException {
        response.setContentType("application/json;charset=UTF-8");
        Map<String, Object> modelMap = new LinkedHashMap();
        modelMap.put("code", code.toString());
        modelMap.put("msg", msg);
        modelMap.put("timestamp", System.currentTimeMillis());
        log.info("response===>" + JSONUtil.toJsonStr(modelMap));
        response.getWriter().write(JSONUtil.toJsonStr(modelMap));
        return false;
    }
}