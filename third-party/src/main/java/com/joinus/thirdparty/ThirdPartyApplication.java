package com.joinus.thirdparty;

import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import com.joinus.thirdparty.config.CustomBeanNameGenerator;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;

/**
*
* <AUTHOR> anpy
* @create 2023/8/21 9:28
*/
@MapperScan(basePackages = {"com.joinus.thirdparty.mapper","com.joinus.common.mapper","com.joinus.dao.mapper"},  nameGenerator = CustomBeanNameGenerator.class)
@ComponentScan(basePackages = {"com.joinus.thirdparty","com.joinus.common","com.joinus.dao"})
@EnableApolloConfig
@SpringBootApplication
public class ThirdPartyApplication {

    public static void main(String[] args) {
        SpringApplication.run(ThirdPartyApplication.class, args);
    }

}
