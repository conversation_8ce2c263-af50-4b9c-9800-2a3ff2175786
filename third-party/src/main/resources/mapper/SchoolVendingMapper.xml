<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.thirdparty.mapper.ExternalTerminalMapper">

    <!-- 根据售货机deviceNum获取全量学生   -->
    <select id="loadStudentByDeviceNum" resultType="com.joinus.thirdparty.model.po.StudentInfoPO">
        SELECT ts.id "studentId",
               ts.STUDENT_NAME "studentName",
               tsff.FACE_FEATURE "faceFeature"
        FROM T_STUDENT ts ,T_EXTERNAL_TERMINAL tty,T_STUDENT_FACE_FEATURES tsff
        WHERE tty.SCHOOL_ID = ts.SCHOOL_ID
          and ts.id = tsff.STUDENT_ID
          AND ts.ISACTIVE = 1
          AND tty.DEVICE_NUMBER = #{deviceNum}
          AND tsff.FACE_FEATURE IS NOT NULL
    </select>

    <!-- 根据售货机deviceNum获取增量学生   -->
    <select id="loadStudentByDeviceNumIncrement" resultType="com.joinus.thirdparty.model.po.StudentInfoPO">
        SELECT ts.id "studentId",
               ts.STUDENT_NAME "studentName",
               tsff.FACE_FEATURE "faceFeature"
        FROM T_STUDENT ts ,T_EXTERNAL_TERMINAL tty,T_STUDENT_FACE_FEATURES tsff
        WHERE tty.SCHOOL_ID = ts.SCHOOL_ID
          and ts.id = tsff.STUDENT_ID
          AND tsff.UPDATE_TIME > TO_DATE(#{updateTime}, 'YYYY-MM-DD hh24:mi:ss')
          AND ts.ISACTIVE = 1
          AND tty.DEVICE_NUMBER = #{deviceNum}
          AND tsff.FACE_FEATURE IS NOT NULL
    </select>

    <select id="loadZhongLiangStudentByDeviceNum" resultType="com.joinus.thirdparty.model.po.ZhongLiangStudentInfoPO">
        SELECT ts.id "studentId",
               ts.STUDENT_NAME "studentName",
               tsff.FACE_FEATURE "faceFeature",
               tc.CLASS_NAME "className",
               tg.GRADE_NAME "gradeName",
               tsff.update_time "updateTime"
        FROM T_STUDENT ts ,T_EXTERNAL_TERMINAL tty,T_STUDENT_FACE_FEATURES tsff,T_GRADE tg,T_CLASS tc
        WHERE tty.SCHOOL_ID = ts.SCHOOL_ID
          and tsff.SCHOOL_ID = ts.SCHOOL_ID
          and ts.id = tsff.STUDENT_ID
          AND ts.ISACTIVE = 1
          and ts.CLASS_ID = tc.ID
          and tc.GRADE_ID = tg.ID
          AND tty.DEVICE_NUMBER = #{deviceNum}
          and tty.type = 3
          AND tsff.FACE_FEATURE IS NOT NULL
        order by GRADE_NAME ,CLASS_NAME
    </select>

    <select id="loadZhongLiangStudentByDeviceNumIncrement" resultType="com.joinus.thirdparty.model.po.ZhongLiangStudentInfoPO">
        SELECT ts.id "studentId",
               ts.STUDENT_NAME "studentName",
               tsff.FACE_FEATURE "faceFeature",
               tc.CLASS_NAME,
               tg.GRADE_NAME,
               tsff.update_time "updateTime"
        FROM T_STUDENT ts ,T_EXTERNAL_TERMINAL tty,T_STUDENT_FACE_FEATURES tsff,T_GRADE tg,T_CLASS tc
        WHERE tty.SCHOOL_ID = ts.SCHOOL_ID
          and ts.id = tsff.STUDENT_ID
          AND tsff.UPDATE_TIME > TO_DATE(#{updateTime}, 'YYYY-MM-DD hh24:mi:ss')
          AND ts.ISACTIVE = 1
          and ts.CLASS_ID = tc.ID
          and tc.GRADE_ID = tg.ID
          AND tty.DEVICE_NUMBER = #{deviceNum}
          AND tsff.FACE_FEATURE IS NOT NULL
    </select>

</mapper>