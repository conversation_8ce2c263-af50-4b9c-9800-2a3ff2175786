<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.dao.mapper.TerminalMapper">
    <update id="unstallTerminal">
        update t_terminal
        set school_id=null,
            class_id=null,
            install_address=null,
            install_time=null,
            terminal_status='0',
            white_resert_flag='0',
            soft_flag='0',
            is_login='0',
            status_operate='0',
            parent_terminal_id = -1
        where id =#{id}
    </update>

    <select id="getRootTerminalByChildId" resultType="com.joinus.dao.TerminalEntity">
        SELECT * FROM T_TERMINAL WHERE CONNECT_BY_ISLEAF = 1
            START WITH ID = #{childId}
        CONNECT BY PRIOR PARENT_TERMINAL_ID = ID
    </select>

    <update id="updateRootTerminalFaceDeviceNum">
        update T_TERMINAL
        set FACE_DEVICE_NUMBER =
                (select count(*)
                 from (select id, PARENT_TERMINAL_ID, TERMINAL_NAME, TERMINAL_TYPE, TERMINAL_STATUS, ISACTIVE
                       from T_TERMINAL start with id = #{rootId}
                       connect by PARENT_TERMINAL_ID = prior id) t
                 where TERMINAL_TYPE in (265, 281)
                   and TERMINAL_STATUS in (1, 3)
                   and ISACTIVE = 1)
        where id = #{rootId}
    </update>

</mapper>