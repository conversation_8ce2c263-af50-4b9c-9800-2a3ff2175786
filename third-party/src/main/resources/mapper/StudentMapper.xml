<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.thirdparty.mapper.StudentMapper">

    <select id="getStudentsBySchoolId" resultType="com.joinus.thirdparty.model.po.StudentInfoPO">
        select ts.id           "id",
               ts.STUDENT_NAME "name",
               ts.CARDCODE     "cardCode",
               tg.GRADE_NAME   "gradeName",
               tc.CLASS_NAME   "className",
               ts.sex          "sex",
               ts.student_img  "studentImg",
               ts.isdorm       "isDorm",
               tb.dorm_id      "dormId",
               ts.UPDATE_TIME  "updateTime"
        from T_STUDENT ts
                 inner join T_CLASS tc
                            on ts.CLASS_ID = tc.id
                 inner join T_GRADE tg
                            on tc.GRADE_ID = tg.id
                 LEFT JOIN t_bed tb
                           ON ts.id = tb.student_id
                               AND tb.isactive = 1
        where ts.SCHOOL_ID = #{schoolId}
          and ts.ISACTIVE = 1
        order by ts.id
    </select>

    <select id="getIncrementSyncStudent" resultType="com.joinus.thirdparty.model.po.StudentInfoPO">
        select ts.id           "id",
               ts.STUDENT_NAME "name",
               ts.CARDCODE     "cardCode",
               tg.GRADE_NAME   "gradeName",
               tc.CLASS_NAME   "className",
               ts.sex          "sex",
               ts.student_img  "studentImg",
               ts.isdorm       "isDorm",
               tb.dorm_id      "dormId",
               ts.UPDATE_TIME  "updateTime"
        from T_STUDENT ts
                 inner join T_CLASS tc
                            on ts.CLASS_ID = tc.id
                 inner join T_GRADE tg
                            on tc.GRADE_ID = tg.id
                 LEFT JOIN t_bed tb
                           ON ts.id = tb.student_id
                               AND tb.isactive = 1
        where ts.SCHOOL_ID = #{schoolId}
          and ts.ISACTIVE = 1
          and ts.UPDATE_TIME > TO_DATE(#{updateTime}, 'YYYY-MM-DD hh24:mi:ss')
        order by ts.id
    </select>

    <select id="getTodayStudentCardInfoBySchoolId" resultType="com.joinus.thirdparty.model.po.StudentCardInfoPO">
        select t.id "cardRecordId", ts.id "studentId", t.card_num "cardNum", t.direction, t.card_time
        from T_CARD_SIGNIN_RECORD6 t
        inner join t_student ts
            on t.card_num = ts.cardcode
        where t.save_time >= to_date(#{param.queryDate}, 'yyyy-mm-dd')
          and t.save_time &lt; to_date(#{param.queryDate}, 'yyyy-mm-dd') + 1
          and t.school_id = #{param.schoolId}
          and ts.school_id = #{param.schoolId}
          and t.direction in (0, 1, 2, 5)
          and t.card_num is not null
    </select>

    <select id="getHisStudentCardInfoBySchoolId" resultType="com.joinus.thirdparty.model.po.StudentCardInfoPO">
        select t.id "cardRecordId", ts.id "studentId",t.cardnum "cardNum", t.direction, t.cardtime
        from T_CARD_SIGNIN_RECORD_HIS t
        inner join t_student ts
         on t.cardnum = ts.cardcode
        where t.SAVETIME >= to_date(#{param.queryDate}, 'yyyy-mm-dd')
          and t.SAVETIME &lt; to_date(#{param.queryDate}, 'yyyy-mm-dd') + 1
          and t.SCHOOLID = #{param.schoolId}
          and ts.school_id = #{param.schoolId}
          and t.direction in (0, 1, 2, 5)
          and t.cardnum is not null
    </select>



    <select id="getStudentLeavesInfoBySchoolId" resultType="com.joinus.thirdparty.model.po.StudentLeaveInfoPO">
        SELECT
               t.id,
               t.leaveer_id                                                "studentId",
               t.leaveer_name                                              "studentName",
               t.class_id                                                  "classId",
               tc.class_name                                               "className",
               t.grade_id                                                  "gradeId",
               tg.grade_name                                               "gradeName",
               nvl(nvl(tp.parent_name, tt.teacher_name), t.create_user_id) "leaveName",
               to_char(START_DATE, 'yyyy-mm-dd')                            "startDate",
               to_char(END_DATE, 'yyyy-mm-dd')                              "endDate",
               CASE
                   WHEN IF_IS_CYCLE = 1 THEN
                       START_TIME
                   WHEN IF_IS_CYCLE = 0 AND
                        TO_DATE(#{param.queryDate}, 'YYYY-MM-DD') = START_DATE THEN
                       START_TIME
                   WHEN IF_IS_CYCLE = 0 THEN
                       '00:00'
                   ELSE
                       NULL
                   END AS                                                  "startTime",

               CASE
                   WHEN IF_IS_CYCLE = 1 THEN
                       END_TIME
                   WHEN IF_IS_CYCLE = 0 AND
                        TO_DATE(#{param.queryDate}, 'YYYY-MM-DD') = END_DATE THEN
                       END_TIME
                   WHEN IF_IS_CYCLE = 0 THEN
                       '23:59'
                   ELSE
                       NULL
                   END AS                                                  "endTime",
               t.status                                                    "approvalResult",
               t.reason
        FROM t_vs_leave t
                 left join t_class tc
                           on t.class_id = tc.id
                 left join t_grade tg
                           on t.grade_id = tg.id
                 left join t_parent tp
                           on t.create_user_id = tp.id
                 left join t_teacher tt
                           on t.create_user_id = tt.id
        WHERE T.SCHOOL_ID = #{param.schoolId}
          AND T.STATUS IN (1, 5)
          AND TO_DATE(#{param.queryDate}, 'YYYY-MM-DD') BETWEEN START_DATE AND END_DATE

    </select>


    <select id="getDormsBySchoolId" resultType="com.joinus.thirdparty.model.result.DormInfoResult">
        SELECT f.id         "floorId",
               f.floor_name "floorName",
               l.id         "layerId",
               l.layer_name "layerName",
               d.id         "dormId",
               d.dorm_name  "dormName"
        FROM t_floor f
                 inner join t_layer l
                            on f.id = l.floor_id
                 inner join t_dorm d
                            on l.id = d.layer_id
        where f.school_id = #{schoolId}
          and f.isactive = 1
          and l.isactive = 1
          and d.isactive = 1
    </select>


    <!--  四十四中学生数据同步  -->
    <select id="syncLibraryStudent" resultType="com.joinus.thirdparty.model.po.StudentCardInfoPO">
        select  ts.id "Barcode",
                ts.STUDENT_NAME Name,
                ts.CARDCODE "Tag",
                tg.GRADE_NAME "gradeName",
                tc.CLASS_NAME "className"
        from T_STUDENT ts,T_CLASS tc,T_GRADE tg
            where ts.SCHOOL_ID = 3536
                and ts.CLASS_ID = tc.id
                and tc.GRADE_ID = tg.id
                and ts.ISACTIVE = 1
    </select>

    <!--  四十四中学生增量数据同步  -->
    <select id="incrementSyncLibraryStudent" resultType="com.joinus.thirdparty.model.po.StudentCardInfoPO">
        select  ts.id "Barcode",
                ts.STUDENT_NAME Name,
                ts.CARDCODE "Tag",
                tg.GRADE_NAME "gradeName",
                tc.CLASS_NAME "className"
        from T_STUDENT ts,T_CLASS tc,T_GRADE tg
        where ts.SCHOOL_ID = 3536
          and ts.CLASS_ID = tc.id
          and tc.GRADE_ID = tg.id
          and ts.ISACTIVE = 1
          and ts.UPDATE_TIME > TO_DATE(#{updateTime}, 'YYYY-MM-DD hh24:mi:ss')
    </select>

    <select id="findStudentsByParam" resultType="com.joinus.dao.StudentEntity">
        select t.id "id",
        t.STUDENT_NAME "studentName",
        t.STUDENT_IMG  "studentImg",
        c.class_name "className",
        t.SEX          "sex",
        g.GRADE_NAME "gradeName",
        t.identity "identity",
        t.FACE_FEATURE "faceFeature"
        from T_STUDENT t
        left join T_CLASS c on c.id = t.CLASS_ID
        left join T_GRADE g on g.id = c.GRADE_ID
        where t.school_id = #{param.schoolId}
        and t.isactive = 1
        and t.FACE_FEATURE is not null
        <if test="param.gradeId != null">
            and c.GRADE_ID = #{param.gradeId}
        </if>
        <if test="param.classId != null">
            and t.class_id = #{param.classId}
        </if>
        <if test="param.studentName != null">
            and t.student_name like '%' || #{param.studentName} || '%'
        </if>
        <if test="param.studentImg != null">
            and t.student_img = #{param.studentImg}
        </if>
    </select>

</mapper>