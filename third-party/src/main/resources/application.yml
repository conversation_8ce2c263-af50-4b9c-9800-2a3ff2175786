app:
  id: third-party

apollo:
  bootstrap:
    enabled: true
    namespaces: application,basic.common.database
    eagerLoad:
      enabled: true

spring:
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher #集成swagger后报错
  datasource:
    dynamic:
      primary: oracle
      strict: false
      datasource:
        oracle:
          driver-class-name: oracle.jdbc.driver.OracleDriver
          url: ${jdbc.url}
          username: ijx
          password: ijxuat9671-hwy
  application:
    name: third-party
  redis:
    host: ************
    port: 6379
    password: ijx967111
    database: 0
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB

server:
  port: 9096
  servlet:
    context-path: /api-external

logging:
  level:
    com.joinus.thirdparty.mapper: debug

mybatis-plus:
  global-config:
    db-config:
      insert-strategy:
        not_null
  configuration:
    default-enum-type-handler: com.baomidou.mybatisplus.core.handlers.MybatisEnumTypeHandler

springfox:
  documentation:
    swagger:
      use-model-v3: false
