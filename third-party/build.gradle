plugins {
    id 'java'
    id "org.springframework.boot"
    id "com.palantir.docker" version "0.32.0"
}

group 'com.jounus.basic'
version 'third-party-1.1.29'

ext {
    set('dockerRepo', 'harbor.ijx.icu')
    if (!project.hasProperty('dockerPrefix')) {
        dockerPrefix = 'ijx'
    }
}

repositories {
    mavenCentral()
}

dependencies {
    implementation fileTree(include: ['*.jar'], dir: 'libs')

    testImplementation 'org.junit.jupiter:junit-jupiter-api:5.8.1'
    testRuntimeOnly 'org.junit.jupiter:junit-jupiter-engine:5.8.1'

    implementation project(':common')
    annotationProcessor 'org.projectlombok:lombok'
    implementation 'org.projectlombok:lombok'

    implementation 'com.qiniu:qiniu-java-sdk:7.13.+'

    //百度人脸识别
    implementation('com.baidu.aip:java-sdk:4.16.16'){
        exclude group: 'org.slf4j', module: 'slf4j-api'
        exclude group: 'org.slf4j', module: 'slf4j-simple'
    }
    implementation 'org.json:json:20230618'
    implementation "com.alibaba:easyexcel:3.0.5"
}

test {
    useJUnitPlatform()
}

bootJar {
    launchScript()
    archiveName "snake-third-party.jar"
}

bootRun {
    jvmArgs = ['-Duser.timezone=GMT+8']
}

dockerPrepare.dependsOn(bootJar)
docker {
    name "${dockerRepo}/${dockerPrefix}/snake-third-party"
    tag 'taskLatest', "${dockerRepo}/${dockerPrefix}/snake-third-party:latest"
    tag 'taskVersion', "${dockerRepo}/${dockerPrefix}/snake-third-party:${version}"
    dockerfile file('Dockerfile')
    copySpec.from("build/libs").into("./")
    buildArgs([BUILD_VERSION: 'version'])
}