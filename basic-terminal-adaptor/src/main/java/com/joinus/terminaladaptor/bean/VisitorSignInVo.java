package com.joinus.terminaladaptor.bean;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Description 访问人刷卡记录
 * @date 2025-8-13 16:00:35
 **/
@Data
public class VisitorSignInVo {

    private Long reservationId;

    private Long schoolId;

    private Long parentId;

    private String cardcode;

    private int direction;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date signTime;

    private Float thermometer;

    private Long studentId;

}
