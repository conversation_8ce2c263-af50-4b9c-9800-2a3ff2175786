package com.joinus.terminaladaptor.kafka;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.XmlUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.github.xiaoymin.knife4j.core.util.StrUtil;
import com.joinus.dao.ClassEntity;
import com.joinus.dao.GradeEntity;
import com.joinus.dao.StudentEntity;
import com.joinus.dao.TerminalEntity;
import com.joinus.dao.enums.StudentDormEnum;
import com.joinus.terminaladaptor.bean.IncrementStudentInfo;
import com.joinus.terminaladaptor.bean.CardSignInRecord;
import com.joinus.terminaladaptor.bean.hik.EventMsg;
import com.joinus.terminaladaptor.bean.hik.HikTtsRemoteCheck;
import com.joinus.terminaladaptor.bean.hik.SignInMsg;
import com.joinus.terminaladaptor.bean.unv.UnvCardSignInRecord;
import com.joinus.terminaladaptor.constant.GlobalConstants;
import com.joinus.terminaladaptor.service.*;
import com.joinus.terminaladaptor.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.listener.ConsumerAwareListenerErrorHandler;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;
import org.w3c.dom.Document;
import org.w3c.dom.Element;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Component
@Slf4j
public class KafkaConsumer {
    @Autowired
    private ITerminalService terminalService;
    @Autowired
    private HikVisionService hikVisionService;

    @Autowired
    private HikSwipeService swipeService;
    @Autowired
    private IStudentService studentService;
    @Autowired
    private IClassService classService;

    @Autowired
    private IGradeService gradeService;
    @Autowired
    private RedisUtil redisUtil;


    @Value("#{'${voice.notifications.teacher.grade-class.name.list:教师,教职工}'.split(',')}")
    private List<String> teacherOperationsList;

    @Value("${aio-gateway.domain.name:https://aio-gateway.uat.ijiaxiao.net}")
    private String aioGateWayDomain;
    private static Pattern pattern = Pattern.compile("(<PPVSPMessage>.*?</PPVSPMessage>)", Pattern.DOTALL);
    @KafkaListener(topics = "${kafka.topic.hik-vision.event.record:public.aio-gateway.hik-vision.event.record}",
            containerFactory = "stringKafkaListenerContainerFactory", groupId = "${spring.kafka.consumer.properties.hik.event.record.group-id:basic-terminal-adaptor-hik-event-record}",
            concurrency = "#{${spring.kafka.consumer.aio-gateway.hik-vision.event.record.concurrency:1}}")
    public void receiveSignInRecordFromHikVision(@Payload String msg,
                                                 Acknowledgment ack) {
        String threadName = Thread.currentThread().getName();
        ack.acknowledge();
        long startTime = System.currentTimeMillis();
        String uuid = UUID.randomUUID().toString().replaceAll("-", "");
        log.info(" uuid {} 线程名称{},kafka收到海康刷卡记录数据 #{}#", uuid, threadName, msg);
        Matcher matcher = pattern.matcher(msg);
        if (!matcher.find()) {
            log.info("uuid {} 线程名称 {},未匹配到海康刷卡记录数据", uuid,  threadName);
            return;
        }
        EventMsg eventMsg = null;
        try {
            Document document = XmlUtil.parseXml(matcher.group(1));
            Element ppvspMessage = XmlUtil.getRootElement(document);
            Map<String, Object> xmlMap = XmlUtil.xmlToMap(ppvspMessage);
            //使用hutool将map转为EventMsg对象
            eventMsg = BeanUtil.toBean(xmlMap, EventMsg.class);
            SignInMsg signInMsg = eventMsg.getParams();
            TerminalEntity terminal = terminalService.getTerminalByTerminalNum(signInMsg.getDeviceID());
            if (terminal == null) {
                return;
            }
            Integer signType = null;
            log.info("uuid {} 线程名称 {},刷卡记录数据类型 signType {} majorType {} minorType {}", uuid, threadName, signType, signInMsg.getMajorType(),
                    signInMsg.getMinorType());
            if (signInMsg.getMajorType() == 5 && signInMsg.getMinorType() == 75) {//人脸认证通过
                signType = GlobalConstants.SIGN_TYPE.FACE;
            } else if (signInMsg.getMajorType() == 5 && signInMsg.getMinorType() == 105) {
                signType = GlobalConstants.SIGN_TYPE.FACE;
            } else if (signInMsg.getMajorType() == 5 && signInMsg.getMinorType() == 76) {//人脸认证失败
                signType = GlobalConstants.SIGN_TYPE.FACE;
            } else if (signInMsg.getMajorType() == 5 && signInMsg.getMinorType() == 1) {//合法卡认证通过
                signType = GlobalConstants.SIGN_TYPE.CARD;
            } else if (signInMsg.getMajorType() == 5 && signInMsg.getMinorType() == 9) { //无此卡号
                signType = GlobalConstants.SIGN_TYPE.CARD;
            } else if (signInMsg.getMajorType() == 2 && signInMsg.getMinorType() == 1024) {//设备上电
                hikVisionService.checkSNforHik(signInMsg.getDeviceID());
                hikVisionService.configVerificationMode(signInMsg.getDeviceID(), terminal.getSchoolId());
                hikVisionService.updateIpAddressAndSoftVersionForHik(signInMsg.getDeviceID());
                log.info("uuid {} 线程名称 {},kafka收到海康设备上电置数据 #{}#", uuid, threadName, msg);
            } else if (signInMsg.getMajorType() == 2 && signInMsg.getMinorType() == 1031) {//设备网络恢复
                hikVisionService.checkSNforHik(signInMsg.getDeviceID());
                hikVisionService.configVerificationMode(signInMsg.getDeviceID(), terminal.getSchoolId());
                hikVisionService.updateIpAddressAndSoftVersionForHik(signInMsg.getDeviceID());
                log.info("uuid {} 线程名称 {},kafka收到海康设备网络恢复 #{}#", uuid,  threadName, msg);
            } else if (signInMsg.getMajorType() == 3 && signInMsg.getMinorType() == 1038) {//服务配置成功后在线数据上传
                hikVisionService.checkSNforHik(signInMsg.getDeviceID());
                hikVisionService.configVerificationMode(signInMsg.getDeviceID(), terminal.getSchoolId());
                log.info("uuid {} 线程名称 {},kafka收到海康设备修改中心组参数配置数据 #{}#", uuid,  threadName, msg);
            }
            if (null != signType) {
                if (ObjectUtil.isNotNull(signInMsg) ) {
                    //如果是学生记录
                    if (StrUtil.isBlank(signInMsg.getEmployeeNoString()) || signInMsg.getEmployeeNoString().length() < 12) {
                        log.info(" uuid {} 线程名称 {},开始处理刷卡记录数据 {}", uuid, threadName, JSONUtil.toJsonStr(signInMsg));
                        CardSignInRecord cardSignInRecord = swipeService.buildCardSignInRecordFromHik(signInMsg, terminal, signType);
                        if (null != cardSignInRecord && StringUtils.isNotBlank(cardSignInRecord.getCardNum())) {
                            log.info(" uuid {} 线程名称 {},开始保存刷卡记录数据 {}", uuid, threadName, JSONUtil.toJsonStr(cardSignInRecord));
                            swipeService.saveAccessRecord(cardSignInRecord, terminal);
                        }
                    }else {
                        // 推送访校预约刷脸记录
                        swipeService.pushReserveSwipe(signInMsg.getEmployeeNoString(), terminal);

                    }
                }
            }
        } catch (Exception e) {
            log.error("uuid {} 线程名称 {},处理海康刷卡记录数据失败", uuid,  threadName, e);
        }finally {
            log.info("uuid {} 线程名称 {},kafka处理海康刷卡记录数据  ,耗时 {}ms  结束 #{}#", uuid,  threadName, System.currentTimeMillis() - startTime, msg);
        }


    }


    @KafkaListener(topics = "${kafka.topic.terminal-adaptor.increment.handle.student-info:public.terminal-adaptor.increment.handle.student-info}",
            containerFactory = "incrementStudentInfoListenerContainerFactory", groupId = "${spring.kafka.consumer.properties.group-id}",
            concurrency = "#{${spring.kafka.consumer.terminal-adaptor.increment.handle.student-info.concurrency:1}}")
    public void incrementHandleStudentInfo(@Payload String msg, ConsumerRecord<?, ?> record,
                                           Acknowledgment ack) throws InterruptedException {
        String threadName = Thread.currentThread().getName();
        ack.acknowledge();
        long startTime = System.currentTimeMillis();
        String uuid = UUID.randomUUID().toString().replaceAll("-", "");
        log.info("uuid {} 线程名称 {},kafka收到学生增量信息数据 #{}#",uuid, threadName, msg);
        try{
            // 最大等待时间
            long maxWaitSeconds = 30;
            IncrementStudentInfo incrementStudentInfo = JSONUtil.toBean(msg, IncrementStudentInfo.class);
            String key = String.format(GlobalConstants.REDIS_KEY_BUSINESS_PHONES, incrementStudentInfo.getStudentId());
            while (!redisUtil.tryLock(key, Convert.toStr(incrementStudentInfo.getStudentId()), GlobalConstants.REDIS_INTERVAL_SECONDS)) {
                if ((System.currentTimeMillis() - startTime) > maxWaitSeconds * 1000) {
                    log.warn("uuid {} 线程名称 {},获取锁超时,学生增量信息:{}", uuid, threadName, incrementStudentInfo);
                    return;
                }
                TimeUnit.SECONDS.sleep(1);
            }
            studentService.pushStudentIncrement(incrementStudentInfo, uuid);
            redisUtil.del(key);
        }catch (Exception e){
            log.warn("uuid {} 线程名称 {},kafka收到学生增量信息数据, 学生信息#{}#, 处理出错#{}#",  uuid, threadName, msg, e.getMessage(), e);
        }

        log.info("uuid {} 线程名称 {},kafka收到学生增量信息数据  ,耗时 {}ms  结束  #{}#",  uuid, threadName,  System.currentTimeMillis() - startTime, msg);
    }

    /*@KafkaListener(topics = "${kafka.topic.uniview.play.voice:private.aio-gateway.uniview.play.voice}",
            containerFactory = "kafkaManualAckListenerContainerFactory", groupId = "${spring.kafka.consumer.properties.unv.play.voice.group-id:basic-terminal-adaptor-unv-play-voice}",
            concurrency = "#{${spring.kafka.consumer.private.aio-gateway.uniview.play.voice.concurrency:1}}")
    public void receivePlayVoice(@Payload String msg, ConsumerRecord<?, ?> record,
                                           Acknowledgment ack) {
        ack.acknowledge();
        String threadName = Thread.currentThread().getName();
        long startTime = System.currentTimeMillis();
        log.info("线程名称:{},kafka收到宇视学生刷脸数据 #{}#", threadName, msg);
        try {
            JSONObject jsonObject = JSONUtil.parseObj(msg);
            long nowTime = System.currentTimeMillis();
            UnvCardSignInRecord unvCardSignInRecord = JSONUtil.toBean(msg, UnvCardSignInRecord.class);
            String uuid = unvCardSignInRecord.getUuid();
            Long timestamp = unvCardSignInRecord.getTimestamp();
            String imei = unvCardSignInRecord.getImei();
            String cardCode = unvCardSignInRecord.getCardNum();
            Long schoolId = unvCardSignInRecord.getSchoolId();
            log.info("uuid:{}, imei:{}, kafka消费者接收到时间:{},发送时间为:{},时间差为:{}", uuid, imei, nowTime, timestamp, (nowTime - timestamp));
            if (nowTime - timestamp > 2000) {
                log.warn("uuid:{}, imei:{}, kafka接收超时,暂不处理,不处理刷卡信息:{}", uuid, imei, jsonObject);
                return;
            }
           
            StudentEntity student = studentService.getStudentInfoByCardCodeAndSchoolId(cardCode, schoolId);
            log.info("uuid:{},imei:{},查询到学生信息:{}", uuid, imei, student);
            if (student == null) {
                return;
            }
            JSONObject voiceInfo = new JSONObject();
            JSONObject showTextInfo = new JSONObject();
            showTextInfo.put("ResultCode", 1);
            showTextInfo.put("ResultMsg", "成功");
            showTextInfo.put("PassTime", StrUtil.isNotBlank(unvCardSignInRecord.getCardTime()) ? unvCardSignInRecord.getCardTime() : DateUtil.format(new Date(), "yyyyMMddHHmmss"));
            showTextInfo.put("ShowInfoNum", 1);
            List<Map<String, String>> showInfoList = new ArrayList<>();
            Map<String, String> map = new HashMap<>();
            map.put("Value", "");
            String playVoice = swipeService.checkStudentLeave(student, new HikTtsRemoteCheck());
            if (StrUtil.isNotBlank(playVoice)) {
                voiceInfo.put("VoiceType", GlobalConstants.UNV_VOICE_TYPE.LEAVE_STUDENT);
                map.put("Key", student.getStudentName() + ":请假");
                log.info("uuid:{},imei:{}, 请假语音播报", uuid, imei);
            } else {
                ClassEntity classInfo = classService.getClassInfo(student.getClassId(), student.getSchoolId());
                GradeEntity gradeInfo = gradeService.getGradeInfo(classInfo.getGradeId(), student.getSchoolId());
                if (classInfo.isTeacher(teacherOperationsList) ||gradeInfo.isTeacher(teacherOperationsList)) {
                    log.info("uuid:{},教师 教职工语音播报,StudentId:" + student.getId(), uuid);
                    voiceInfo.put("VoiceType", GlobalConstants.UNV_VOICE_TYPE.TEACHER);
                    map.put("Key", student.getStudentName() + ":老师好");
                } else {
                    log.info("uuid:{},imei:{},走读生 住宿生语音播报,StudentId:{}", uuid, student.getId(), imei);
                    //播放走读生还是住宿生
                    if (StudentDormEnum.DORM.equals(student.getIsDorm())) {
                        voiceInfo.put("VoiceType", GlobalConstants.UNV_VOICE_TYPE.DORM_STUDENT);
                        map.put("Key", student.getStudentName() + ":住宿生");
                    } else {
                        voiceInfo.put("VoiceType", GlobalConstants.UNV_VOICE_TYPE.HOME_STUDENT);
                        map.put("Key", student.getStudentName() + ":走读生");
                    }
                }

            }
            showInfoList.add(map);
            showTextInfo.put("ShowInfoList", showInfoList);
            String url = aioGateWayDomain + "/unv/terminal/play/voice";
            JSONObject requestBody = new JSONObject();
            requestBody.put("voiceInfo", voiceInfo);
            requestBody.put("showTextInfo", showTextInfo);
            requestBody.put("imei", imei);
            requestBody.put("uuid", uuid);
            try {
                HttpUtil.post(url, requestBody.toString(), 3000);
            } catch (Exception e) {
                log.warn("uuid:{}, imei:{}, 语音播报请求aio-gateway失败: {}", uuid, imei, e.getMessage());
            }
            log.info("uuid:{},imei:{},语音播报执行时间:{} ms", uuid, imei, System.currentTimeMillis() - startTime);
        } catch (Exception e) {
            log.warn("线程名称:{},kafka收到宇视学生刷脸数据, 学生信息#{}#, 处理出错#{}#", threadName, msg, e.getMessage(), e);
        }

        log.info("线程名称:{},kafka收到宇视学生刷脸数据 结束 #{}#", threadName, msg);
    }*/

    @Bean
    public ConsumerAwareListenerErrorHandler consumerAwareErrorHandler() {
        return (message, exception, consumer) -> {
            log.error("kafka消费异常:{}", message.getPayload());
            return null;
        };
    }
}
