plugins {
    id 'java'
    id "org.springframework.boot"
    id "com.palantir.docker" version "0.32.0"
}
group 'basic-terminal-adaptor'
version 'basic-terminal-adaptor-1.0.15'

repositories {
    mavenCentral()
}

ext {
    set('dockerRepo', 'harbor.ijx.icu')
    if (!project.hasProperty('dockerPrefix')) {
        dockerPrefix = 'ijx'
    }
}

dependencies {
    implementation project(':common')
    implementation "com.alibaba.fastjson2:fastjson2:2.0.39"
    implementation 'com.qiniu:qiniu-java-sdk:7.7.0'
    annotationProcessor 'org.projectlombok:lombok'
    implementation 'org.projectlombok:lombok'
    testImplementation 'org.junit.jupiter:junit-jupiter-api:5.7.0'
    testRuntimeOnly 'org.junit.jupiter:junit-jupiter-engine:5.7.0'
    implementation 'com.github.xiaoymin:knife4j-spring-boot-starter:3.0.3'
    implementation 'io.opentelemetry.instrumentation:opentelemetry-instrumentation-annotations:1.26.0'
}

bootJar {
    launchScript()
    archiveName "basic-terminal-adaptor.jar"
}
docker {
    name "${dockerRepo}/${dockerPrefix}/basic-terminal-adaptor"
    tag 'taskLatest', "${dockerRepo}/${dockerPrefix}/basic-terminal-adaptor:latest"
    tag 'taskVersion', "${dockerRepo}/${dockerPrefix}/basic-terminal-adaptor:${version}"
    dockerfile file('Dockerfile')
    copySpec.from("build/libs").into("./")
    buildArgs([BUILD_VERSION: 'version'])
}
dockerPrepare.dependsOn(bootJar)