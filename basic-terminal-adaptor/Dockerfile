FROM registry.cn-beijing.aliyuncs.com/ijx-public/opentelemetry-javaagent:1.26.0 as opentelemetry
FROM ijx-registry.cn-beijing.cr.aliyuncs.com/ijx-public/openjdk11:alpine-jre
RUN apk add tzdata && cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime \
    && echo "Asia/Shanghai" > /etc/timezone \
    && apk del tzdata
VOLUME /tmp
COPY basic-terminal-adaptor.jar basic-terminal-adaptor.jar
COPY --from=opentelemetry / /opentelemetry
CMD ["java","-Djava.security.egd=file:/dev/./urandom","-jar","/basic-terminal-adaptor.jar"]
