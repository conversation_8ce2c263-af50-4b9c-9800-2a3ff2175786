package com.joinus.wx.videophone.model.param.message;

import com.joinus.common.model.enums.messageboard.MessageBoardSenderTypeEnum;
import com.joinus.wx.videophone.model.enums.MessageBoardMessageTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 发送留言参数
 *
 * <AUTHOR> anpy
 * @create 2023/6/30 14:18
 */
@Data
public class SendMessageBoardParam {

    @NotBlank(message = "学生ID不能为空")
    @ApiModelProperty(value = "学生ID", required = true)
    private Long studentId;
    @NotBlank(message = "家长ID不能为空")
    @ApiModelProperty(value = "家长ID", required = true)
    private Long parentId;
    @NotBlank(message = "学校ID不能为空")
    @ApiModelProperty(value = "学校ID", required = true)
    private Long schoolId;

    @ApiModelProperty(value = "手机号码(学生发送留言时传)")
    private String phone;
    @ApiModelProperty(value = "文本消息,messageTypeEnum值为TXT_TYPE时传", example = "你好")
    private String message;
    @ApiModelProperty(value = "语音url,messageTypeEnum值为MEDIA_TYPE时传", example = "www.xxx.com")
    private String mediaUrl;
    @ApiModelProperty(value = "留言板消息类型", required = true, notes = "com.joinus.wx.videophone.model.enums.MessageBoardMessageTypeEnum", example = "TXT_TYPE")
    private MessageBoardMessageTypeEnum messageTypeEnum;
    @ApiModelProperty(value = "发送方类型", required = true, notes = "com.joinus.common.model.enums.messageboard.MessageBoardSenderTypeEnum", example = "PARENT")
    private MessageBoardSenderTypeEnum senderTypeEnum;

    @ApiModelProperty(value = "语音时长,messageTypeEnum值为MEDIA_TYPE时传", example = "10")
    private Integer mediaDuration = 0;

}
