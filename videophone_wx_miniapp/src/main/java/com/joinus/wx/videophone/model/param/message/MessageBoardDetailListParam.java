package com.joinus.wx.videophone.model.param.message;

import com.joinus.common.model.enums.messageboard.MessageBoardSenderTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 留言相亲个列表
 *
 * <AUTHOR> anpy
 * @create 2023/6/30 14:18
 */
@Data
public class MessageBoardDetailListParam {

    @NotBlank(message = "学生ID不能为空")
    @ApiModelProperty(value = "学生ID", required = true)
    private Long studentId;
    @NotBlank(message = "家长ID不能为空")
    @ApiModelProperty(value = "家长ID", required = true)
    private Long parentId;

    @NotBlank(message = "senderType")
    @ApiModelProperty(value = "发送方身份", required = true)
    private MessageBoardSenderTypeEnum senderType;

}
