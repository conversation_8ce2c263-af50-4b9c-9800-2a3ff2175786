package com.joinus.wx.videophone.model.enums;

import com.joinus.dao.enums.SwaggerDisplayEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 留言板消息类型
 *
 * <AUTHOR> anpy
 * @create 2023/6/28 9:04
 */
@Getter
@AllArgsConstructor
public enum ParentSignStatusEnum implements SwaggerDisplayEnum {
    /**
     * 未授权设备组
     */
    NO_SIGN(0, "未授权设备组"),
    /**
     * 已授权设备组
     */
    SIGN(1, "已授权组");

    private final int status;
    private final String typeName;

    @Override
    public String description() {
        return status + "-" + typeName + "-使用值:" + name();
    }
}
