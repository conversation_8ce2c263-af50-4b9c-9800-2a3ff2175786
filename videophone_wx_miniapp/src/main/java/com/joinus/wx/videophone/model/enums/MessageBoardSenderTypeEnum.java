package com.joinus.wx.videophone.model.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.joinus.dao.enums.SwaggerDisplayEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 留言板发送方类型
 *
 * <AUTHOR> anpy
 * @create 2023/6/28 9:04
 */
@Getter
@AllArgsConstructor
public enum MessageBoardSenderTypeEnum implements SwaggerDisplayEnum {
    /**
     * 学生发送
     */
    STUDENT(0, "学生发送"),
    /**
     * 家长发送
     */
    PARENT(1, "家长发送");
    @EnumValue
    private final int type;
    private final String typeName;

    @Override
    public String description() {
        return type + "-" + typeName + "-使用值:" + name();
    }
}
