package com.joinus.wx.videophone.model.dto;

import com.joinus.wx.videophone.model.enums.ParentSignStatusEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
* 家长信息
* <AUTHOR> anpy
* @create 2023/7/4 16:18
*/
@Data
public class ParentDTO {

    @ApiModelProperty(value = "家长id")
    private Long parentId;
    @ApiModelProperty(value = "家长姓名")
    private String parentName;
    @ApiModelProperty(value = "家长与孩子关系")
    private String childRelation;
    private String telNum;
    @ApiModelProperty(value = "授权状态",notes = "com.joinus.wx.videophone.model.enums.ParentSignStatusEnum", example = "SIGN")
    private ParentSignStatusEnum signStatus;

}
