package com.joinus.youkate.model.bo;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@Builder
public class OfflineRecordBo implements Serializable {

    private String deviceNumber;
    /*
     * 3预约扣费  5加款模式
     */
    private Integer operateMode;
    private String cardCodeHex;
    private String cardCode;
    private String consumeTimeStr;
    private Date consumeDate;
    private Integer balance;
    private Integer consumeAmount;
    private Integer leftCount;
    private Integer consumeCount;

    private String checkDuplicateKey;
}
