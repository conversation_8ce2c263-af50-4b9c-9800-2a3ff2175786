package com.joinus.youkate.model.dto;

import com.joinus.dao.TerminalYoukateEntity;
import com.joinus.youkate.model.enums.YouKaTeDeviceTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @projectName: snake-basic-business
 * @package: com.joinus.youkate.model.dto
 * @className: SchoolInfoDto
 * @description: TODO
 * @author: wangtianbao
 * @date: 2023/7/6 01:56
 */
@Data
@EqualsAndHashCode(callSuper=false)
public class DeviceInfoDto extends TerminalYoukateEntity {

    private String url;

    private String udpServerUrl;

    private Integer udpServerPort;

    private YouKaTeDeviceTypeEnum deviceType;

    public YouKaTeDeviceTypeEnum getDeviceType() {
        return YouKaTeDeviceTypeEnum.fromTableType(this.getType());
    }
}
