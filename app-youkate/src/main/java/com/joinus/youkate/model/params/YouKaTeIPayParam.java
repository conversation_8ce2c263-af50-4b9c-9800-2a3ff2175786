package com.joinus.youkate.model.params;

import lombok.Builder;
import lombok.Data;

@Builder
@Data
public class YouKaTeIPayParam {

    /**
     *  爱家校平台分配的client_id
     */
    private String clientId;
    /**
     * 时间戳，当前请求时间，格式为：yyyy-MM-dd HH:mm:ss
     */
    private String timeStamp;
    /**
     * 支付请求订单号，需保持唯一
     */
    private String norceStr;
    private String sign;
    private String schoolId;
    /**
     * 学生身份标识
     */
    private String identity;
    /**
     * 支付请求订单号，需保持唯一
     */
    private String orderNo;
    /**
     * 金额（元）
     */
    private String amount;
    /**
     * 设备类型： 10. 洗衣机 11.吹风机
     */
    private String deviceType;

}
