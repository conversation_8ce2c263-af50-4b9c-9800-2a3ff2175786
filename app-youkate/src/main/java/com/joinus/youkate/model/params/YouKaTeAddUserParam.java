package com.joinus.youkate.model.params;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class YouKaTeAddUserParam {

    @JsonProperty("user_number")
    private String userNumber;
    @JsonProperty("user_name")
    private String userName;
    @JsonProperty("user_phone")
    private String userPhone;

    private String department;
    private String position;
    @JsonProperty("user_card")
    private String userCard;
    @JsonProperty("user_mailbox")
    private String userMailbox;
    @JsonProperty("face_images")
    private String faceImages;
    private String identityId;
    private Long studentId;

}
