package com.joinus.common.utils;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.connection.RedisStringCommands;
import org.springframework.data.redis.connection.ReturnType;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.data.redis.core.types.Expiration;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.nio.charset.Charset;
import java.util.Arrays;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class RedisUtils {

    private static RedisTemplate<String, Object> redisTemplate;
    private static final String UNLOCK_LUA = "if redis.call('get', KEYS[1]) == ARGV[1] then return redis.call('del', KEYS[1]) else return 0 end";

    @Resource
    public void setRedisTemplate(RedisTemplate<String, Object> redisTemplate) {
        RedisUtils.redisTemplate = redisTemplate;
    }

    public static void expires(String key, long expires) {
        redisTemplate.expire(key, expires, TimeUnit.SECONDS);
    }

    public static boolean hasKey(String key) {
        Boolean hasKey = redisTemplate.hasKey(key);
        if (hasKey == null) {
            return false;
        }
        return hasKey;
    }

    public static void delete(String... keys) {
        if (keys == null || keys.length <= 0) {
            return;
        }

        Arrays.stream(keys)
                .forEach(key -> {
                    if (hasKey(key)) {
                        redisTemplate.delete(key);
                    }
                });
    }

    public static Object get(String key) {
        if (StrUtil.isEmpty(key) || !hasKey(key)) {
            return null;
        }

        return redisTemplate.opsForValue().get(key);
    }

    public static void set(String key, Object value) {
        redisTemplate.opsForValue().set(key, value);
    }

    public static void set(String key, Object value, long expires) {
        if (expires > 0) {
            redisTemplate.opsForValue().set(key, value, expires, TimeUnit.SECONDS);
        } else {
            set(key, value);
        }
    }

    public static void convertAndSend(String channel, Object message) {
        redisTemplate.convertAndSend(channel, message);
    }

    public static ValueOperations<String, Object> opsForValue() {
        return redisTemplate.opsForValue();
    }

    public static boolean tryGetDistributedLock(String lockKey, String requestId, long expire) {
        try {
            RedisCallback<Boolean> redisCallback = (connection) -> connection.set(lockKey.getBytes(Charset.forName("UTF-8")),
                    requestId.getBytes(Charset.forName("UTF-8")),
                    Expiration.milliseconds(expire),
                    RedisStringCommands.SetOption.SET_IF_ABSENT);
            return redisTemplate.execute(redisCallback);
        } catch (Exception e) {
            log.error("redis lock error.", e);
        }
        return false;
    }

    public static boolean releaseDistributedLock(String lockKey, String requestId) {
        RedisCallback<Boolean> redisCallback = (connection) -> connection.eval(UNLOCK_LUA.getBytes(),
                ReturnType.BOOLEAN,
                1,
                lockKey.getBytes(Charset.forName("UTF-8")),
                requestId.getBytes(Charset.forName("UTF-8")));
        return redisTemplate.execute(redisCallback);
    }

}
