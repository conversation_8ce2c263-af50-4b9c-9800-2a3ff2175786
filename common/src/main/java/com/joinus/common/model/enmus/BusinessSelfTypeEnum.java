package com.joinus.common.model.enmus;

/**
* 自推广业务表业务类型
* <AUTHOR> anpy
* @create 2023/6/26 10:14
*/
public enum BusinessSelfTypeEnum {
    /**
     * 基础业务
     */
    BASIC_BUSINESS(0,"基础业务"),
    /**
     * 乘车安
     */
    BUS(2,"乘车安"),
    /**
     * 微信可视电话
     */
    WX_VIDEOPHONE(3,"微信可视电话");


    private final Integer businessType;
    private String businessTypeName;

    BusinessSelfTypeEnum(Integer businessType, String businessTypeName) {
        this.businessType = businessType;
        this.businessTypeName = businessTypeName;
    }

    public Integer getBusinessType() {
        return this.businessType;
    }
}
