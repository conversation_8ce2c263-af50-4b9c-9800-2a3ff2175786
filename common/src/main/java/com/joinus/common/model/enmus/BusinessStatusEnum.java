package com.joinus.common.model.enmus;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
* 基础业务开通状态
* <AUTHOR> anpy
* @create 2023/11/27 10:14
*/
@Getter
@AllArgsConstructor
public enum BusinessStatusEnum {
    /**
     * 河南业务开通
     */
    HN_FORMAL_OPEN(1,"河南业务开通"),
    /** 河南使用开通*/
    HN_TRY_OPEN(2,"河南使用开通"),
    /**
     * 非河南移动开通
     */
    UNGO_OPEN(4,"非河南移动开通"),
    /**
     * 河北业务开通
     */
    HB_FORMAL_OPEN(6,"河北业务开通");


    private final Integer businessType;
    private String businessTypeName;

}
