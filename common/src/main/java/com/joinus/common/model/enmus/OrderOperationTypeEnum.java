package com.joinus.common.model.enmus;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
* 三方对接项目订单操作类型
* <AUTHOR> anpy
* @create 2023/6/26 10:14
*/
@Getter
@AllArgsConstructor
public enum OrderOperationTypeEnum {
    /**
     * 购买
     */
    BUY(1,"购买"),
    /**
     * 退款
     */
    REFUND(2,"退款");

    private final Integer operationType;
    private final String operationTypeName;


}
