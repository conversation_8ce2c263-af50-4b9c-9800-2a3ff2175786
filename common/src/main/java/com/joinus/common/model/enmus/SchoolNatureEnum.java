package com.joinus.common.model.enmus;

import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * 学校性质枚举类
 * @Author: zxr
 * @Date: 2020/12/7 14:33
 */
@Getter
public enum SchoolNatureEnum {

    /**
     * 校讯通转型学校
     */
    XIAO_XUN_TONG(1, "xiaoXunTong","校讯通转型学校", "如果此学校是校讯通转型的学校，需要勾选此项。只有勾选此项的学校，“班主任发送统计”功能才会统计此学校的数据"),
    ONE_CARD(2, "oneCard", "一卡通学校", null),
    FACE_RECOGNITION(4, "faceRecognition", "人脸识别方式进出", "如果此学校通过刷脸进出校或宿舍，需要勾选此项。如果不勾选，学生照片发送变化将不会同步到设备中。"),
    OTHER(8, "other", "自推广业务", "学校发展的有非移动的业务，不勾选时开业务的学生无法打电话。"),
    /**
     * 讯达安学校
     */
    XUN_DA_AN(16, "xunDaAN", "讯达安学校", "讯达安公司发展的学校需要勾选此项。用于区分可视电话、弹照片系统的前端展示样式"),
    HALF_PAST_THREE_SCHOOL(128,"halfPastThreeSchool", "课后三点半", null),
    YIDU_REFERRAL(32, "YIDU_REFERRAL", "益读引流", "勾选此项的学校，会在家长登录青于蓝app后，弹出免费领取益读借阅兑换码的弹框。"),
    HAND_AUDIT(256, "handAudit", "本校人员审核(人工)", "用于有闸机的学校，照片需要人工审核是否为本校的学生后，才进行设备的人脸是否符合要求的校验；不勾选时仅进行后者的校验"),
    PLAGUE_MONITOR(512, "plagueMonitor", "健康状态监测学校", "勾选此项的学校，支持健康状态展示（健康码、核酸、疫苗接种情况）"),
    NOT_MOBILE_COOPERATE(1024, "notMobileCooperate", "非移动运营商合作学校", "仅在白名单的手机号方可通过迎新录入的学校"),
    STUDENT_RISK_ALERT(2048, "studentRiskAlert", "学情预警学校", "根据学生行为计算风险等级通知家长和老师"),
    CLOUD_DIAN_MING(4096, "cloudDianMing", "电子点名学校", "教育局端平台根据此项计算点名学校的相关数据"),
    JIA_YIN_TONG(8192, "jiaYinTong", "家音通学校", "勾选此项时，老师收到的缺勤上报反馈信息链接指向“家音通小程序”"),
    SUNSHINE_CATERING(16384, "sunshineCatering", "阳光餐饮", null);

    /** code */
    private int code;

    /** 显示标签 */
    private String label;

    /** 名称 */
    private String name;

    /** 描述 */
    private String description;

    SchoolNatureEnum(int code, String label, String name, String description){
        this.code = code;
        this.label = label;
        this.name = name;
        this.description = description;
    }

    /**
     * 获取code的文本
     * @param code
     * @return
     */
    public static String getLabelByCode (int code){

        for (SchoolNatureEnum schoolNatureEnum : SchoolNatureEnum.values()){
            if (schoolNatureEnum.getCode() == code){
                return schoolNatureEnum.getLabel();
            }
        }
        return "未知枚举项";
    }

    /**
     * 获取code的名称
     * @param code
     * @return
     */
    public static String getNameByCode (int code){

        for (SchoolNatureEnum schoolNatureEnum : SchoolNatureEnum.values()){
            if (schoolNatureEnum.getCode() == code){
                return schoolNatureEnum.getName();
            }
        }
        return "未知枚举项";
    }

    /**
     * 根据文本获取code
     */
    public static int getCodeByLabel(String label){

        for(SchoolNatureEnum schoolNatureEnum : SchoolNatureEnum.values()){
            if(schoolNatureEnum.getLabel().equals(label)){
                return schoolNatureEnum.getCode();
            }
        }
        return -1;

    }

    /**
     * 根据code获取枚举
     */
    public static SchoolNatureEnum getSchoolNatureByCode(int code){

        for(SchoolNatureEnum schoolNatureEnum : SchoolNatureEnum.values()){
            if(schoolNatureEnum.getCode() == code){
                return schoolNatureEnum;
            }
        }
        return null;

    }

    public static List<SchoolNatureEnum> listSchoolNature(Integer schoolNature) {
        List<SchoolNatureEnum> schoolNatureEnumList = new ArrayList<>();
        for(SchoolNatureEnum schoolNatureEnum : SchoolNatureEnum.values()){
            if((schoolNatureEnum.getCode() & schoolNature) > 0){
                schoolNatureEnumList.add(schoolNatureEnum);
            }
        }
        return schoolNatureEnumList;
    }

    public String getStringCode(){
        return String.valueOf(this.code);
    }
}
