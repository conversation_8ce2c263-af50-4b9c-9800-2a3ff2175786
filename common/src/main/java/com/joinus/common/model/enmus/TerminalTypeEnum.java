package com.joinus.common.model.enmus;

import lombok.Getter;

/**
 * @description :  * sys_dict表dict_id = 26 / 5
 * <AUTHOR> anpengyu
 * @create 2024/10/15 8:54
 */
@Getter
public enum TerminalTypeEnum {
    IN_SCHOOL_TERMINAL(79L, "入校终端"),
    DOOR_GRPS_COMPUTER_TERMINAL(80L, "门卫GPRS电脑终端"),
    MEETING_TERMINAL(81L, "会议终端"),
    NO_SMS_DORM_TERMINAL(82L, "无短信宿舍终端"),
    GIVE_LESSONS_TRACKING_TERMINAL(83L, "授课跟踪"),
    NEW_PHONE_terminal(84L, "新电话终端"),
    ATTENDANCE_TERMINAL(85L, "考勤终端"),
    CLASS_TERMINAL(86L, "班级终端"),
    PHONE_TERMINAL(87L, "电话终端"),
    SCHOOL_BUS_TERMINAL(88L, "校车终端"),
    GPRS_DOOR_TERMINAL(89L, "GPRS门卫终端"),
    VIDEOPHONE(264L,"可视话机"),
    CABINET(267L, "云校柜"),
    NO_UPPER_COMPUTER_MACHINE(268L, "无上位机一体机"),
    CABINET_DOUBLE_SIDED(270L, "云校柜双面柜"),
    FACE_tERMINAL(281L, "人脸识别设备"),
    UPPER_COMPUTER_TYPE_ID(287L, "上位机"),
    PREVENT_BULLYING_TYPE_ID(302L,"AI防欺凌"),
    CONSUMER_MACHINE_TYPE_ID(307L,"消费终端");

    private final Long terminalType;
    private final String desc;

    TerminalTypeEnum(Long terminalType, String desc) {
        this.terminalType = terminalType;
        this.desc = desc;
    }

    public static String getDesc(Long terminalType) {
        for (TerminalTypeEnum terminalTypeEnum : TerminalTypeEnum.values()) {
            if (terminalTypeEnum.terminalType.equals(terminalType)) {
                return terminalTypeEnum.desc;
            }
        }
        return null;
    }

}
