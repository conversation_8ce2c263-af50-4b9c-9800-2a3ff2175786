package com.joinus.common.model.enmus;

import com.baomidou.mybatisplus.annotation.EnumValue;

/**
 * <AUTHOR>
 * @return
 * @description 学生及家长健康结果
 * @date 2022/3/29 4:17 PM
 */
public enum PlagueHealthEnum {

    IDENTITY_RRROR(-1, "身份信息异常"),
    HEALTH_NONE(0, "没有健康信息"),
    NORMAL(1, "健康"),
    HEALTH_ERROR(2, "结果异常");

    @EnumValue
    private int code;

    private String label;

    PlagueHealthEnum(int code, String label) {
        this.code = code;
        this.label = label;
    }

    public static String getLabelByCode(Integer code) {
        if (null == code) {
            return "未知枚举项";
        }
        for (PlagueHealthEnum SchoolStateEnum : PlagueHealthEnum.values()) {
            if (SchoolStateEnum.getCode() == code) {
                return SchoolStateEnum.getLabel();
            }
        }
        return "未知枚举项";
    }

    /**
     * 根据stateTitle获取stateType
     */
    public static int getCodeByLabel(String code) {

        for (PlagueHealthEnum SchoolStateEnum : PlagueHealthEnum.values()) {
            if (SchoolStateEnum.getLabel().equals(code)) {
                return SchoolStateEnum.getCode();
            }
        }
        return -1;

    }

    public static String fromType(String name) {
        for (PlagueHealthEnum plagueHealthEnum : PlagueHealthEnum.values()) {
            if (plagueHealthEnum.name().equals(name)) {
                return plagueHealthEnum.label;
            }
        }
        return null;
    }

    public int getCode() {
        return code;
    }

    public String getLabel() {
        return label;
    }
}
